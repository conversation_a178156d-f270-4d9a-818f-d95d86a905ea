import requests
from bs4 import BeautifulSoup
import tkinter as tk
from tkinter import messagebox
from PIL import Image, ImageTk
import io
import random
import time

class LoginApp:
    def __init__(self, root):
        self.root = root
        self.root.title("自动登录界面")
        self.session = requests.Session()
        
        # 页面组件
        tk.Label(root, text="账号:").pack()
        self.username_entry = tk.Entry(root)
        self.username_entry.pack(pady=5)
        
        tk.Label(root, text="密码:").pack()
        self.password_entry = tk.Entry(root, show="*")
        self.password_entry.pack(pady=5)
        
        tk.Label(root, text="验证码:").pack()
        self.verify_code = tk.Entry(root)
        self.verify_code.pack(pady=5)
        
        # 验证码刷新按钮
        self.refresh_btn = tk.Button(root, text="刷新验证码", command=self.refresh_verification_code)
        self.refresh_btn.pack(pady=5)
        
        # 登录按钮
        self.login_btn = tk.But<PERSON>(root, text="立即登录", command=self.login)
        self.login_btn.pack(pady=10)
        
        # 验证码图片展示区
        self.verify_image_label = tk.Label(root)
        self.verify_image_label.pack()
        
        # 初始化加载验证码
        self.verify_url = "https://jk7859.mcu25805gtwqekeyw.com:59789/loginVerifycode.do"
        self.refresh_verification_code()

    def refresh_verification_code(self):
        try:
            timestamp = int(time.time()*1000)
            params = {"timestamp": timestamp}
            headers = {
                "User-Agent": "Mozilla/5.0",
                "X-Requested-With": "XMLHttpRequest"
            }
            
            response = self.session.get(self.verify_url, params=params, headers=headers, verify=False)
            if response.status_code == 200:
                image_data = io.BytesIO(response.content)
                pil_image = Image.open(image_data)
                self.verify_image = ImageTk.PhotoImage(pil_image)
                self.verify_image_label.config(image=self.verify_image)
            else:
                messagebox.showerror("错误", f"验证码加载异常：状态码 {response.status_code}")
        except Exception as e:
            messagebox.showerror("错误", f"验证码加载失败：{str(e)}")

    def login(self):
        try:
            # 获取字段值
            username = self.username_entry.get()
            password = self.password_entry.get()
            verify_code = self.verify_code.get()
            
            # 验证字段完整性
            if not all([username, password, verify_code]):
                messagebox.showwarning("提示", "所有字段均为必填项")
                return

            # 构造登录请求
            login_url = "https://jk7859.mcu25805gtwqekeyw.com:59789/index.do"
            headers = {
                "User-Agent": "Mozilla/5.0",
                "Referer": "https://jk7859.mcu25805gtwqekeyw.com:59789/",
                "Content-Type": "application/x-www-form-urlencoded"
            }
            
            # 模拟登录参数
            form_data = {
                "username": username,
                "password": password,
                "verifyCode": verify_code,
                "timestamp": str(random.randint(1000000000, 9999999999))
            }
            
            # 发送登录请求
            response = self.session.post(login_url, data=form_data, headers=headers, verify=False)
            
            # 判断登录状态
            if response.status_code == 200:
                if "登录成功" in response.text:
                    messagebox.showinfo("成功", "登录成功！")
                elif "账号或密码错误" in response.text:
                    messagebox.showerror("错误", "账号或密码错误")
                elif "验证码错误" in response.text:
                    messagebox.showerror("错误", "验证码错误")    
                else:
                    messagebox.showinfo("信息", "身份验证成功，跳转中...")
            else:
                messagebox.showerror("错误", f"登录异常：状态码 {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            messagebox.showerror("错误", "连接服务器失败，请检查网络连接")
        except Exception as e:
            messagebox.showerror("错误", f"登录过程中发生异常：{str(e)}")

if __name__ == "__main__":
    root = tk.Tk()
    root.geometry("300x400")
    app = LoginApp(root)
    root.mainloop()