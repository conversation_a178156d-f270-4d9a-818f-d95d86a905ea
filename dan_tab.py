#!/usr/bin/env python
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import re
from bs4 import BeautifulSoup
import json
import threading
import urllib3
import requests
from urllib.parse import urljoin
import datetime
import time
from PIL import Image, ImageTk
import io

# 禁用未验证HTTPS请求的警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

from utils.logger import Logger, register_status_callback, unregister_status_callback
from core.session import SessionConfig

# 从SessionConfig获取BASE_URL和BASE_DOMAIN
BASE_URL = SessionConfig.BASE_URL
BASE_DOMAIN = SessionConfig.BASE_DOMAIN
from game_info import GameInfo
from game_display import GameDisplay
from betting_ui import BettingUI
from betting_logic import BettingLogic
from bet_records import BetRecords
from status_info import StatusInfo
from game_info_ui import GameInfoUI

class DanTab:
    def __init__(self, parent, notebook, app):
        """初始化胆选项卡"""
        self.parent = parent
        self.notebook = notebook
        self.app = app
        self.session = app.session
        
        # 创建日志记录器
        self.logger = Logger("DanTab")
        
        # 注册状态更新回调
        register_status_callback(self.update_status_from_log)
        
        # 创建胆选项卡
        self.tab = ttk.Frame(notebook)
        notebook.add(self.tab, text="胆选项卡")
        
        # 胆选项卡的URL
        self.dan_url = f"{BASE_URL}/offcial/index.do?code=JSSC168#5.0.0"
        
        # 初始化游戏信息相关
        self.game_info = GameInfo(self.session)
        self.game_info_ui = GameInfoUI(self.tab, self.session, self.game_info)
        
        # 投注信息
        self.positions = ["冠军", "亚军", "季军", "第四", "第五", "第六", "第七", "第八", "第九", "第十"]
        
        # 默认选中的位置
        self.selected_position = "冠军"  # 默认选中冠军
        
        # 每个位置的选中号码和投注金额
        self.position_data = {}
        for position in self.positions:
            self.position_data[position] = {
                "selected_numbers": [],
                "amount": tk.StringVar(value="10")
            }
        
        # 总投注金额
        self.total_amount = tk.StringVar(value="0")
        
        # 设置默认投注模式为元
        self.bet_unit = tk.StringVar(value="1")
        self.bet_unit_text = tk.StringVar(value="元")
        
        # 设置默认倍数为1
        self.multiple = tk.StringVar(value="1")
        self.multiple_options = ["1", "5", "10", "15", "20", "50", "100", "200", "500", "1000", "2000"]
        
        # 初始化选项卡内容
        self.init_tab_content()
        
        # 监听选项卡切换
        self.notebook.bind("<<NotebookTabChanged>>", self.on_tab_changed)
    
    def init_tab_content(self):
        """初始化选项卡内容，复用试玩选项卡的界面组件"""
        try:
            # 创建主要容器 - 采用试玩选项卡的布局方式
            self.main_paned = ttk.PanedWindow(self.tab, orient=tk.HORIZONTAL)
            self.main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
            
            # 左侧面板 - 复用试玩选项卡的布局结构
            self.left_frame = ttk.Frame(self.main_paned)
            self.main_paned.add(self.left_frame, weight=5)
            
            # 右侧面板 - 复用试玩选项卡的状态信息区域
            self.right_frame = ttk.Frame(self.main_paned)
            self.main_paned.add(self.right_frame, weight=1)
            
            # 初始化左侧面板 - 复用试玩选项卡的游戏信息和投注界面
            self.init_left_panel()
            
            # 初始化右侧面板 - 复用试玩选项卡的状态信息显示
            self.init_right_panel()
            
            # 初始化模块化组件 - 复用试玩选项卡的组件结构
            self.init_modules()
            
            self.logger.info("胆选项卡初始化完成")
        except Exception as e:
            self.logger.error(f"初始化胆选项卡时出错: {str(e)}")
    
    def init_left_panel(self):
        """初始化左侧面板"""
        try:
            # 左侧面板分为上下两部分
            left_paned = ttk.PanedWindow(self.left_frame, orient=tk.VERTICAL)
            left_paned.pack(fill=tk.BOTH, expand=True)
            
            # 上部分 - 游戏信息和投注界面
            self.upper_frame = ttk.Frame(left_paned)
            left_paned.add(self.upper_frame, weight=2)
            
            # 下部分 - 投注记录
            self.lower_frame = ttk.Frame(left_paned)
            left_paned.add(self.lower_frame, weight=1)
            
            self.logger.info("左侧面板初始化完成")
        except Exception as e:
            self.logger.info(f"初始化左侧面板时出错: {str(e)}")
    
    def init_right_panel(self):
        """初始化右侧面板"""
        try:
            # 右侧面板为状态信息区域
            self.status_frame = ttk.Frame(self.right_frame)
            self.status_frame.pack(fill=tk.BOTH, expand=True)
            
            self.logger.info("右侧面板初始化完成")
        except Exception as e:
            self.logger.info(f"初始化右侧面板时出错: {str(e)}")
    
    def init_modules(self):
        """初始化模块化组件"""
        try:
            # 初始化投注逻辑
            self.betting_logic = BettingLogic(self.session, self.game_info)
            
            # 初始化游戏信息显示
            self.game_display = GameDisplay(self.upper_frame, self.session, self.game_info)
            
            # 初始化投注界面
            self.betting_ui = BettingUI(self.upper_frame, self)
            
            # 初始化投注记录
            self.bet_records = BetRecords(self.lower_frame, self.betting_logic)
            
            # 初始化状态信息
            self.status_info = StatusInfo(self.status_frame)
            
            self.logger.info("模块化组件初始化完成")
        except Exception as e:
            self.logger.info(f"初始化模块化组件时出错: {str(e)}")
    
    def get_dan_data(self):
        """获取胆数据"""
        try:
            # 检查是否已登录
            if not self.app.is_logged_in:
                messagebox.showwarning("警告", "请先登录")
                # 切换到登录选项卡
                self.notebook.select(self.app.login_tab.tab)
                return
            
            # 更新状态
            self.status_var.set("正在获取数据...")
            self.data_text.delete(1.0, tk.END)
            self.data_text.insert(tk.END, "正在获取数据，请稍候...\n")
            
            # 发送请求获取胆页面
            self.logger.info(f"发送请求到: {self.dan_url}")
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36",
                "Referer": BASE_URL
            }
            
            response = self.app.session.get(self.dan_url, headers=headers, verify=False)
            
            if response.status_code != 200:
                error_msg = f"获取胆页面失败，状态码: {response.status_code}"
                self.logger.info(error_msg)
                self.status_var.set(error_msg)
                self.data_text.delete(1.0, tk.END)
                self.data_text.insert(tk.END, error_msg)
                return
            
            # 保存响应内容到文件以便调试
            with open("dan_response.html", "w", encoding="utf-8") as f:
                f.write(response.text)
            self.logger.info("成功获取胆页面数据并保存到dan_response.html")
            
            # 解析HTML获取数据
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 初始化结果变量和位置数据
            result = None
            html_content = response.text
            self.positions_data = []
            
            # 综合解析策略 - 尝试所有方法并合并结果
            # 1. 尝试从渲染内容中解析
            rendered_result = self.try_parse_rendered_content(soup)
            
            # 2. 尝试从模板中提取数据结构
            self.logger.info("尝试从模板中提取数据结构")
            template_result = self.extract_from_templates(soup)
            
            # 3. 尝试使用正则表达式
            self.logger.info("尝试使用正则表达式提取数据")
            regex_result = self.extract_using_regex(html_content)
            
            # 整合所有方法的结果
            if rendered_result:
                result = rendered_result
                self.logger.info("使用从渲染内容中解析的结果")
            elif template_result:
                result = template_result
                self.logger.info("使用从模板中提取的结果")
            elif regex_result:
                result = regex_result
                self.logger.info("使用正则表达式提取的结果")
            else:
                # 如果所有方法都失败，创建一个默认的数据结构
                self.logger.info("所有解析方法都失败，创建默认数据结构")
                default_positions = ["冠军", "亚军", "季军", "第四", "第五", "第六", "第七", "第八", "第九", "第十"]
                
                for position in default_positions:
                    self.positions_data.append({
                        "position": position,
                        "numbers": [{"number": f"{i:02d}", "active": False} for i in range(1, 11)],
                        "quick_buttons": ["全", "大", "小", "单", "双", "清"]
                    })
                
                result = "使用默认数据结构:\n\n"
                for position in default_positions:
                    result += f"位置: {position}\n"
                    result += "可选号码: 01-10\n\n"
            
            # 显示结果
            self.data_text.delete(1.0, tk.END)
            self.data_text.insert(tk.END, result)
            self.status_var.set("数据获取成功")
            
            # 调试输出完整数据结构
            try:
                if self.positions_data:
                    self.logger.info(f"获取到 {len(self.positions_data)} 个位置的数据结构")
                    for pos in self.positions_data:
                        self.logger.info(f"位置: {pos['position']}, 号码数量: {len(pos['numbers'])}, 快捷按钮: {len(pos['quick_buttons'])}")
                else:
                    self.logger.info("未能生成有效的位置数据结构")
            except Exception as e:
                self.logger.info(f"输出数据结构时出错: {str(e)}")
        
        except Exception as e:
            self.logger.info(f"获取胆数据时出错: {str(e)}")
            self.data_text.delete(1.0, tk.END)
            self.data_text.insert(tk.END, f"获取数据时出错: {str(e)}")
            self.status_var.set("获取数据失败")

    def try_parse_rendered_content(self, soup):
        """尝试从已渲染的内容中解析数据"""
        try:
            # 尝试精确匹配HTML结构
            # 首先找到sumBtn_box容器
            sumBtn_box = soup.select_one('.sumBtn_box')
            if not sumBtn_box:
                self.logger.info("未找到.sumBtn_box容器")
                return None
            
            self.logger.info("找到.sumBtn_box容器")
            
            # 找到clear元素
            clear_div = sumBtn_box.select_one('.clear[data-bind*="component"]')
            if not clear_div:
                self.logger.info("未找到.clear[data-bind*=component]元素")
                return None
            
            self.logger.info("找到.clear[data-bind*=component]元素")
            
            # 尝试多种可能的itemlist选择器
            possible_selectors = [
                'div[data-bind*="foreach:itemList"]',
                'div[class*="itemlist"]',
                'div[data-bind*="foreach"]',
                '.itemlist-10',  # 尝试直接用类名
                'div[class^="itemlist"]'  # 以itemlist开头的类
            ]
            
            itemlist_div = None
            used_selector = None
            
            for selector in possible_selectors:
                temp_div = clear_div.select_one(selector)
                if temp_div:
                    itemlist_div = temp_div
                    used_selector = selector
                    break
            
            if not itemlist_div:
                self.logger.info("尝试了多种选择器但未找到itemlist元素")
                
                # 直接检查clear_div的所有子div元素
                child_divs = clear_div.find_all('div', recursive=False)
                if child_divs:
                    self.logger.info(f"找到{len(child_divs)}个直接子div元素，尝试使用第一个")
                    itemlist_div = child_divs[0]
                else:
                    # 尝试直接从clear元素中找sumBtn_list
                    sumBtn_list = clear_div.select('.sumBtn_list.clear')
                    if not sumBtn_list:
                        self.logger.info("在clear元素中也未找到sumBtn_list.clear元素")
                        return None
                        
                    self.logger.info(f"直接从clear元素中找到{len(sumBtn_list)}个sumBtn_list元素")
            else:
                self.logger.info(f"使用选择器 '{used_selector}' 找到itemlist元素")
                # 找到所有sumBtn_list元素
                sumBtn_list = itemlist_div.select('.sumBtn_list.clear')
                if not sumBtn_list:
                    self.logger.info("在itemlist元素中未找到sumBtn_list.clear元素")
                    # 尝试查找不带clear类的sumBtn_list
                    sumBtn_list = itemlist_div.select('.sumBtn_list')
                    if not sumBtn_list:
                        self.logger.info("在itemlist元素中也未找到.sumBtn_list元素")
                        return None
                    self.logger.info(f"在itemlist元素中找到{len(sumBtn_list)}个.sumBtn_list元素(不带clear类)")
                else:
                    self.logger.info(f"在itemlist元素中找到{len(sumBtn_list)}个sumBtn_list.clear元素")
            
            # 如果找到sumBtn_list元素，继续处理
            result = "胆数据:\n\n"
            position_data = []
            
            # 遍历每个sumBtn_list元素
            for row in sumBtn_list:
                # 找到sumBtn_left元素
                sumBtn_left = row.select_one('.sumBtn_left')
                if not sumBtn_left:
                    self.logger.info("未找到sumBtn_left元素")
                    continue
                
                # 找位置名称
                name_elem = sumBtn_left.select_one('.name')
                position_name = name_elem.text.strip() if name_elem else "未知位置"
                
                # 找到fleft元素
                fleft_div = sumBtn_left.select_one('.fleft')
                if not fleft_div:
                    self.logger.info(f"在位置 {position_name} 中未找到fleft元素")
                    continue
                
                # 找到所有num-box元素
                num_boxes = fleft_div.select('.num-box')
                numbers = []
                
                for box in num_boxes:
                    # 找到a元素
                    num_elem = box.select_one('a')
                    if num_elem:
                        # 获取号码文本
                        font_elem = num_elem.select_one('font')
                        if font_elem:
                            num_text = font_elem.text.strip()
                        else:
                            num_text = num_elem.text.strip()
                        
                        # 检查是否激活
                        is_active = 'active' in num_elem.get('class', [])
                        numbers.append((num_text, is_active))
                
                # 找到快捷按钮
                sumBtn_righr = row.select_one('.sumBtn_righr')
                quick_buttons = []
                if sumBtn_righr:
                    btn_elems = sumBtn_righr.select('a')
                    for btn in btn_elems:
                        quick_buttons.append(btn.text.strip())
                
                # 构建结果
                if numbers:
                    result += f"位置: {position_name}\n"
                    result += "号码: "
                    for num, active in numbers:
                        if active:
                            result += f"[{num}] "
                        else:
                            result += f"{num} "
                    result += "\n"
                    
                    if quick_buttons:
                        result += f"快捷按钮: {' '.join(quick_buttons)}\n"
                    
                    result += "\n"
                    
                    # 保存到结构化数据
                    position_data.append({
                        "position": position_name,
                        "numbers": [{"number": num, "active": active} for num, active in numbers],
                        "quick_buttons": quick_buttons
                    })
            
            if position_data:
                self.positions_data = position_data
                self.logger.info(f"成功解析了{len(position_data)}个位置的数据")
                return result
            else:
                self.logger.info("未能从sumBtn_list元素中提取有效数据")
                return None
        except Exception as e:
            self.logger.info(f"解析渲染内容时出错: {str(e)}")
            return None

    def extract_from_templates(self, soup):
        """从模板中提取数据结构"""
        try:
            # 查找所有模板脚本
            templates = soup.select('script[type="text/html"]')
            if not templates:
                self.logger.info("未找到模板脚本")
                return None
            
            # 尝试从所有模板中组合数据
            all_positions = []
            template_ids = []
            
            for template in templates:
                template_id = template.get('id', '未知')
                template_content = template.text
                
                # 检查模板是否包含相关内容
                if 'sumBtn_list' in template_content or 'num-box' in template_content:
                    self.logger.info(f"分析模板: id={template_id}")
                    template_ids.append(template_id)
                    
                    # 从模板解析位置结构
                    positions = re.findall(r'<span class="name"[^>]*>([^<]*)</span>', template_content)
                    if positions:
                        self.logger.info(f"在模板 {template_id} 中找到 {len(positions)} 个位置名称")
                        all_positions.extend(positions)
            
            # 如果没有找到位置，使用默认名称
            if not all_positions:
                self.logger.info("在所有模板中都未找到位置名称，检查其他可能的模式")
                
                # 尝试查找mainNav-children中的位置名称
                nav_children = soup.select_one('.mainNav-children')
                if nav_children:
                    nav_positions = [span.text.strip() for span in nav_children.select('span') if span.text.strip()]
                    if nav_positions:
                        self.logger.info(f"从mainNav-children中找到 {len(nav_positions)} 个位置名称")
                        all_positions.extend(nav_positions)
            
            # 如果仍然没有找到，使用默认名称
            if not all_positions:
                all_positions = ["冠军", "亚军", "季军", "第四", "第五", "第六", "第七", "第八", "第九", "第十"]
                self.logger.info("使用默认位置名称")
                
                # 创建结果
                result = "从模板提取的胆数据结构:\n\n"
                for position in all_positions:
                    result += f"位置: {position}\n"
                    result += "可选号码: 01-10\n\n"
                return result
            
            # 创建结果
            result = "从模板提取的胆数据结构:\n\n"
            for position in all_positions:
                result += f"位置: {position}\n"
                result += "可选号码: 01-10\n\n"
            return result
        except Exception as e:
            self.logger.info(f"从模板提取数据时出错: {str(e)}")
            return None

    def extract_using_regex(self, html_content):
        """使用正则表达式提取数据"""
        try:
            # 创建结果和位置数据结构
            position_data = []
            result = "胆数据(正则表达式提取):\n\n"
            
            # 首先尝试从mainNav-children中提取所有位置名称
            nav_positions = []
            nav_matches = re.findall(r'<div class="mainNav-children".*?>(.*?)</div>', html_content, re.DOTALL)
            if nav_matches:
                nav_text = nav_matches[0]
                # 查找所有a标签中的文本作为位置名称
                nav_positions = re.findall(r'<a[^>]*>([^<]+)</a>', nav_text)
                self.logger.info(f"从导航栏中找到位置名称: {nav_positions}")
            
            # 如果没有找到位置名称，使用默认位置名称
            if not nav_positions:
                nav_positions = ["冠军", "亚军", "季军", "第四", "第五", "第六", "第七", "第八", "第九", "第十"]
                self.logger.info("使用默认位置名称")
            
            # 尝试提取完整的位置结构
            # 查找sumBtn_list区块
            sumBtn_lists = re.findall(r'<div class="sumBtn_list clear">(.+?)<div class="sumBtn_righr">', html_content, re.DOTALL)
            
            if sumBtn_lists:
                self.logger.info(f"使用正则表达式找到{len(sumBtn_lists)}个sumBtn_list区块")
                
                # 确保位置名称数量足够
                if len(nav_positions) < len(sumBtn_lists):
                    # 如果位置名称不够，补充默认名称
                    default_positions = ["冠军", "亚军", "季军", "第四", "第五", "第六", "第七", "第八", "第九", "第十"]
                    nav_positions.extend([f"位置{i+1}" for i in range(len(nav_positions), len(sumBtn_lists))])
                    nav_positions = nav_positions[:len(sumBtn_lists)]
                
                # 处理每个位置块
                for idx, block in enumerate(sumBtn_lists):
                    # 获取位置名称
                    position_name = nav_positions[idx] if idx < len(nav_positions) else f"位置{idx+1}"
                    
                    # 提取号码
                    numbers = re.findall(r'<a [^>]*class="[^"]*num[^"]*"[^>]*>(?:<font[^>]*>)?(\d+)(?:</font>)?</a>', block)
                    if not numbers:
                        # 如果没有找到号码，使用01-10
                        numbers = [f"{i:02d}" for i in range(1, 11)]
                    
                    # 提取快捷按钮
                    quick_buttons = re.findall(r'<a [^>]*class="[^"]*Btn"[^>]*>([^<]+)</a>', block)
                    if not quick_buttons:
                        quick_buttons = ["全", "大", "小", "单", "双", "清"]
                    
                    # 添加到结果
                    position_data.append({
                        "position": position_name,
                        "numbers": [{"number": num, "active": False} for num in numbers],
                        "quick_buttons": quick_buttons
                    })
                    
                    # 构建显示文本
                    result += f"位置: {position_name}\n"
                    result += "号码: " + " ".join([f"{num}" for num in numbers]) + "\n"
                    result += f"快捷按钮: {' '.join(quick_buttons)}\n\n"
                
                if position_data:
                    self.positions_data = position_data
                    self.logger.info(f"成功提取{len(position_data)}个位置的数据")
                    return result
                
                # 构建结果
                for position in positions:
                    result += f"位置: {position}\n"
                    result += "可选号码: 01-10\n\n"
                    position_data.append({
                        "position": position,
                        "numbers": [{"number": f"{i:02d}", "active": False} for i in range(1, 11)],
                        "quick_buttons": ["全", "大", "小", "单", "双", "清"]
                    })
                
                if position_data:
                    self.positions_data = position_data
                    return result
            
            # 如果没有找到完整结构，尝试单独提取元素
            # 尝试匹配数字
            numbers = re.findall(r'<a [^>]*class="[^"]*num[^"]*"[^>]*>([^<]+)</a>', html_content)
            if not numbers:
                numbers = re.findall(r'<span [^>]*class="[^"]*num[^"]*"[^>]*>([^<]+)</span>', html_content)
                
            # 尝试匹配位置名称
            positions = re.findall(r'<span [^>]*class="name"[^>]*>([^<]+)</span>', html_content)
            
            # 尝试匹配快捷按钮
            quick_buttons = re.findall(r'<a [^>]*class="[^"]*Btn"[^>]*>([^<]+)</a>', html_content)
            
            # 如果找到了位置名称，构建简单的数据结构
            if positions:
                clean_positions = [p for p in positions if p.strip() and p.strip() != '&nbsp;']
                self.logger.info(f"使用正则表达式找到{len(clean_positions)}个位置名称")
                
                for position in clean_positions:
                    result += f"位置: {position}\n"
                    if numbers:
                        result += f"找到的号码: {', '.join(sorted(set(numbers))[:10])}\n"
                    else:
                        result += "可选号码: 01-10\n"
                    
                    if quick_buttons:
                        result += f"快捷按钮: {', '.join(sorted(set(quick_buttons)))}\n"
                    else:
                        result += "快捷按钮: 全, 大, 小, 单, 双, 清\n"
                    
                    result += "\n"
                    
                    position_data.append({
                        "position": position,
                        "numbers": [{"number": f"{i:02d}", "active": False} for i in range(1, 11)],
                        "quick_buttons": quick_buttons if quick_buttons else ["全", "大", "小", "单", "双", "清"]
                    })
                
                if position_data:
                    self.positions_data = position_data
                    return result
            
            # 如果没有找到位置名称但找到了数字
            if numbers and not positions:
                self.logger.info(f"使用正则表达式找到{len(numbers)}个数字，但没有位置名称")
                result += f"找到的号码: {', '.join(sorted(set(numbers))[:20])}\n\n"
                
                # 使用默认位置名称
                default_positions = ["冠军", "亚军", "季军", "第四", "第五", "第六", "第七", "第八", "第九", "第十"]
                
                for position in default_positions:
                    position_data.append({
                        "position": position,
                        "numbers": [{"number": f"{i:02d}", "active": False} for i in range(1, 11)],
                        "quick_buttons": ["全", "大", "小", "单", "双", "清"]
                    })
                
                self.positions_data = position_data
                return result
            
            # 如果只找到了数据绑定
            bindings = re.findall(r'data-bind="[^"]+"', html_content)
            if bindings:
                self.logger.info(f"找到{len(bindings)}个数据绑定")
                result = "找到的数据绑定:\n\n"
                result += "\n".join(bindings[:20])  # 只显示前20个避免过多
                return result
            
            self.logger.info("正则表达式未找到任何有用数据")
            return None
        except Exception as e:
            self.logger.info(f"使用正则表达式提取数据时出错: {str(e)}")
            return None
    
    def update_status(self, message):
        """更新状态显示"""
        try:
            if hasattr(self, 'status_info'):
                self.status_info.add_status(message)
            self.logger.info(message)
        except Exception as e:
            self.logger.error(f"更新状态显示时出错: {str(e)}")
    
    def update_status_from_log(self, message):
        """从日志更新状态显示，用于回调函数"""
        try:
            # 状态信息模块会自动处理日志消息
            pass
        except Exception as e:
            self.logger.error(f"从日志更新状态时出错: {str(e)}")
    
    def on_tab_changed(self, event):
        """当选项卡切换时触发"""
        try:
            # 获取当前选中的选项卡
            current_tab = self.notebook.select()
            
            # 如果切换到胆选项卡
            if current_tab == str(self.tab):
                self.logger.info("切换到胆选项卡")
                
                # 刷新游戏信息
                if hasattr(self, 'game_info'):
                    self.game_info.fetch_game_info()
                    # 游戏信息自动更新由 GameInfoUI 处理，无需重复启动
                
                # 刷新投注记录
                if hasattr(self, 'bet_records'):
                    self.bet_records.refresh_records()
        except Exception as e:
            self.logger.error(f"处理选项卡切换时出错: {str(e)}")
    
    def cleanup(self):
        """清理资源，在应用程序关闭时调用"""
        try:
            # 取消注册状态更新回调
            unregister_status_callback(self.update_status_from_log)
            
            # 清理游戏信息相关资源
            if hasattr(self, 'game_info'):
                self.game_info.stop_auto_update()
            
            # 清理游戏显示相关资源
            if hasattr(self, 'game_display'):
                self.game_display.cleanup()
            
            # 在初始化完成后设置默认选中的位置
            if hasattr(self, 'betting_ui'):
                self.betting_ui.select_position(self.selected_position)
            
            # 清理投注记录相关资源
            if hasattr(self, 'bet_records'):
                self.bet_records.cleanup()
            
            # 清理状态信息相关资源
            if hasattr(self, 'status_info'):
                self.status_info.cleanup()
            
            self.logger.info("胆选项卡资源已清理")
        except Exception as e:
            self.logger.error(f"清理胆选项卡资源时出错: {str(e)}")
