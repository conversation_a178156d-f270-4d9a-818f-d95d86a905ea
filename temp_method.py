def update_game_info_from_api(self, manual_refresh=False):
    """使用API接口数据更新游戏信息
    
    Args:
        manual_refresh: 是否是手动刷新，如果是手动刷新则更新游戏名称
        
    Returns:
        bool: 是否成功更新游戏信息
    """
    try:
        # 生成时间戳
        current_time = datetime.datetime.now()
        timestamp = int(current_time.timestamp() * 1000)
        self.logger.info(f"当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S.%f')}")
        
        # 构建 API URL
        api_url = f"{BASE_URL}/lotData/getNewPeriod.do?code=JSSC168&_={timestamp}"
        self.logger.info(f"请求游戏信息 API URL: {api_url}")
        
        # 添加请求头
        headers = {
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }
        
        # 发送请求
        response = self.session.get(api_url, headers=headers, verify=False, timeout=10)
        response.raise_for_status()
        
        # 解析响应
        data = response.json()
        # 只输出关键信息，避免日志过多
        self.logger.info(f"游戏信息 API 获取成功，当前期号: {data.get('fnumberofperiod', '--')}, 状态: {data.get('fstatus', '--')}")
        
        # 更新游戏信息
        # 使用API返回的游戏名称，如果有的话
        game_name = None
        
        # 尝试从数据中获取游戏名称
        if 'gameName' in data:
            game_name = data['gameName']
        elif 'game_name' in data:
            game_name = data['game_name']
        
        # 如果是手动刷新或者游戏名称为空，才更新游戏名称
        # 判断是否是手动刷新，或者游戏名称标签当前为空
        current_name = self.game_name_label.cget("text")
        if manual_refresh or not current_name or current_name == "--":
            if game_name:
                self.game_name_label.config(text=game_name)
                # 更新游戏信息对象中的游戏名称
                self.game_info.game_name = game_name
            elif 'code' in data:
                # 如果没有游戏名称但有游戏代码，使用游戏代码
                game_code = data['code']
                if game_code == "JSSC168":
                    self.game_name_label.config(text="168极速赛车")
                    self.game_info.game_name = "168极速赛车"
                else:
                    self.game_name_label.config(text=game_code)
                    self.game_info.game_name = game_code
        
        # 更新期号信息
        if 'fnumberofperiod' in data:
            current_period = data['fnumberofperiod']
            self.period_label.config(text=f"第 {current_period} 期")
            # 更新游戏信息对象中的期号
            self.game_info.current_period = current_period
        
        # 更新期号状态
        if 'fstatus' in data:
            status = data['fstatus']
            status_text = "未知状态"
            if status == 1:
                status_text = "投注中"
            elif status == 2:
                status_text = "已封盘"
            self.period_status_label.config(text=status_text)
            # 更新游戏信息对象中的期号状态
            self.game_info.period_status = status_text
        
        # 更新倒计时
        remaining_time = "未知"
        self.remaining_seconds = 0  # 初始化剩余秒数
        
        if 'fclosetime' in data and 'ServerTime' in data:
            try:
                # 解析时间
                close_time = datetime.datetime.strptime(data['fclosetime'], "%Y/%m/%d %H:%M:%S")
                server_time = datetime.datetime.strptime(data['ServerTime'], "%Y/%m/%d %H:%M:%S")
                
                # 计算剩余时间
                time_diff = (close_time - server_time).total_seconds()
                if time_diff > 0:
                    # 保存剩余秒数供倒计时使用
                    self.remaining_seconds = int(time_diff)
                    self.logger.info(f"获取到剩余投注时间: {self.remaining_seconds} 秒")
                    
                    minutes = int(time_diff // 60)
                    seconds = int(time_diff % 60)
                    remaining_time = f"{minutes:02d}:{seconds:02d}"
                else:
                    remaining_time = "00:00"
                    self.remaining_seconds = 0
            except Exception as e:
                self.logger.info(f"计算倒计时时出错: {str(e)}")
                remaining_time = "--:--"
                self.remaining_seconds = 0
        
        # 只有当没有正在运行的倒计时线程时才更新显示
        if not hasattr(self, 'countdown_running') or not self.countdown_running:
            self.countdown_label.config(text=remaining_time)
        
        # 更新游戏信息对象中的倒计时
        self.game_info.remaining_time_str = remaining_time
        
        # 显示上一期开奖号码
        if 'fpreviousperiod' in data and 'fpreviousresult' in data:
            previous_period = data['fpreviousperiod']
            previous_result = data['fpreviousresult']
            self.logger.info(f"上一期({previous_period})开奖号码: {previous_result}")
            
            # 更新上一期期号显示
            self.previous_period_label.config(text=previous_period)
            
            # 将开奖号码分割为列表
            numbers = previous_result.split(',')
            
            # 更新开奖号码显示，使用空格分隔以便于阅读
            formatted_result = ' '.join(numbers)
            self.previous_result_label.config(text=formatted_result)
            
            # 将开奖号码保存到游戏信息对象中
            self.game_info.previous_result = previous_result
            self.game_info.previous_period = previous_period
        
        self.logger.info("使用 API 数据更新游戏信息成功")
        return True
    except Exception as e:
        self.logger.info(f"使用 API 数据更新游戏信息时出错: {str(e)}")
        return False
