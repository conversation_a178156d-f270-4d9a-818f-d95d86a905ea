#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
游戏信息显示模块
处理游戏信息的显示和更新
"""

import tkinter as tk
from tkinter import ttk
import threading
import time
import datetime
import webbrowser
from PIL import Image, ImageTk
import io
import requests
from urllib.parse import urljoin
from utils.logger import Logger
from core.session import SessionConfig

# 从SessionConfig获取BASE_URL
BASE_URL = SessionConfig.BASE_URL

class GameDisplay:
    def __init__(self, parent, session, game_info):
        """初始化游戏信息显示
        
        Args:
            parent: 父级容器
            session: 会话对象，用于发送请求
            game_info: 游戏信息对象
        """
        # 创建日志记录器
        self.logger = Logger("GameDisplay")
        self.parent = parent
        self.session = session
        self.game_info = game_info
        
        # 游戏信息显示区域
        self.info_frame = None
        
        # 游戏名称和图标
        self.game_name_label = None
        self.game_icon_label = None
        self.game_icon_image = None
        self.game_icon_photo = None
        
        # 期号信息
        self.period_label = None
        self.period_status_label = None
        
        # 倒计时
        self.countdown_label = None
        self.countdown_running = False
        self.countdown_thread = None
        
        # 上一期开奖结果
        self.previous_period_label = None
        self.previous_result_label = None
        
        # 官方开奖网链接
        self.official_site_link = None
        
        # 刷新按钮
        self.refresh_button = None
        self.auto_refresh_var = None
        
        # 初始化UI
        self.init_ui()
        
        # 注册游戏信息更新回调
        self.game_info.register_update_callback(self.on_game_info_updated)
    
    def init_ui(self):
        """初始化游戏信息显示UI"""
        # 创建游戏信息框架
        self.info_frame = ttk.LabelFrame(self.parent, text="游戏信息", padding=10)
        self.info_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 创建游戏信息网格
        info_grid = ttk.Frame(self.info_frame)
        info_grid.pack(fill=tk.X, expand=True)
        
        # 第一行：游戏名称和图标
        self.game_icon_label = ttk.Label(info_grid)
        self.game_icon_label.grid(row=0, column=0, rowspan=2, padx=5, pady=5)
        
        self.game_name_label = ttk.Label(info_grid, text="加载中...", font=("Arial", 12, "bold"))
        self.game_name_label.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 第二行：期号信息
        period_frame = ttk.Frame(info_grid)
        period_frame.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        self.period_label = ttk.Label(period_frame, text="第 -- 期")
        self.period_label.pack(side=tk.LEFT, padx=(0, 10))
        
        self.period_status_label = ttk.Label(period_frame, text="状态: --")
        self.period_status_label.pack(side=tk.LEFT)
        
        # 第三行：倒计时
        countdown_frame = ttk.Frame(info_grid)
        countdown_frame.grid(row=0, column=2, rowspan=2, padx=5, pady=5)
        
        ttk.Label(countdown_frame, text="倒计时:").pack(side=tk.LEFT)
        self.countdown_label = ttk.Label(countdown_frame, text="--:--:--", font=("Arial", 12, "bold"))
        self.countdown_label.pack(side=tk.LEFT, padx=5)
        
        # 第四行：上一期开奖结果
        result_frame = ttk.Frame(info_grid)
        result_frame.grid(row=2, column=0, columnspan=3, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(result_frame, text="上期开奖:").pack(side=tk.LEFT)
        self.previous_period_label = ttk.Label(result_frame, text="--")
        self.previous_period_label.pack(side=tk.LEFT, padx=5)
        
        ttk.Label(result_frame, text="期").pack(side=tk.LEFT)
        self.previous_result_label = ttk.Label(result_frame, text="--")
        self.previous_result_label.pack(side=tk.LEFT, padx=5)
        
        # 第五行：官方开奖网链接和刷新按钮
        control_frame = ttk.Frame(info_grid)
        control_frame.grid(row=3, column=0, columnspan=3, sticky=tk.W, padx=5, pady=5)
        
        self.official_site_link = ttk.Label(control_frame, text="官方开奖网", foreground="blue", cursor="hand2")
        self.official_site_link.pack(side=tk.LEFT, padx=5)
        self.official_site_link.bind("<Button-1>", self.open_official_site)
        
        self.refresh_button = ttk.Button(control_frame, text="刷新", command=self.manual_refresh)
        self.refresh_button.pack(side=tk.LEFT, padx=10)
        
        self.auto_refresh_var = tk.BooleanVar(value=True)
        auto_refresh_check = ttk.Checkbutton(
            control_frame, 
            text="自动刷新", 
            variable=self.auto_refresh_var,
            command=self.toggle_auto_refresh
        )
        auto_refresh_check.pack(side=tk.LEFT)
        
        self.logger.info("游戏信息显示UI初始化完成")
    
    def update_display(self):
        """更新游戏信息显示"""
        try:
            # 更新游戏名称
            self.game_name_label.config(text=self.game_info.game_name)
            
            # 更新游戏图标
            if self.game_info.game_icon:
                try:
                    # 尝试从服务器获取图标
                    icon_url = urljoin(BASE_URL, self.game_info.game_icon)
                    response = self.session.get(icon_url, verify=False)
                    response.raise_for_status()
                    
                    # 将图像数据转换为PIL图像
                    image_data = response.content
                    self.game_icon_image = Image.open(io.BytesIO(image_data))
                    
                    # 调整图像大小
                    self.game_icon_image = self.game_icon_image.resize((40, 40), Image.LANCZOS)
                    
                    # 转换为Tkinter可用的格式
                    self.game_icon_photo = ImageTk.PhotoImage(self.game_icon_image)
                    
                    # 更新标签
                    self.game_icon_label.config(image=self.game_icon_photo)
                except Exception as e:
                    self.logger.info(f"加载游戏图标失败: {str(e)}")
            
            # 更新期号信息
            if self.game_info.current_period:
                self.period_label.config(text=f"第 {self.game_info.current_period} 期")
            
            # 更新期号状态
            self.period_status_label.config(text=self.game_info.period_status)
            
            # 更新倒计时
            if not self.countdown_running:
                self.countdown_label.config(text=self.game_info.remaining_time_str)
            
            # 更新上一期开奖结果
            if hasattr(self.game_info, 'previous_period') and self.game_info.previous_period:
                self.previous_period_label.config(text=self.game_info.previous_period)
            
            if hasattr(self.game_info, 'previous_result') and self.game_info.previous_result:
                self.previous_result_label.config(text=self.game_info.previous_result)
            
            # 更新官方开奖网链接
            self.official_site_link.config(text="官方开奖网")
            
            self.logger.info("游戏信息显示已更新")
        except Exception as e:
            self.logger.info(f"更新游戏信息显示时出错: {str(e)}")
    
    def on_game_info_updated(self, game_info, countdown_only=False):
        """游戏信息更新回调
        
        Args:
            game_info: 更新后的游戏信息对象
            countdown_only: 是否只更新倒计时
        """
        if countdown_only:
            # 只更新倒计时
            if not self.countdown_running:
                self.countdown_label.config(text=game_info.remaining_time_str)
        else:
            # 更新所有信息
            self.update_display()
            
            # 如果剩余时间小于等于30秒，则启动精确倒计时
            if game_info.remaining_time <= 30 and game_info.remaining_time > 0:
                self.start_countdown(game_info.remaining_time)
    
    def manual_refresh(self):
        """手动刷新游戏信息"""
        self.logger.info("手动刷新游戏信息")
        self.game_info.fetch_game_info()
        self.update_display()
    
    def toggle_auto_refresh(self):
        """切换自动刷新状态"""
        if self.auto_refresh_var.get():
            self.logger.info("启用自动刷新")
            self.game_info.start_auto_update()
        else:
            self.logger.info("禁用自动刷新")
            self.game_info.stop_auto_update()
    
    def open_official_site(self, event=None):
        """打开官方开奖网站"""
        try:
            if self.game_info.official_site:
                webbrowser.open(self.game_info.official_site)
                self.logger.info(f"打开官方开奖网站: {self.game_info.official_site}")
        except Exception as e:
            self.logger.error(f"打开官方开奖网站失败: {str(e)}")
    
    def start_countdown(self, seconds):
        """启动精确倒计时
        
        Args:
            seconds: 倒计时秒数
        """
        # 停止已有的倒计时线程
        if self.countdown_running:
            self.countdown_running = False
            if self.countdown_thread:
                try:
                    self.countdown_thread.join(timeout=1)
                except Exception:
                    pass
        
        # 创建新的倒计时线程
        self.countdown_running = True
        self.countdown_thread = threading.Thread(target=self._countdown_task, args=(seconds,))
        self.countdown_thread.daemon = True
        self.countdown_thread.start()
        
        self.logger.info(f"启动精确倒计时，总时间: {seconds} 秒")
    
    def _countdown_task(self, total_seconds):
        """倒计时任务
        
        Args:
            total_seconds: 总倒计时秒数
        """
        try:
            self.logger.info(f"倒计时开始，总时间: {total_seconds} 秒")
            remaining = total_seconds
            is_drawing_time = False
            drawing_time = 10  # 开奖时间额外增加10秒
            
            start_time = time.time()
            end_time = start_time + total_seconds
            
            # 倒计时循环
            while time.time() < end_time + drawing_time:
                if not self.countdown_running:
                    break
                
                current_time = time.time()
                
                # 判断是否进入开奖时间
                if current_time >= end_time and not is_drawing_time:
                    is_drawing_time = True
                    self.logger.info("投注时间结束，进入开奖时间")
                
                # 计算并显示倒计时
                if not is_drawing_time:
                    # 投注时间倒计时
                    remaining = end_time - current_time
                    minutes = int(remaining // 60)
                    seconds = int(remaining % 60)
                    milliseconds = int((remaining - int(remaining)) * 100)  # 显示百分之一秒
                    time_str = f"{minutes:02d}:{seconds:02d}.{milliseconds:02d}"
                else:
                    # 开奖时间倒计时
                    remaining = end_time + drawing_time - current_time
                    seconds = int(remaining)
                    milliseconds = int((remaining - seconds) * 100)  # 显示百分之一秒
                    time_str = f"开奖中 {seconds:02d}.{milliseconds:02d}"
                
                # 更新UI（使用after方法确保在主线程中更新UI）
                self.countdown_label.after(0, lambda t=time_str: self.countdown_label.config(text=t))
                
                # 循环睡眠
                time.sleep(0.01)  # 使用小的睡眠时间获得更精确的倒计时
            
            self.logger.info("倒计时结束")
            # 倒计时结束，标记为非运行状态
            self.countdown_running = False
            
            # 倒计时结束后，刷新游戏信息
            self.countdown_label.after(1000, self.manual_refresh)
        except Exception as e:
            self.logger.error(f"倒计时任务出错: {str(e)}")
            self.countdown_running = False
    
    def cleanup(self):
        """清理资源"""
        # 停止倒计时
        self.countdown_running = False
        if self.countdown_thread:
            try:
                self.countdown_thread.join(timeout=1)
            except Exception:
                pass
        
        # 取消注册游戏信息更新回调
        self.game_info.unregister_update_callback(self.on_game_info_updated)
        
        self.logger.info("游戏信息显示资源已清理")
