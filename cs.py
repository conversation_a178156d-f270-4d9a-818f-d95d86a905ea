# -*- coding: utf-8 -*-
import requests
from bs4 import BeautifulSoup
import tkinter as tk
from tkinter import ttk, messagebox
from PIL import Image, ImageTk
import io
import time
import os
import datetime
import re
import pickle
import json
from requests.cookies import RequestsCookieJar

# 创建调试日志函数
def self.logger.info(message):
    log_file = "login_debug.log"
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    with open(log_file, "a", encoding="utf-8") as f:
        f.write(f"[{timestamp}] {message}\n")
    print(message)

class LoginApp:
    def __init__(self, root):
        self.root = root
        self.root.title("自动登录系统")
        
        # 定义域名和基础URL，确保一致性
        # 注意：登录API需要使用jvvpbv2580513aknu.com域名
        self.base_domain = "jvvpbv2580513aknu.com"
        self.base_url = f"https://jk7859.{self.base_domain}:59789"
        
        # 初始化会话和用户信息
        self.session = requests.Session()
        self.username = ""
        self.balance = "0"
        self.user_links = []
        self.is_logged_in = False
        
        # 会话文件路径
        self.session_file = "session.pkl"
        self.user_info_file = "user_info.json"
        
        # 胞选项卡的URL
        self.dan_url = f"{self.base_url}/offcial/index.do?code=JSSC168#5.0.0"
        
        # 尝试加载之前的会话
        if self.load_session():
            # 如果加载成功，验证会话是否有效
            if self.verify_session():
                # 会话有效，直接显示用户信息
                self.logger.info("会话有效，自动登录成功")
                self.is_logged_in = True
                
                # 更新用户信息显示
                self.update_user_info_display()
            else:
                # 会话无效，需要重新登录
                self.logger.info("会话无效，需要重新登录")
        
        # 创建选项卡控件
        # 首先创建选项卡控件
        self.notebook = ttk.Notebook(root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 按顺序创建每个选项卡并添加到notebook
        # 1. 创建登录选项卡
        self.login_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.login_tab, text="登录")
        
        # 2. 创建用户信息选项卡
        self.user_info_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.user_info_tab, text="用户信息")
        
        # 3. 创建胞选项卡
        self.dan_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.dan_tab, text="胞选项")
        
        # 最后初始化各个选项卡的内容
        # 创建登录表单
        self.create_login_form()
        
        # 初始化用户信息选项卡内容
        self.init_user_info_tab()
        
        # 初始化胞选项卡内容
        self.init_dan_tab()
        
        # 初始化时选中登录选项卡
        self.notebook.select(self.login_tab)
        
        # 确保退出按钮初始时禁用
        if hasattr(self, 'logout_btn'):
            self.logout_btn.config(state=tk.DISABLED)
        
        self.verify_url = f"{self.base_url}/loginVerifycode.do"
        self.refresh_verification_code()

    def create_login_form(self):
        """创建登录表单"""
        try:
            # 创建登录界面
            self.login_frame = tk.Frame(self.login_tab)
            self.login_frame.pack(pady=20)
            
            # 创建输入框
            tk.Label(self.login_frame, text="账号:").pack()
            self.username_entry = tk.Entry(self.login_frame)
            self.username_entry.pack(pady=5)
            
            tk.Label(self.login_frame, text="密码:").pack()
            self.password_entry = tk.Entry(self.login_frame, show="*")
            self.password_entry.pack(pady=5)
            
            tk.Label(self.login_frame, text="验证码:").pack()
            self.verify_code = tk.Entry(self.login_frame)
            self.verify_code.pack(pady=5)

            # 功能按钮
            self.refresh_btn = tk.Button(self.login_frame, text="刷新验证码", command=self.refresh_verification_code)
            self.refresh_btn.pack(pady=5)
            
            self.login_btn = tk.Button(self.login_frame, text="立即登录", command=self.login)
            self.login_btn.pack(pady=10)
            
            # 验证码显示
            self.verify_image_label = tk.Label(self.login_frame)
            self.verify_image_label.pack()
            
            self.logger.info("登录表单初始化完成")
        except Exception as e:
            self.logger.info(f"创建登录表单时出错: {str(e)}")
    
    def init_user_info_tab(self):
        """初始化用户信息选项卡"""
        # 初始化用户信息选项卡
        # user_info_tab已在__init__中创建，这里只初始化其内容
        
        # 创建用户信息显示区域
        info_frame = tk.Frame(self.user_info_tab, padx=20, pady=20)
        info_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建用户信息标签
        self.info_label = tk.Label(info_frame, text="未登录", font=("Arial", 12), justify=tk.LEFT)
        self.info_label.pack(anchor=tk.W, pady=10)
        
        # 创建功能链接区域
        self.links_frame = tk.Frame(self.user_info_tab)
        self.links_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # 创建退出登录按钮
        self.logout_btn = tk.Button(self.user_info_tab, text="退出登录", command=self.logout, state=tk.DISABLED)
        self.logout_btn.pack(pady=20)
        
        self.logger.info("用户信息选项卡初始化完成")
    
    def init_dan_tab(self):
        """初始化胞选项卡"""
        try:
            # 创建胞选项卡
            # dan_tab已在__init__中创建，这里只初始化其内容
            
            # 创建按钮和滚动区域
            control_frame = tk.Frame(self.dan_tab)
            control_frame.pack(fill=tk.X, padx=10, pady=10)
            
            # 添加刷新按钮
            refresh_btn = tk.Button(control_frame, text="获取数据", command=self.fetch_dan_data, width=15, height=2)
            refresh_btn.pack(side=tk.LEFT, padx=10)
            
            # 创建滚动区域显示数据
            self.dan_canvas = tk.Canvas(self.dan_tab, bg="white")
            scrollbar = tk.Scrollbar(self.dan_tab, orient=tk.VERTICAL, command=self.dan_canvas.yview)
            self.dan_canvas.configure(yscrollcommand=scrollbar.set)
            
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            self.dan_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            
            # 创建框架来存放数据
            self.dan_frame = tk.Frame(self.dan_canvas)
            self.dan_canvas.create_window((0, 0), window=self.dan_frame, anchor=tk.NW)
            
            # 配置滚动区域
            self.dan_frame.bind("<Configure>", lambda e: self.dan_canvas.configure(scrollregion=self.dan_canvas.bbox("all")))
            
            # 初始显示消息
            self.dan_status = tk.Label(self.dan_frame, text="点击获取数据按钮来加载数据", font=("Arial", 12))
            self.dan_status.pack(pady=20)
            
            self.logger.info("胆选项卡初始化完成")
        except Exception as e:
            self.logger.info(f"初始化胆选项卡时出错: {str(e)}")
    
    def fetch_dan_data(self):
        """获取胆页面数据"""
        try:
            # 显示加载中消息
            self.dan_status.config(text="正在加载数据...", fg="blue")
            self.root.update()
            
            # 清除之前的数据
            for widget in self.dan_frame.winfo_children():
                widget.destroy()
            
            # 重新创建状态标签
            self.dan_status = tk.Label(self.dan_frame, text="正在加载数据...", font=("Arial", 12), fg="blue")
            self.dan_status.pack(pady=20)
            self.root.update()
            
            # 发送请求获取数据
            self.logger.info(f"发送请求到: {self.dan_url}")
            response = self.session.get(self.dan_url, verify=False)
            
            if response.status_code == 200:
                self.logger.info("成功获取胆页面数据")
                
                # 解析HTML
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 尝试查找指定的div
                sumBtn_box = soup.select_one('div.sumBtn_box')
                
                if sumBtn_box:
                    self.logger.info("找到sumBtn_box元素")
                    
                    # 清除状态标签
                    self.dan_status.destroy()
                    
                    # 创建标题
                    title_label = tk.Label(self.dan_frame, text="数据选择", font=("Arial", 16, "bold"))
                    title_label.pack(pady=10)
                    
                    # 解析并创建选择界面
                    self.create_selection_ui(sumBtn_box)
                else:
                    self.logger.info("未找到sumBtn_box元素")
                    self.dan_status.config(text="未找到数据元素，请确认您已登录", fg="red")
            else:
                self.logger.info(f"请求失败，状态码: {response.status_code}")
                self.dan_status.config(text=f"请求失败，状态码: {response.status_code}", fg="red")
        except Exception as e:
            self.logger.info(f"获取胆数据时出错: {str(e)}")
            # 确保状态标签存在
            if hasattr(self, 'dan_status') and self.dan_status.winfo_exists():
                self.dan_status.config(text=f"获取数据时出错: {str(e)}", fg="red")
            else:
                self.dan_status = tk.Label(self.dan_frame, text=f"获取数据时出错: {str(e)}", font=("Arial", 12), fg="red")
                self.dan_status.pack(pady=20)
    
    def create_selection_ui(self, sumBtn_box):
        """根据HTML创建选择界面"""
        try:
            # 查找所有的sumBtn_list元素
            sumBtn_lists = sumBtn_box.select('div.sumBtn_list')
            
            if not sumBtn_lists:
                self.logger.info("未找到sumBtn_list元素")
                tk.Label(self.dan_frame, text="未找到选择列表", font=("Arial", 12), fg="red").pack(pady=10)
                return
            
            self.logger.info(f"找到 {len(sumBtn_lists)} 个sumBtn_list元素")
            
            # 为每个选择列表创建一个框架
            for i, sumBtn_list in enumerate(sumBtn_lists):
                # 创建框架
                list_frame = tk.Frame(self.dan_frame, bd=1, relief=tk.SOLID)
                list_frame.pack(fill=tk.X, padx=10, pady=5, expand=True)
                
                # 获取名称
                name_span = sumBtn_list.select_one('span.name')
                name = name_span.text if name_span else f"选择{i+1}"
                
                # 创建标题
                title_frame = tk.Frame(list_frame, bg="#f0f0f0")
                title_frame.pack(fill=tk.X)
                
                name_label = tk.Label(title_frame, text=name, font=("Arial", 12, "bold"), bg="#f0f0f0")
                name_label.pack(side=tk.LEFT, padx=10, pady=5)
                
                # 创建数字选择区域
                nums_frame = tk.Frame(list_frame)
                nums_frame.pack(fill=tk.X, padx=10, pady=5)
                
                # 查找所有的数字按钮
                num_boxes = sumBtn_list.select('span.num-box')
                
                # 创建数字按钮
                for num_box in num_boxes:
                    num_a = num_box.select_one('a.num')
                    if num_a:
                        font_tag = num_a.select_one('font')
                        num_text = font_tag.text if font_tag else ""
                        
                        # 创建按钮
                        num_btn = tk.Button(nums_frame, text=num_text, width=3, height=1)
                        num_btn.pack(side=tk.LEFT, padx=2, pady=2)
                
                # 创建快速选择按钮区域
                quick_frame = tk.Frame(list_frame)
                quick_frame.pack(fill=tk.X, padx=10, pady=5)
                
                # 查找快速选择按钮
                quick_btns = sumBtn_list.select('div.sumBtn_righr a')
                
                # 创建快速选择按钮
                for quick_btn in quick_btns:
                    btn_text = quick_btn.text
                    btn = tk.Button(quick_frame, text=btn_text, width=5)
                    btn.pack(side=tk.LEFT, padx=5, pady=2)
            
            self.logger.info("成功创建选择界面")
        except Exception as e:
            self.logger.info(f"创建选择界面时出错: {str(e)}")
            tk.Label(self.dan_frame, text=f"创建界面时出错: {str(e)}", font=("Arial", 12), fg="red").pack(pady=10)
    
    def update_user_info_display(self):
        """更新用户信息显示"""
        try:
            # 确保用户信息选项卡已初始化
            if not hasattr(self, 'user_info_tab') or not hasattr(self, 'info_label'):
                self.logger.info("用户信息选项卡未初始化，先初始化")
                self.init_user_info_tab()
            
            # 确保 links_frame 已初始化
            if not hasattr(self, 'links_frame'):
                self.logger.info("links_frame 未初始化，创建一个新的")
                self.links_frame = tk.Frame(self.user_info_tab)
                self.links_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
            
            # 清空之前的功能链接显示
            for widget in self.links_frame.winfo_children():
                widget.destroy()
            
            # 更新用户信息标签
            self.info_label.config(text=f"账号：{self.username}\n系统余额：{self.balance}")
            
            # 添加功能链接按钮
            if self.user_links:
                link_label = tk.Label(self.links_frame, text="可用功能：", font=("Arial", 10, "bold"))
                link_label.pack(anchor="w", padx=10, pady=5)
                
                for i, link in enumerate(self.user_links[:10]):  # 限制显示数量
                    if link.strip():
                        link_btn = tk.Button(self.links_frame, text=link, width=20)
                        link_btn.pack(anchor="w", padx=20, pady=2)
            
            # 确保 logout_btn 已初始化
            if hasattr(self, 'logout_btn'):
                # 启用退出登录按钮
                self.logout_btn.config(state=tk.NORMAL)
            
            # 切换到用户信息选项卡
            self.notebook.select(self.user_info_tab)
            
            self.logger.info("用户信息显示已更新")
        except Exception as e:
            self.logger.info(f"更新用户信息显示时出错: {str(e)}")
    
    def refresh_verification_code(self):
        try:
            timestamp = int(time.time() * 1000)
            params = {"timestamp": timestamp}
            headers = {
                "User-Agent": "Mozilla/5.0",
                "X-Requested-With": "XMLHttpRequest"
            }
            
            response = self.session.get(self.verify_url, params=params, headers=headers, verify=False)
            if response.status_code == 200:
                image_data = io.BytesIO(response.content)
                pil_image = Image.open(image_data)
                self.verify_image = ImageTk.PhotoImage(pil_image)
                self.verify_image_label.config(image=self.verify_image)
        except Exception as e:
            messagebox.showerror("错误", f"验证码加载失败：{str(e)}")
            
    def save_session(self):
        """保存会话和用户信息到文件"""
        try:
            # 保存会话 cookie
            with open(self.session_file, 'wb') as f:
                pickle.dump(self.session.cookies, f)
            self.logger.info(f"会话信息已保存到 {self.session_file}")
            
            # 保存用户信息
            user_info = {
                "username": self.username,
                "balance": self.balance,
                "user_links": self.user_links,
                "is_logged_in": self.is_logged_in,
                "timestamp": time.time()
            }
            with open(self.user_info_file, 'w', encoding='utf-8') as f:
                json.dump(user_info, f, ensure_ascii=False, indent=2)
            self.logger.info(f"用户信息已保存到 {self.user_info_file}")
            
            return True
        except Exception as e:
            self.logger.info(f"保存会话时出错: {str(e)}")
            return False
    
    def load_session(self):
        """从文件加载会话和用户信息"""
        try:
            # 检查会话文件是否存在
            if os.path.exists(self.session_file) and os.path.exists(self.user_info_file):
                # 加载会话 cookie
                with open(self.session_file, 'rb') as f:
                    cookies = pickle.load(f)
                    self.session.cookies.update(cookies)
                self.logger.info(f"从 {self.session_file} 加载了会话信息")
                
                # 加载用户信息
                with open(self.user_info_file, 'r', encoding='utf-8') as f:
                    user_info = json.load(f)
                    
                # 检查会话是否过期（超过24小时则认为过期）
                timestamp = user_info.get("timestamp", 0)
                if time.time() - timestamp > 24 * 60 * 60:
                    self.logger.info("会话已过期，需要重新登录")
                    return False
                
                self.username = user_info.get("username", "")
                self.balance = user_info.get("balance", "0")
                self.user_links = user_info.get("user_links", [])
                self.is_logged_in = user_info.get("is_logged_in", False)
                
                self.logger.info(f"从 {self.user_info_file} 加载了用户信息: {self.username}")
                return True
            else:
                self.logger.info("没有找到会话文件，需要重新登录")
                return False
        except Exception as e:
            self.logger.info(f"加载会话时出错: {str(e)}")
            return False
    
    def verify_session(self):
        """验证当前会话是否有效"""
        try:
            if not self.is_logged_in:
                return False
                
            # 访问首页检查登录状态
            index_url = f"{self.base_url}/index.do"
            self.logger.info(f"验证会话，发送请求到: {index_url}")
            
            response = self.session.get(index_url, headers={
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36",
                "Referer": f"{self.base_url}/"
            }, verify=False, timeout=10)
            
            # 检查是否包含登录成功的标志
            success_patterns = [
                r'退出账户',  # 退出账户按钮
                r'会员中心',  # 会员中心按钮
                r'系统余额',  # 系统余额标签
                r'账\s*号'   # 账号标签
            ]
            
            html_content = response.text
            for pattern in success_patterns:
                if re.search(pattern, html_content):
                    self.logger.info(f"会话验证成功，找到登录标志: {pattern}")
                    return True
            
            self.logger.info("会话验证失败，未找到登录标志")
            return False
        except Exception as e:
            self.logger.info(f"验证会话时出错: {str(e)}")
            return False
    
    def logout(self):
        """退出登录功能"""
        try:
            # 尝试访问退出登录链接
            logout_url = f"{self.base_url}/logout.do"
            self.logger.info(f"发送退出登录请求到: {logout_url}")
            
            response = self.session.get(logout_url, headers={
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36",
                "Referer": f"{self.base_url}/"
            }, verify=False)
            
            self.logger.info(f"退出登录响应状态码: {response.status_code}")
        except Exception as e:
            self.logger.info(f"退出登录时出错: {str(e)}")
        
        # 无论退出请求是否成功，都重置会话和界面
        self.session = requests.Session()  # 创建新的会话
        self.is_logged_in = False
        self.username = ""
        self.balance = "0"
        self.user_links = []
        
        # 删除会话文件
        if os.path.exists(self.session_file):
            os.remove(self.session_file)
        if os.path.exists(self.user_info_file):
            os.remove(self.user_info_file)
        self.logger.info("已删除会话文件")
        
        # 清空用户信息
        self.info_label.config(text="请先登录获取用户信息")
        
        # 清空功能链接区域
        for widget in self.links_frame.winfo_children():
            widget.destroy()
        
        # 禁用退出按钮
        self.logout_btn.config(state=tk.DISABLED)
        
        # 清空输入框
        self.username_entry.delete(0, tk.END)
        self.password_entry.delete(0, tk.END)
        self.verify_code.delete(0, tk.END)
        
        # 刷新验证码
        self.refresh_verification_code()
        
        # 切换回登录选项卡
        self.notebook.select(self.login_tab)
        
        messagebox.showinfo("操作成功", "已成功退出登录")

    def login(self):  # 
        try:
            # 字段获取
            username = self.username_entry.get()
            password = self.password_entry.get()
            verify_code = self.verify_code.get()
            
            # 验证字段
            if not all([username, password, verify_code]):
                messagebox.showwarning("提示", "所有字段均为必填项")
                return

            # 构造请求
            login_url = f"{self.base_url}/login.do"
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36",
                "Referer": f"{self.base_url}/index.do",
                "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
                "X-Requested-With": "XMLHttpRequest"
            }
            
            # 请求参数（避免分号 ）
            form_data = {
                "username": username,
                "password": password,
                "verifyCode": verify_code,
                "gToken": "",  # 添加gToken参数
                "timestamp": str(int(time.time() * 1000))  
            }
            
            self.logger.info(f"发送登录请求数据: {form_data}")
            
            # 打印请求URL和头信息
            self.logger.info(f"登录URL: {login_url}")
            self.logger.info(f"请求头信息: {headers}")
            
            # 发送登录请求
            response = self.session.post(login_url, data=form_data, headers=headers, verify=False)
            
            # 记录原始响应以便调试
            self.logger.info(f"响应状态码: {response.status_code}")
            self.logger.info(f"响应头信息: {dict(response.headers)}")
            self.logger.info(f"原始响应内容: {response.text}")
            
            # 尝试解析JSON响应
            login_success = False  # 默认为登录失败
            try:
                # 判断是否是JSON响应
                content_type = response.headers.get('Content-Type', '')
                if 'application/json' in content_type:
                    # 解析JSON响应
                    json_data = response.json()
                    self.logger.info(f"解析的JSON响应: {json_data}")
                    
                    # 判断登录结果
                    if 'status' in json_data:
                        # 检查状态码
                        status_code = json_data.get('status') or json_data.get('code')
                        if status_code == 0 or status_code == '0' or status_code == 200:
                            self.logger.info("登录成功: 服务器返回成功状态码")
                            login_success = True
                            message = json_data.get('message', '登录成功')
                        else:
                            self.logger.info(f"登录失败: 服务器返回错误状态码 {status_code}")
                            error_msg = json_data.get('message') or json_data.get('msg') or '登录失败'
                            messagebox.showerror("登录失败", error_msg)
                            self.refresh_verification_code()
                            return
                    elif 'success' in json_data:
                        # 检查success字段
                        if json_data['success'] == True or json_data['success'] == 'true' or json_data['success'] == 1:
                            self.logger.info("登录成功: 服务器返回success=true")
                            login_success = True
                            message = json_data.get('message', '登录成功')
                        else:
                            self.logger.info("登录失败: 服务器返回success=false")
                            error_msg = json_data.get('message') or json_data.get('msg') or '登录失败'
                            messagebox.showerror("登录失败", error_msg)
                            self.refresh_verification_code()
                            return
                    else:
                        # 如果没有标准的状态字段，尝试其他方式判断
                        login_success = True  # 默认认为成功，后面会通过获取首页进一步验证
                        self.logger.info("未找到标准状态字段，将尝试通过获取首页验证登录状态")
                else:
                    # 处理HTML响应
                    self.logger.info("响应不是JSON格式，将尝试解析HTML")
                    soup = BeautifulSoup(response.text, 'html.parser')
                    page_text = soup.get_text()
                    
                    # 检查是否有登录成功的关键词
                    success_text = ['会员中心', '系统余额', '账 号', '退出账户']
                    success_indicators = [text for text in success_text if text in page_text]
                    if success_indicators:
                        self.logger.info(f"找到成功指示词: {success_indicators}")
                        login_success = True
            except Exception as e:
                # 捕获解析错误
                self.logger.info(f"解析响应时出错: {str(e)}")
                messagebox.showerror("登录失败", f"处理登录响应时出错: {str(e)}")
                self.refresh_verification_code()
                return
                
                # 新版账号检测逻辑 - 根据用户提供的HTML结构调整
                self.logger.info("开始查找.loginInner元素...")
                
                # 保存完整HTML以便分析
                with open("full_response.html", "w", encoding="utf-8") as f:
                    f.write(response.text)
                self.logger.info("已保存完整HTML响应到 full_response.html")
                
                # 尝试直接从完整HTML中匹配相关内容
                self.logger.info("尝试使用更直接的方法查找用户信息...")
                
                # 尝试使用正则表达式或字符串匹配来找到用户信息
                html_str = str(soup)
                
                # 检查是否包含 loginInner 类
                if "loginInner" in html_str:
                    self.logger.info("在HTML中找到 'loginInner' 字符串")
                else:
                    self.logger.info("在HTML中未找到 'loginInner' 字符串")
                
                # 尝试不同的选择器
                selectors = [
                    '.loginInner', 
                    '.login .box .loginInner', 
                    '.login .loginInner', 
                    'div.loginInner',
                    '.box .loginInner',
                    '.lgbox .login .box .loginInner',
                    '.loginbg .lgbox .login .box .loginInner'
                ]
                
                login_inner = None
                for selector in selectors:
                    elements = soup.select(selector)  # 使用select而不是select_one来获取所有匹配元素
                    self.logger.info(f"选择器 '{selector}' 找到 {len(elements)} 个元素")
                    if elements:
                        login_inner = elements[0]
                        self.logger.info(f"使用选择器 '{selector}' 找到.loginInner元素")
                        break
                
                self.logger.info(f"是否找到.loginInner: {login_inner is not None}")
                
                # 如果找不到loginInner，尝试查找其他可能包含用户信息的元素
                if not login_inner:
                    self.logger.info("尝试查找其他可能包含用户信息的元素...")
                    # 查找所有包含“账号”或“系统余额”的元素
                    account_elements = []
                    for element in soup.find_all():
                        if element.string and ('账 号' in element.text or '账号' in element.text or '系统余额' in element.text):
                            account_elements.append(element)
                            self.logger.info(f"找到包含账号/余额信息的元素: {element.name} - {element.text.strip()}")
                
                if login_inner:
                    # 打印完整的loginInner内容以便调试
                    self.logger.info(f".loginInner内容: {login_inner.text.strip()}")
                    self.logger.info(f".loginInner HTML: {login_inner}")
                    
                    # 尝试提取用户名和余额
                    account_info = login_inner.select('p')
                    self.logger.info(f"在.loginInner中找到 {len(account_info)} 个 p 标签")
                    if account_info:
                        for i, p in enumerate(account_info):
                            self.logger.info(f"p标签 {i}: {p.text.strip()}")
                    
                    # 使用更精确的选择器
                    # 注意: BeautifulSoup的contains选择器可能不能正确工作，所以我们使用更直接的方法
                    username_span = None
                    balance_span = None
                    
                    # 逐个检查p标签寻找用户名和余额
                    for p in account_info:
                        if '账 号' in p.text or '账号' in p.text:
                            username_span = p.select_one('span')
                            self.logger.info(f"在账号行找到span: {username_span.text if username_span else 'None'}")
                        elif '系统余额' in p.text:
                            balance_span = p.select_one('span#lebo_money') or p.select_one('span')
                            self.logger.info(f"在余额行找到span: {balance_span.text if balance_span else 'None'}")
                    
                    # 如果上面的选择器不起作用，尝试更通用的方法
                    if not username_span or not balance_span:
                        all_spans = login_inner.select('span')
                        self.logger.info(f"在.loginInner中找到 {len(all_spans)} 个 span 元素")
                        for i, span in enumerate(all_spans):
                            self.logger.info(f"span {i}: {span.text}")
                        
                        # 假设第一个span是用户名，第二个是余额
                        if len(all_spans) >= 2:
                            if not username_span:
                                username_span = all_spans[0]
                                self.logger.info(f"使用第一个span作为用户名: {username_span.text}")
                            if not balance_span:
                                balance_span = all_spans[1]
                                self.logger.info(f"使用第二个span作为余额: {balance_span.text}")
                
                # 尝试直接从完整HTML中提取用户信息
                username = ""
                balance = "0"
                user_links = []
                
                # 如果在HTML中找到loginInner字符串但选择器未找到元素
                # 尝试使用正则表达式或字符串匹配来提取信息
                if "loginInner" in html_str and not login_inner:
                    self.logger.info("尝试使用字符串匹配方式提取用户信息")
                    
                    # 尝试找到账号行
                    import re
                    account_match = re.search(r'账 号：<span>([^<]+)</span>', html_str)
                    if account_match:
                        username = account_match.group(1)
                        self.logger.info(f"使用正则表达式找到用户名: {username}")
                    
                    # 尝试找到余额行
                    balance_match = re.search(r'系统余额：<span[^>]*>([^<]*)</span>', html_str)
                    if balance_match:
                        balance = balance_match.group(1)
                        self.logger.info(f"使用正则表达式找到余额: {balance}")
                    
                    # 尝试找到功能链接
                    link_matches = re.finditer(r'<a href="[^"]+">([^<]+)</a>', html_str)
                    for match in link_matches:
                        link_text = match.group(1)
                        if link_text not in ["网站首页", "会员登入", "开奖网", "购彩帮助", "加入收藏"]:
                            user_links.append(link_text)
                    
                    # 创建一个虚拟的loginInner元素
                    if username or balance != "0" or user_links:
                        # 注意: 我们已经在文件开头导入了BeautifulSoup，这里不需要再次导入
                        login_inner_html = f'<div class="loginInner"><p>账 号：<span>{username}</span></p><p>系统余额：<span>{balance}</span></p></div>'
                        login_inner = BeautifulSoup(login_inner_html, 'html.parser').div
                        self.logger.info("创建了虚拟的loginInner元素")
                
                # 如果找到了loginInner元素，使用正常的方式提取信息
                if login_inner:
                    # 提取用户名和余额
                    account_p = None
                    balance_p = None
                    
                    # 逐个检查p标签
                    for p in login_inner.select('p'):
                        p_text = p.text.strip()
                        if '账 号' in p_text or '账号' in p_text:
                            account_p = p
                            username_span = p.select_one('span')
                            if username_span:
                                username = username_span.text.strip()
                                self.logger.info(f"从 p 标签找到用户名: {username}")
                        elif '系统余额' in p_text:
                            balance_p = p
                            balance_span = p.select_one('span#lebo_money') or p.select_one('span')
                            if balance_span:
                                balance = balance_span.text.strip()
                                self.logger.info(f"从 p 标签找到余额: {balance}")
                    
                    # 获取功能链接
                    links = login_inner.select('a')
                    if links:
                        for link in links:
                            user_links.append(link.text.strip())
                
                # 判断登录是否成功 - 采用更严格的登录验证逻辑
                # 检查是否有明确的错误信息
                error_elements = soup.select('.error-message, .alert-danger, .error, .login-error')
                if error_elements:
                    error_message = error_elements[0].text.strip()
                    self.logger.info(f"发现错误消息: {error_message}")
                    messagebox.showerror("登录失败", f"登录失败: {error_message}")
                    self.refresh_verification_code()
                    return
                
                # 检查登录失败的关键词
                fail_keywords = ['密码错误', '账号不存在', '验证码错误', '登录失败', '用户名或密码错误']
                for keyword in fail_keywords:
                    if keyword in page_text:
                        self.logger.info(f"在页面中发现失败关键词: {keyword}")
                        messagebox.showerror("登录失败", f"登录失败: {keyword}")
                        self.refresh_verification_code()
                        return
                
                # 检查具体的登录成功标志 - 更精确的验证
                login_success = False
                
                # 1. 检查是否同时包含账号信息和余额信息
                if login_inner and ('账号' in login_inner.text or '账 号' in login_inner.text) and '系统余额' in login_inner.text:
                    login_success = True
                    self.logger.info("通过loginInner同时包含账号和余额信息确认登录成功")
                
                # 2. 检查是否有退出登录的链接
                logout_links = soup.select('a[href*="logout"], a:contains("退出")')
                if logout_links:
                    login_success = True
                    self.logger.info("发现退出登录链接，确认登录成功")
                
                # 3. 特定页面结构的检测
                if not login_success and len(success_indicators) >= 2:  # 至少匹配两个成功指标
                    login_success = True
                    self.logger.info(f"匹配多个成功指标: {success_indicators}，确认登录成功")
                
                if login_success:
                    self.logger.info("确认登录成功，尝试获取首页信息...")
                    
                    # 登录成功后，再次访问首页获取用户信息
                    try:
                        index_url = f"{self.base_url}/index.do"
                        self.logger.info(f"发送额外请求到首页: {index_url}")
                        
                        # 使用同一个session访问首页
                        index_response = self.session.get(index_url, headers={
                            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36",
                            "Referer": f"{self.base_url}/"
                        }, verify=False)
                        
                        if index_response.status_code == 200:
                            self.logger.info("成功获取首页内容")
                            
                            # 保存首页HTML以便分析
                            with open("index_response.html", "w", encoding="utf-8") as f:
                                f.write(index_response.text)
                            self.logger.info("已保存首页HTML响应到 index_response.html")
                            
                            # 使用BeautifulSoup解析首页
                            index_soup = BeautifulSoup(index_response.text, 'html.parser')
                            
                            # 使用更通用的方法提取用户信息
                            index_html_str = str(index_soup)
                            self.logger.info("尝试使用更通用的方法提取用户信息...")
                            
                            # 保存完整HTML到文件以便分析
                            with open("full_index_response.html", "w", encoding="utf-8") as f:
                                f.write(index_html_str)
                            self.logger.info("已保存完整首页HTML到 full_index_response.html")
                            
                            # 特别获取class="login"下class="box"下class="loginInner"的span和id="lebo_money"信息
                            self.logger.info("开始获取.login .box .loginInner中的span和id=lebo_money元素...")
                            
                            # 尝试直接获取指定结构
                            login_box_inner = index_soup.select_one('.login .box .loginInner')
                            if login_box_inner:
                                self.logger.info("找到.login .box .loginInner元素")
                                
                                # 获取所有span元素
                                spans = login_box_inner.find_all('span')
                                self.logger.info(f"在.login .box .loginInner中找到{len(spans)}个span元素")
                                for i, span in enumerate(spans):
                                    self.logger.info(f"span {i}: {span.text.strip()}")
                                
                                # 获取id=lebo_money元素
                                lebo_money = index_soup.find(id="lebo_money")
                                if lebo_money:
                                    self.logger.info(f"找到id=lebo_money元素: {lebo_money.text.strip()}")
                                    balance = lebo_money.text.strip()
                                else:
                                    self.logger.info("未找到id=lebo_money元素")
                                    # 尝试在loginInner中找余额span
                                    for p in login_box_inner.find_all('p'):
                                        if '系统余额' in p.text:
                                            balance_span = p.find('span')
                                            if balance_span:
                                                balance = balance_span.text.strip()
                                                self.logger.info(f"从系统余额行找到余额: {balance}")
                            else:
                                self.logger.info("未找到.login .box .loginInner元素，尝试其他选择器")
                                
                                # 尝试多种选择器来匹配用户提供的HTML结构
                                selectors = [
                                    '.loginInner',
                                    '.loginbg .lgbox .login .box .loginInner',
                                    'div.loginInner',
                                    '.box div.loginInner',
                                    '.login .loginInner'
                                ]
                                
                                login_inner_div = None
                                for selector in selectors:
                                    found = index_soup.select_one(selector)
                                    self.logger.info(f"尝试选择器 '{selector}': {found is not None}")
                                    if found:
                                        login_inner_div = found
                                        self.logger.info(f"使用选择器 '{selector}' 找到 loginInner 元素")
                                        break
                                if login_inner_div:
                                    self.logger.info("在首页中找到 .loginInner 元素")
                                    
                                    # 尝试获取账号信息 - 直接匹配用户提供的HTML结构
                                    for p in login_inner_div.find_all('p'):
                                        p_text = p.get_text()
                                        if '账 号' in p_text or '账号' in p_text:
                                            span = p.find('span')
                                            if span:
                                                username = span.get_text().strip()
                                                self.logger.info(f"从 loginInner 找到用户名: {username}")
                                        elif '系统余额' in p_text:
                                            span = p.find('span', id='lebo_money')
                                            if span:
                                                balance = span.get_text().strip()
                                                self.logger.info(f"从 loginInner 找到余额: {balance}")
                                            # 如果没有id为lebo_money的span，尝试找任何span
                                            span = p.find('span')
                                            if span:
                                                balance = span.get_text().strip()
                                                self.logger.info(f"从 loginInner 找到余额: {balance}")
                                
                                    # 获取功能链接
                                    for link in login_inner_div.find_all('a'):
                                        if link.text.strip() and link.text.strip() not in user_links:
                                            user_links.append(link.text.strip())
                                            self.logger.info(f"从 loginInner 找到功能链接: {link.text.strip()}")
                                            break
                                        # 如果没有id为lebo_money的span，尝试找任何span
                                        span = p.find('span')
                                        if span:
                                            balance = span.text.strip()
                                            self.logger.info(f"从 loginInner 找到余额: {balance}")
                                            break
                                
                                # 获取功能链接
                                for link in login_inner_div.find_all('a'):
                                    if link.text.strip() and link.text.strip() not in user_links:
                                        user_links.append(link.text.strip())
                                        self.logger.info(f"从 loginInner 找到功能链接: {link.text.strip()}")
                            
                            # 如果不能直接找到 loginInner，尝试使用正则表达式
                            if not login_inner_div:
                                self.logger.info("尝试使用正则表达式提取用户信息...")
                                
                                # 尝试匹配常见的用户信息模式
                                # 1. 先检查页面中是否包含登录成功的标志
                                import re  # 确保在这里导入re模块
                                
                                success_patterns = [
                                    r'退出账户',  # 退出账户按钮通常只有登录成功才会显示
                                    r'会员中心',  # 会员中心按钮
                                    r'系统余额',  # 系统余额标签
                                    r'账\s*号'   # 账号标签
                                ]
                                
                                is_logged_in = False
                                for pattern in success_patterns:
                                    if re.search(pattern, index_html_str):
                                        is_logged_in = True
                                        self.logger.info(f"在页面中找到登录成功标志: {pattern}")
                                        break
                            
                            # 如果找到loginInner元素或检测到登录成功标志，尝试提取用户信息
                            if login_inner_div or (not login_inner_div and is_logged_in):
                                self.logger.info("检测到登录成功，开始提取用户信息...")
                                
                                # 如果没有从 loginInner 直接提取到用户名，尝试使用正则表达式
                                if not username:
                                    # 尝试匹配用户名模式
                                    username_patterns = [
                                        r'账\s*号\s*[\uff1a:]\s*<span[^>]*>([^<]+)</span>',  # 账号：<span>username</span>
                                        r'<p[^>]*>\s*账\s*号[^<]*<span[^>]*>([^<]+)</span>',  # <p>账号...<span>username</span>
                                        r'账\s*号[^<]*<span[^>]*>([^<]+)</span>',  # 账号...<span>username</span>
                                        r'<span[^>]*>([^<]+)</span>[^<]*账\s*号',  # <span>username</span>...账号
                                        r'<div[^>]*class="user[^"]*"[^>]*>([^<]+)</div>',  # <div class="user">username</div>
                                        r'<div[^>]*id="username[^"]*"[^>]*>([^<]+)</div>',  # <div id="username">username</div>
                                    ]
                                    
                                    for pattern in username_patterns:
                                        match = re.search(pattern, index_html_str)
                                        if match:
                                            username = match.group(1).strip()
                                            self.logger.info(f"使用模式 '{pattern}' 找到用户名: {username}")
                                            break
                                
                                # 如果没有从 loginInner 直接提取到余额，尝试使用正则表达式
                                if balance == "0":
                                    # 尝试匹配余额模式
                                    balance_patterns = [
                                        r'系统余额\s*[\uff1a:]\s*<span[^>]*>([^<]+)</span>',  # 系统余额：<span>balance</span>
                                        r'<span[^>]*id="lebo_money"[^>]*>([^<]+)</span>',  # <span id="lebo_money">balance</span>
                                        r'余\s*额\s*[\uff1a:]\s*<span[^>]*>([^<]+)</span>',  # 余额：<span>balance</span>
                                        r'<div[^>]*class="balance[^"]*"[^>]*>([^<]+)</div>',  # <div class="balance">balance</div>
                                    ]
                                    
                                    for pattern in balance_patterns:
                                        match = re.search(pattern, index_html_str)
                                        if match:
                                            balance = match.group(1).strip()
                                            self.logger.info(f"使用模式 '{pattern}' 找到余额: {balance}")
                                            break
                                
                                # 如果没有从 loginInner 直接提取到功能链接，尝试使用正则表达式
                                if not user_links:
                                    # 尝试找到可能包含用户功能链接的区域
                                    user_areas = [
                                        r'<div[^>]*class="loginInner"[^>]*>(.*?)</div>',
                                        r'<div[^>]*class="user-info"[^>]*>(.*?)</div>',
                                        r'<div[^>]*class="user-links"[^>]*>(.*?)</div>',
                                        r'<div[^>]*class="account-info"[^>]*>(.*?)</div>',
                                    ]
                                    
                                    for pattern in user_areas:
                                        match = re.search(pattern, index_html_str, re.DOTALL)
                                        if match:
                                            user_area_content = match.group(1)
                                            self.logger.info(f"找到可能包含用户功能的区域: {pattern}")
                                            
                                            # 从区域中提取链接
                                            link_matches = re.finditer(r'<a[^>]*href="([^"]+)"[^>]*>([^<]+)</a>', user_area_content)
                                            for link_match in link_matches:
                                                link_url = link_match.group(1)
                                                link_text = link_match.group(2).strip()
                                                if link_text and link_text not in ["网站首页", "会员登入", "开奖网", "购彩帮助", "加入收藏"] and link_text not in user_links:
                                                    user_links.append(link_text)
                                                    self.logger.info(f"从用户区域找到功能链接: {link_text} ({link_url})")
                                            
                                            # 如果找到了链接，就不需要继续查找其他区域
                                            if user_links:
                                                break
                                    
                                    # 如果从用户区域没有找到链接，尝试从整个HTML中提取
                                    if not user_links:
                                        # 尝试找到常见的用户功能链接
                                        common_functions = [
                                            r'<a[^>]*href="[^"]*finance/recharge[^"]*"[^>]*>([^<]+)</a>',  # 充值/存款
                                            r'<a[^>]*href="[^"]*finance/withdraw[^"]*"[^>]*>([^<]+)</a>',  # 提款/取款
                                            r'<a[^>]*href="[^"]*directCharge[^"]*"[^>]*>([^<]+)</a>',  # 直充
                                            r'<a[^>]*href="[^"]*moneyChange[^"]*"[^>]*>([^<]+)</a>',  # 额度转换
                                            r'<a[^>]*href="[^"]*userCenter[^"]*"[^>]*>([^<]+)</a>',  # 用户中心/会员中心
                                            r'<a[^>]*href="[^"]*message[^"]*"[^>]*>([^<]+)</a>',  # 消息/讯息
                                        ]
                                        
                                        for pattern in common_functions:
                                            matches = re.finditer(pattern, index_html_str)
                                            for match in matches:
                                                link_text = match.group(1).strip()
                                                if link_text and link_text not in user_links:
                                                    user_links.append(link_text)
                                                    self.logger.info(f"从全局找到功能链接: {link_text}")
                                
                                # 如果没有找到任何功能链接，添加一些默认的功能
                                if not user_links:
                                    default_links = ["线上存款", "线上取款", "免提直充", "额度转换", "会员中心", "未读讯息"]
                                    user_links.extend(default_links)
                                    self.logger.info("使用默认功能链接")
                            else:
                                self.logger.info("未检测到登录成功标志，也未找到loginInner元素")
                    except Exception as e:
                        self.logger.info(f"获取首页信息时出错: {str(e)}")
                    
                    # 处理用户信息
                    # 如果没有提取到用户名，尝试从页面中再次提取
                    if not username or username == "用户":
                        # 尝试从页面中提取用户名
                        try:
                            import re
                            # 尝试匹配用户名模式
                            username_patterns = [
                                r'账\s*号\s*[\uff1a:]\s*<span[^>]*>([^<]+)</span>',  # 账号：<span>username</span>
                                r'<p[^>]*>\s*账\s*号[^<]*<span[^>]*>([^<]+)</span>',  # <p>账号...<span>username</span>
                                r'账\s*号[^<]*<span[^>]*>([^<]+)</span>',  # 账号...<span>username</span>
                                r'<span[^>]*>([^<]+)</span>[^<]*账\s*号',  # <span>username</span>...账号
                                r'<div[^>]*class="user[^"]*"[^>]*>([^<]+)</div>',  # <div class="user">username</div>
                                r'<div[^>]*id="username[^"]*"[^>]*>([^<]+)</div>',  # <div id="username">username</div>
                            ]
                            
                            html_str = response.text
                            for pattern in username_patterns:
                                match = re.search(pattern, html_str)
                                if match:
                                    username = match.group(1).strip()
                                    self.logger.info(f"从响应中直接提取到用户名: {username}")
                                    break
                        except Exception as e:
                            self.logger.info(f"尝试提取用户名时出错: {str(e)}")
                    
                    # 如果还是没有提取到，使用默认值
                    self.username = username or "1"  # 使用用户提供的HTML中的默认值
                    self.balance = balance
                    self.user_links = user_links
                    self.is_logged_in = True
                    
                    # 保存会话信息
                    self.save_session()
                    
                    # 更新用户信息显示
                    self.update_user_info_display()
                    
                    # 切换到用户信息选项卡
                    self.logger.info("切换到用户信息选项卡")
                    self.notebook.select(self.user_info_tab)
                    
                    # 显示成功消息
                    messagebox.showinfo("登录成功", f"欢迎 {self.username}，登录成功！")
                elif success_indicators:
                    # 如果有成功指标但没有找到用户信息
                    self.info_label.config(text="登录成功\n但未能获取完整用户信息")
                    self.logout_btn.config(state=tk.NORMAL)
                    self.notebook.select(self.user_info_tab)
                    messagebox.showinfo("登录成功", "欢迎登录\n\n注意: 系统未能获取完整用户信息")
                else:
                    # 分析登录失败的具体原因
                    self.logger.info("登录验证未通过，分析失败原因...")
                    
                    # 检查是否有错误信息
                    error_msg = soup.select_one('.error-message, .alert, .error, .login-error, .message, .notice')
                    if error_msg:
                        error_text = error_msg.text.strip()
                        self.logger.info(f"找到错误信息元素: {error_text}")
                        messagebox.showerror("登录失败", error_text)
                    else:
                        # 查找可能包含错误信息的文本
                        error_patterns = [
                            r'(密码错误|账号不存在|验证码错误|登录失败|用户名或密码错误)[^<]*',
                            r'<div[^>]*>(.*(?:错误|失败|不正确|不存在).*?)</div>',
                            r'<p[^>]*>(.*(?:错误|失败|不正确|不存在).*?)</p>'
                        ]
                        
                        error_found = False
                        for pattern in error_patterns:
                            matches = re.finditer(pattern, html_str)
                            for match in matches:
                                error_text = match.group(1).strip()
                                if error_text and len(error_text) < 100:  # 避免匹配太长的文本
                                    self.logger.info(f"使用正则表达式找到错误信息: {error_text}")
                                    messagebox.showerror("登录失败", error_text)
                                    error_found = True
                                    break
                            if error_found:
                                break
                        
                        if not error_found:
                            # 保存HTML以便分析
                            with open("login_response.html", "w", encoding="utf-8") as f:
                                f.write(response.text)
                            self.logger.info("未找到明确的错误信息，已保存响应到login_response.html")
                            messagebox.showerror("登录失败", "登录未成功，请检查账号密码或验证码\n已保存响应到login_response.html")
                    
                    # 刷新验证码
                    self.refresh_verification_code()
        except Exception as e:
            messagebox.showerror("错误", f"登录过程中发生错误：{str(e)}")
            self.refresh_verification_code()
    
    # ===== 主程序入口 =====
if __name__ == "__main__":
    root = tk.Tk()
    root.geometry("450x650")
    app = LoginApp(root)
    root.mainloop()