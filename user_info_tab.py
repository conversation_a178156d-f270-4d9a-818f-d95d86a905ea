#!/usr/bin/env python
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, messagebox
from utils.logger import Logger
from core.session import SessionConfig, SessionManager

# 从SessionConfig获取BASE_URL
BASE_URL = SessionConfig.BASE_URL

class UserInfoTab:
    def __init__(self, parent, notebook, app):
        """初始化用户信息选项卡"""
        self.parent = parent
        self.notebook = notebook
        self.app = app
        
        # 创建日志记录器
        self.logger = Logger("UserInfoTab")
        
        # 创建用户信息选项卡
        self.tab = ttk.Frame(notebook)
        notebook.add(self.tab, text="用户信息")
        
        # 初始化选项卡内容
        self.init_tab_content()
    
    def init_tab_content(self):
        """初始化选项卡内容"""
        try:
            # 创建用户信息显示区域
            info_frame = tk.Frame(self.tab, padx=20, pady=20)
            info_frame.pack(fill=tk.BOTH, expand=True)
            
            # 创建用户信息标签
            self.info_label = tk.Label(info_frame, text="未登录", font=("Arial", 12), justify=tk.LEFT)
            self.info_label.pack(anchor=tk.W, pady=10)
            
            # 创建功能链接区域
            self.links_frame = tk.Frame(self.tab)
            self.links_frame.pack(fill=tk.X, padx=20, pady=10)
            
            # 创建退出登录按钮
            self.logout_btn = tk.Button(self.tab, text="退出登录", command=self.logout, state=tk.DISABLED)
            self.logout_btn.pack(pady=20)
            
            self.logger.info("用户信息选项卡初始化完成")
        except Exception as e:
            self.logger.error(f"创建用户信息选项卡时出错: {str(e)}")
    
    def update_display(self):
        """更新用户信息显示"""
        try:
            if self.app.is_logged_in:
                # 设置用户信息文本
                info_text = f"账号：{self.app.username}\n系统余额：{self.app.balance}"
                self.info_label.config(text=info_text)
                
                # 启用退出按钮
                self.logout_btn.config(state=tk.NORMAL)
                
                # 生成功能链接（如果有）
                if hasattr(self, 'links_frame'):
                    # 先清除已有的链接
                    for widget in self.links_frame.winfo_children():
                        widget.destroy()
                    
                    # 添加链接
                    if self.app.user_links:
                        for i, link in enumerate(self.app.user_links):
                            if isinstance(link, dict) and 'name' in link and 'url' in link:
                                btn = tk.Button(
                                    self.links_frame, 
                                    text=link['name'], 
                                    command=lambda url=link['url']: self.open_url(url)
                                )
                                btn.pack(side=tk.LEFT, padx=5, pady=5)
                
                self.logger.info("用户信息显示已更新")
            else:
                self.info_label.config(text="未登录")
                self.logout_btn.config(state=tk.DISABLED)
                self.logger.info("用户未登录，显示默认信息")
        except Exception as e:
            self.logger.error(f"更新用户信息显示时出错: {str(e)}")
    
    def logout(self):
        """退出登录"""
        try:
            # 确认是否退出
            if not messagebox.askyesno("确认", "确定要退出登录吗？"):
                return
            
            # 尝试发送退出登录请求
            try:
                logout_url = f"{BASE_URL}/logout.do"
                self.app.session.get(logout_url, verify=False, timeout=5)
            except Exception as e:
                self.logger.warning(f"发送退出请求时出错: {str(e)}")
            
            # 清除会话和用户信息
            # 使用SessionManager的clear_session方法
            self.app.session_manager.clear_session()
            
            # 重置应用程序状态
            self.app.session = self.app.get_new_session()
            self.app.username = ""
            self.app.balance = "0"
            self.app.user_links = []
            self.app.is_logged_in = False
            
            # 更新显示
            self.update_display()
            
            # 切换到登录选项卡
            self.notebook.select(self.app.login_tab.tab)
            
            # 刷新验证码
            self.app.login_tab.refresh_verification_code()
            
            messagebox.showinfo("提示", "已成功退出登录")
            self.logger.info("用户已退出登录")
        except Exception as e:
            self.logger.error(f"退出登录时出错: {str(e)}")
            messagebox.showerror("错误", f"退出登录时出错: {str(e)}")
    
    def open_url(self, url):
        """打开功能链接"""
        import webbrowser
        try:
            # 对于相对URL，添加基础URL
            if not url.startswith(('http://', 'https://')):
                if url.startswith('/'):
                    url = f"{BASE_URL}{url}"
                else:
                    url = f"{BASE_URL}/{url}"
            
            webbrowser.open(url)
            self.logger.info(f"打开链接: {url}")
        except Exception as e:
            self.logger.error(f"打开链接时出错: {str(e)}")
            messagebox.showerror("错误", f"无法打开链接: {str(e)}")
