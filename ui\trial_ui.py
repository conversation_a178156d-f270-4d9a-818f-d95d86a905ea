#!/usr/bin/env python
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, scrolledtext
from PIL import Image, ImageTk
import io

class TrialTabUI:
    """
    试玩选项卡UI类，负责创建和管理试玩选项卡的所有UI元素
    """
    
    def __init__(self, tab, root):
        """
        初始化试玩选项卡UI
        
        Args:
            tab: 选项卡容器
            root: 根窗口
        """
        self.tab = tab
        self.root = root
        
        # 位置列表
        self.positions = ["冠军", "亚军", "季军", "第四", "第五", "第六", "第七", "第八", "第九", "第十"]
        
        # UI组件
        self.main_paned = None
        self.left_frame = None
        self.right_frame = None
        
        # 游戏信息区域组件
        self.game_info_frame = None
        self.game_icon_label = None
        self.game_name_label = None
        self.game_period_label = None
        self.game_status_label = None
        self.countdown_label = None
        self.refresh_button = None
        self.game_icon_image = None
        self.game_icon_photo = None
        
        # 试玩账号信息区域组件
        self.account_frame = None
        self.account_label = None
        self.balance_label = None
        self.refresh_balance_button = None
        self.auto_refresh_button = None
        self.get_trial_button = None
        
        # 投注界面区域组件
        self.betting_frame = None
        self.position_buttons = {}
        self.number_frames = {}
        self.number_buttons = {}
        self.amount_entries = {}
        self.clear_buttons = {}
        self.all_buttons = {}
        
        # 投注控制区域组件
        self.control_frame = None
        self.bet_unit_label = None
        self.bet_unit_buttons = {}
        self.multiple_label = None
        self.multiple_entry = None
        self.multiple_menu = None
        self.multiple_button = None
        self.total_amount_label = None
        self.confirm_button = None
        self.place_bet_button = None
        
        # 状态信息区域组件
        self.status_frame = None
        self.status_text = None
        
        # 投注记录区域组件
        self.history_frame = None
        self.history_tree = None
        self.history_scrollbar = None
        
        # 初始化UI
        self.init_tab_content()
    
    def init_tab_content(self):
        """初始化选项卡内容"""
        # 创建主要容器
        self.main_paned = ttk.PanedWindow(self.tab, orient=tk.HORIZONTAL)
        self.main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 左侧面板 - 账号信息和投注界面
        self.left_frame = ttk.Frame(self.main_paned)
        self.main_paned.add(self.left_frame, weight=5)  # 增加左侧面板的权重
        
        # 右侧面板 - 状态信息
        self.right_frame = ttk.Frame(self.main_paned)
        self.main_paned.add(self.right_frame, weight=1)  # 保持右侧面板的权重不变
        
        # 初始化左侧面板
        self.init_left_panel()
        
        # 初始化右侧面板
        self.init_right_panel()
    
    def init_left_panel(self):
        """初始化左侧面板"""
        # 游戏信息框架
        self.game_info_frame = ttk.LabelFrame(self.left_frame, text="游戏信息", padding=10)
        self.game_info_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 游戏信息布局
        game_info_grid = ttk.Frame(self.game_info_frame)
        game_info_grid.pack(fill=tk.X)
        
        # 游戏图标
        self.game_icon_label = ttk.Label(game_info_grid)
        self.game_icon_label.grid(row=0, column=0, rowspan=2, padx=5, pady=5)
        
        # 游戏名称
        game_name_frame = ttk.Frame(game_info_grid)
        game_name_frame.grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        ttk.Label(game_name_frame, text="游戏名称:").pack(side=tk.LEFT)
        self.game_name_label = ttk.Label(game_name_frame, text="--")
        self.game_name_label.pack(side=tk.LEFT, padx=5)
        
        # 当前期号
        game_period_frame = ttk.Frame(game_info_grid)
        game_period_frame.grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)
        ttk.Label(game_period_frame, text="当前期号:").pack(side=tk.LEFT)
        self.game_period_label = ttk.Label(game_period_frame, text="--")
        self.game_period_label.pack(side=tk.LEFT, padx=5)
        
        # 期号状态
        game_status_frame = ttk.Frame(game_info_grid)
        game_status_frame.grid(row=0, column=2, sticky=tk.W, padx=5, pady=2)
        ttk.Label(game_status_frame, text="状态:").pack(side=tk.LEFT)
        self.game_status_label = ttk.Label(game_status_frame, text="--")
        self.game_status_label.pack(side=tk.LEFT, padx=5)
        
        # 倒计时
        countdown_frame = ttk.Frame(game_info_grid)
        countdown_frame.grid(row=1, column=2, sticky=tk.W, padx=5, pady=2)
        ttk.Label(countdown_frame, text="倒计时:").pack(side=tk.LEFT)
        self.countdown_label = ttk.Label(countdown_frame, text="00:00:00", font=("Arial", 10, "bold"))
        self.countdown_label.pack(side=tk.LEFT, padx=5)
        
        # 刷新按钮
        self.refresh_button = ttk.Button(game_info_grid, text="刷新")
        self.refresh_button.grid(row=0, column=3, rowspan=2, padx=5, pady=5)
        
        # 试玩账号信息框架
        self.account_frame = ttk.LabelFrame(self.left_frame, text="试玩账号信息", padding=10)
        self.account_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 账号信息布局
        account_grid = ttk.Frame(self.account_frame)
        account_grid.pack(fill=tk.X)
        
        # 账号
        account_label_frame = ttk.Frame(account_grid)
        account_label_frame.grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Label(account_label_frame, text="账号:").pack(side=tk.LEFT)
        self.account_label = ttk.Label(account_label_frame, text="未登录")
        self.account_label.pack(side=tk.LEFT, padx=5)
        
        # 余额
        balance_frame = ttk.Frame(account_grid)
        balance_frame.grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Label(balance_frame, text="余额:").pack(side=tk.LEFT)
        self.balance_label = ttk.Label(balance_frame, text="0.00")
        self.balance_label.pack(side=tk.LEFT, padx=5)
        
        # 刷新余额按钮
        self.refresh_balance_button = ttk.Button(account_grid, text="刷新余额")
        self.refresh_balance_button.grid(row=0, column=1, padx=5, pady=2)
        
        # 自动刷新按钮
        self.auto_refresh_button = ttk.Button(account_grid, text="自动刷新: 关")
        self.auto_refresh_button.grid(row=1, column=1, padx=5, pady=2)
        
        # 获取试玩账号按钮
        self.get_trial_button = ttk.Button(account_grid, text="获取试玩账号")
        self.get_trial_button.grid(row=0, column=2, rowspan=2, padx=5, pady=2)
        
        # 投注界面框架
        self.betting_frame = ttk.LabelFrame(self.left_frame, text="投注界面", padding=10)
        self.betting_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 位置选择区域
        position_frame = ttk.Frame(self.betting_frame)
        position_frame.pack(fill=tk.X, pady=5)
        
        # 创建位置按钮
        for i, position in enumerate(self.positions):
            button = ttk.Button(position_frame, text=position, width=8)
            button.grid(row=0, column=i, padx=2, pady=2)
            self.position_buttons[position] = button
        
        # 号码选择区域
        number_container = ttk.Frame(self.betting_frame)
        number_container.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 为每个位置创建号码选择框架
        for position in self.positions:
            frame = ttk.Frame(number_container)
            self.number_frames[position] = frame
            
            # 号码按钮区域
            number_grid = ttk.Frame(frame)
            number_grid.pack(fill=tk.BOTH, expand=True, pady=5)
            
            # 创建号码按钮 (1-10)
            self.number_buttons[position] = {}
            for i in range(10):
                row, col = divmod(i, 5)
                number = i + 1
                button = ttk.Button(number_grid, text=str(number), width=5)
                button.grid(row=row, column=col, padx=5, pady=5)
                self.number_buttons[position][number] = button
            
            # 控制区域
            control_grid = ttk.Frame(frame)
            control_grid.pack(fill=tk.X, pady=5)
            
            # 金额输入
            ttk.Label(control_grid, text="金额:").grid(row=0, column=0, padx=5, pady=5)
            amount_var = tk.StringVar(value="1")
            amount_entry = ttk.Entry(control_grid, textvariable=amount_var, width=8)
            amount_entry.grid(row=0, column=1, padx=5, pady=5)
            self.amount_entries[position] = amount_entry
            
            # 清除按钮
            clear_button = ttk.Button(control_grid, text="清除")
            clear_button.grid(row=0, column=2, padx=5, pady=5)
            self.clear_buttons[position] = clear_button
            
            # 全选按钮
            all_button = ttk.Button(control_grid, text="全选")
            all_button.grid(row=0, column=3, padx=5, pady=5)
            self.all_buttons[position] = all_button
        
        # 投注控制区域
        self.control_frame = ttk.Frame(self.betting_frame)
        self.control_frame.pack(fill=tk.X, pady=10)
        
        # 投注单位
        unit_frame = ttk.Frame(self.control_frame)
        unit_frame.pack(side=tk.LEFT, padx=10)
        
        self.bet_unit_label = ttk.Label(unit_frame, text="单位:")
        self.bet_unit_label.pack(side=tk.LEFT)
        
        # 元角分按钮
        self.bet_unit_buttons = {}
        for unit, text in [("1", "元"), ("0.1", "角"), ("0.01", "分")]:
            button = ttk.Button(unit_frame, text=text, width=5)
            button.pack(side=tk.LEFT, padx=2)
            self.bet_unit_buttons[unit] = button
        
        # 倍数
        multiple_frame = ttk.Frame(self.control_frame)
        multiple_frame.pack(side=tk.LEFT, padx=10)
        
        self.multiple_label = ttk.Label(multiple_frame, text="倍数:")
        self.multiple_label.pack(side=tk.LEFT)
        
        multiple_var = tk.StringVar(value="1")
        self.multiple_entry = ttk.Entry(multiple_frame, textvariable=multiple_var, width=5)
        self.multiple_entry.pack(side=tk.LEFT, padx=2)
        
        self.multiple_button = ttk.Button(multiple_frame, text="▼", width=2)
        self.multiple_button.pack(side=tk.LEFT)
        
        # 创建倍数菜单
        self.multiple_menu = tk.Menu(self.root, tearoff=0)
        for value in ["1", "5", "10", "15", "20", "50", "100", "200", "500", "1000", "2000"]:
            self.multiple_menu.add_command(label=value)
        
        # 总金额
        total_frame = ttk.Frame(self.control_frame)
        total_frame.pack(side=tk.LEFT, padx=10)
        
        ttk.Label(total_frame, text="总金额:").pack(side=tk.LEFT)
        
        total_amount_var = tk.StringVar(value="0")
        self.total_amount_label = ttk.Label(total_frame, textvariable=total_amount_var, font=("Arial", 10, "bold"))
        self.total_amount_label.pack(side=tk.LEFT, padx=2)
        
        ttk.Label(total_frame, text="元").pack(side=tk.LEFT)
        
        # 确认和投注按钮
        button_frame = ttk.Frame(self.control_frame)
        button_frame.pack(side=tk.RIGHT, padx=10)
        
        self.confirm_button = ttk.Button(button_frame, text="确认选号")
        self.confirm_button.pack(side=tk.LEFT, padx=5)
        
        self.place_bet_button = ttk.Button(button_frame, text="立即投注")
        self.place_bet_button.pack(side=tk.LEFT, padx=5)
    
    def init_right_panel(self):
        """初始化右侧面板"""
        # 状态信息框架
        self.status_frame = ttk.LabelFrame(self.right_frame, text="状态信息", padding=5)
        self.status_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 状态文本区域
        self.status_text = scrolledtext.ScrolledText(self.status_frame, wrap=tk.WORD, width=30, height=10)
        self.status_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.status_text.config(state=tk.DISABLED)  # 设置为只读
        
        # 投注记录框架
        self.history_frame = ttk.LabelFrame(self.right_frame, text="投注记录", padding=5)
        self.history_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建投注记录表格
        self.create_order_history_table()
    
    def create_order_history_table(self):
        """创建投注记录表格"""
        # 创建表格和滚动条
        table_frame = ttk.Frame(self.history_frame)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建垂直滚动条
        self.history_scrollbar = ttk.Scrollbar(table_frame)
        self.history_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 创建表格
        columns = ("期号", "金额", "状态", "时间")
        self.history_tree = ttk.Treeview(
            table_frame, 
            columns=columns, 
            show="headings", 
            height=15,
            yscrollcommand=self.history_scrollbar.set
        )
        
        # 设置列宽和对齐方式
        self.history_tree.column("期号", width=80, anchor=tk.CENTER)
        self.history_tree.column("金额", width=60, anchor=tk.CENTER)
        self.history_tree.column("状态", width=60, anchor=tk.CENTER)
        self.history_tree.column("时间", width=120, anchor=tk.CENTER)
        
        # 设置表头
        for col in columns:
            self.history_tree.heading(col, text=col)
        
        # 放置表格
        self.history_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 配置滚动条
        self.history_scrollbar.config(command=self.history_tree.yview)
    
    def update_status(self, message):
        """
        更新状态信息显示
        
        Args:
            message: 状态消息
        """
        self.status_text.config(state=tk.NORMAL)
        self.status_text.insert(tk.END, f"{message}\n")
        self.status_text.see(tk.END)
        self.status_text.config(state=tk.DISABLED)
    
    def clear_status(self):
        """清空状态信息显示"""
        self.status_text.config(state=tk.NORMAL)
        self.status_text.delete(1.0, tk.END)
        self.status_text.config(state=tk.DISABLED)
    
    def set_game_info(self, game_info):
        """
        设置游戏信息
        
        Args:
            game_info: 游戏信息字典
        """
        self.game_name_label.config(text=game_info.get('game_name', '--'))
        self.game_period_label.config(text=game_info.get('current_period', '--'))
        self.game_status_label.config(text=game_info.get('period_status', '--'))
        self.countdown_label.config(text=game_info.get('remaining_time_str', '00:00:00'))
    
    def set_account_info(self, account, balance):
        """
        设置账号信息
        
        Args:
            account: 账号名称
            balance: 账号余额
        """
        self.account_label.config(text=account)
        self.balance_label.config(text=balance)
    
    def set_auto_refresh_button_text(self, is_auto_refresh):
        """
        设置自动刷新按钮文本
        
        Args:
            is_auto_refresh: 是否自动刷新
        """
        text = "自动刷新: 开" if is_auto_refresh else "自动刷新: 关"
        self.auto_refresh_button.config(text=text)
    
    def select_position(self, position):
        """
        选择位置
        
        Args:
            position: 位置名称
        """
        # 重置所有位置按钮样式
        for pos, button in self.position_buttons.items():
            button.state(['!pressed'])
        
        # 设置选中位置按钮样式
        self.position_buttons[position].state(['pressed'])
        
        # 显示对应位置的号码选择框架
        for pos, frame in self.number_frames.items():
            frame.pack_forget()
        self.number_frames[position].pack(fill=tk.BOTH, expand=True)
    
    def update_number_button_style(self, position, number, is_selected):
        """
        更新号码按钮样式
        
        Args:
            position: 位置名称
            number: 号码
            is_selected: 是否选中
        """
        if is_selected:
            self.number_buttons[position][number].state(['pressed'])
        else:
            self.number_buttons[position][number].state(['!pressed'])
    
    def set_total_amount(self, amount):
        """
        设置总金额
        
        Args:
            amount: 总金额
        """
        self.total_amount_label.config(text=f"{amount:.2f}")
    
    def clear_history_table(self):
        """清空投注记录表格"""
        for item in self.history_tree.get_children():
            self.history_tree.delete(item)
    
    def add_history_item(self, period, amount, status, time):
        """
        添加投注记录项
        
        Args:
            period: 期号
            amount: 金额
            status: 状态
            time: 时间
        """
        self.history_tree.insert("", 0, values=(period, amount, status, time))
    
    def set_game_icon(self, icon_data):
        """
        设置游戏图标
        
        Args:
            icon_data: 图标数据
        """
        try:
            if icon_data:
                # 创建图片对象
                self.game_icon_image = Image.open(io.BytesIO(icon_data))
                self.game_icon_image = self.game_icon_image.resize((40, 40), Image.LANCZOS)
                self.game_icon_photo = ImageTk.PhotoImage(self.game_icon_image)
                self.game_icon_label.config(image=self.game_icon_photo)
            else:
                self.game_icon_label.config(image="")
        except Exception as e:
            print(f"设置游戏图标时出错: {str(e)}")
            self.game_icon_label.config(image="")
