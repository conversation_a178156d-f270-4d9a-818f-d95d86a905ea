#!/usr/bin/env python
# -*- coding: utf-8 -*-

from flask import Flask, jsonify, request
import time
import random
from datetime import datetime, timedelta

app = Flask(__name__)

# 模拟游戏数据
current_period = int(time.time() * 1000)
next_period = current_period + 300000  # 5分钟一期（毫秒）

# 模拟历史开奖记录
history_records = [
    {"period": current_period - 300000, "numbers": "01,02,03,04,05,06,07,08,09,10"},
    {"period": current_period - 600000, "numbers": "10,09,08,07,06,05,04,03,02,01"},
]

@app.route('/lotData/getNewPeriod.do', methods=['GET'])
def get_new_period():
    """
    获取最新期号信息（转发真实API）
    """
    import requests
    REAL_BASE_URL = "https://jk7859.jvvpbv2580513aknu.com:59789"
    try:
        # 直接转发GET请求到真实API
        resp = requests.get(f"{REAL_BASE_URL}/lotData/getNewPeriod.do?code=JSSC168", verify=False, timeout=8)
        return (resp.content, resp.status_code, resp.headers.items())
    except Exception as e:
        return jsonify({"code": 500, "msg": f"转发真实API失败: {e}", "data": None}), 500

@app.route('/bet/placeBet.do', methods=['POST'])
def place_bet():
    """
    投注接口（转发真实API）
    """
    import requests
    REAL_BASE_URL = "https://jk7859.jvvpbv2580513aknu.com:59789"
    try:
        # 直接转发POST请求到真实API，保持form参数一致
        resp = requests.post(f"{REAL_BASE_URL}/bet/placeBet.do", data=request.form, verify=False, timeout=8)
        return (resp.content, resp.status_code, resp.headers.items())
    except Exception as e:
        return jsonify({"code": 500, "msg": f"转发真实API失败: {e}", "data": None}), 500

@app.route('/user/getAccountInfo.do', methods=['GET'])
def get_account_info():
    """
    获取账户信息（转发真实API）
    """
    import requests
    REAL_BASE_URL = "https://jk7859.jvvpbv2580513aknu.com:59789"
    try:
        resp = requests.get(f"{REAL_BASE_URL}/user/getAccountInfo.do", cookies=request.cookies, verify=False, timeout=8)
        return (resp.content, resp.status_code, resp.headers.items())
    except Exception as e:
        return jsonify({"code": 500, "msg": f"转发真实API失败: {e}", "data": None}), 500

@app.route('/')
def home():
    """首页"""
    return """
    <html>
    <head>
        <title>模拟游戏服务器</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
            h1 { color: #333; }
            .endpoint { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 5px; }
            .url { color: #007bff; }
            pre { background: #eee; padding: 10px; border-radius: 5px; overflow: auto; }
        </style>
    </head>
    <body>
        <h1>游戏模拟服务器</h1>
        <p>以下是可用的API端点:</p>
        
        <div class="endpoint">
            <h3>获取最新期号信息</h3>
            <p class="url">GET /lotData/getNewPeriod.do</p>
            <a href="/lotData/getNewPeriod.do">点击测试</a>
        </div>
        
        <div class="endpoint">
            <h3>投注</h3>
            <p class="url">POST /bet/placeBet.do</p>
            <p>参数:</p>
            <ul>
                <li>period: 期号</li>
                <li>betNumber: 投注号码</li>
                <li>betAmount: 投注金额</li>
                <li>betCount: 注数</li>
            </ul>
        </div>
        
        <div class="endpoint">
            <h3>获取账户信息</h3>
            <p class="url">GET /user/getAccountInfo.do</p>
            <a href="/user/getAccountInfo.do">点击测试</a>
        </div>
    </body>
    </html>
    """

if __name__ == '__main__':
    print("启动模拟服务器...")
    print(f"当前期号: {current_period}")
    print(f"下一期号: {next_period}")
    print("API 端点:")
    print("  GET  /lotData/getNewPeriod.do - 获取最新期号信息")
    print("  POST /bet/placeBet.do - 提交投注")
    print("  GET  /user/getAccountInfo.do - 获取账户信息")
    print("\n访问 http://localhost:5000 查看API文档")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
