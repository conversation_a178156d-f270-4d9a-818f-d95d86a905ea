#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
投注记录模块
处理投注记录的显示和管理
"""

import tkinter as tk
from tkinter import ttk
from utils.logger import Logger

class BetRecords:
    def __init__(self, parent, betting_logic):
        """初始化投注记录显示
        
        Args:
            parent: 父级容器
            betting_logic: 投注逻辑对象
        """
        # 创建日志记录器
        self.logger = Logger("BetRecords")
        self.parent = parent
        self.betting_logic = betting_logic
        
        # 投注记录框架
        self.records_frame = None
        
        # 投注记录表格
        self.records_tree = None
        
        # 初始化UI
        self.init_ui()
        
        # 注册投注回调
        self.betting_logic.register_bet_callback(self.on_bet_completed)
    
    def init_ui(self):
        """初始化投注记录UI"""
        # 创建投注记录框架
        self.records_frame = ttk.LabelFrame(self.parent, text="投注记录", padding=10)
        self.records_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建表格
        columns = ("period", "numbers", "multiplier", "unit", "amount", "time", "status")
        self.records_tree = ttk.Treeview(self.records_frame, columns=columns, show="headings")
        
        # 设置列标题
        self.records_tree.heading("period", text="期号")
        self.records_tree.heading("numbers", text="投注号码")
        self.records_tree.heading("multiplier", text="倍数")
        self.records_tree.heading("unit", text="单位")
        self.records_tree.heading("amount", text="金额")
        self.records_tree.heading("time", text="投注时间")
        self.records_tree.heading("status", text="状态")
        
        # 设置列宽
        self.records_tree.column("period", width=100)
        self.records_tree.column("numbers", width=150)
        self.records_tree.column("multiplier", width=50)
        self.records_tree.column("unit", width=50)
        self.records_tree.column("amount", width=80)
        self.records_tree.column("time", width=150)
        self.records_tree.column("status", width=80)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(self.records_frame, orient=tk.VERTICAL, command=self.records_tree.yview)
        self.records_tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.records_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 添加操作按钮
        button_frame = ttk.Frame(self.records_frame)
        button_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(button_frame, text="刷新", command=self.refresh_records).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="清空", command=self.clear_records).pack(side=tk.LEFT, padx=5)
        
        self.logger.info("投注记录UI初始化完成")
    
    def refresh_records(self):
        """刷新投注记录"""
        # 清空表格
        for item in self.records_tree.get_children():
            self.records_tree.delete(item)
        
        # 获取最新记录
        records = self.betting_logic.get_bet_records()
        
        # 更新投注结果
        self.betting_logic.update_bet_results()
        
        # 添加到表格
        for record in records:
            values = (
                record['period'],
                ','.join(record['numbers']),
                record['multiplier'],
                record['unit'],
                f"{record['amount']:.2f}",
                record['time'],
                record['status']
            )
            self.records_tree.insert('', tk.END, values=values)
        
        self.logger.info(f"刷新投注记录，共{len(records)}条")
    
    def clear_records(self):
        """清空投注记录"""
        # 清空表格
        for item in self.records_tree.get_children():
            self.records_tree.delete(item)
        
        # 清空记录
        self.betting_logic.clear_bet_records()
        
        self.logger.info("已清空投注记录")
    
    def on_bet_completed(self, success, data):
        """投注完成回调
        
        Args:
            success: 投注是否成功
            data: 投注数据
        """
        if success:
            # 刷新记录
            self.refresh_records()
    
    def update_bet_results(self):
        """更新投注结果"""
        self.betting_logic.update_bet_results()
        self.refresh_records()
    
    def cleanup(self):
        """清理资源"""
        # 取消注册投注回调
        self.betting_logic.unregister_bet_callback(self.on_bet_completed)
        
        self.logger.info("投注记录资源已清理")
