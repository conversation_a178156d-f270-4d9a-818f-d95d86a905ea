#!/usr/bin/env python
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin
from utils import debug_log, BASE_URL, register_status_callback, unregister_status_callback
import datetime
import threading
import time
import json
import re
import webbrowser
from PIL import Image, ImageTk
import io
import random

class TrialTab:
    def __init__(self, parent, notebook, app, root=None):
        """初始化试玩选项卡"""
        self.parent = parent
        self.notebook = notebook
        self.app = app
        self.session = app.session
        self.root = root if root is not None else parent
        
        # 注册状态更新回调
        register_status_callback(self.update_status_from_log)
        
        # 创建试玩选项卡
        self.tab = ttk.Frame(notebook)
        notebook.add(self.tab, text="试玩")
        
        # 账号信息
        self.account = ""
        self.balance = "0"
        self.has_loaded_trial = False  # 标记是否已加载试玩账号
        
        # 游戏信息
        self.game_name = "极速赛车"
        self.game_code = "JSSC168"
        self.period = "--"
        self.status = "--"
        self.countdown = "--:--"
        self.last_result = "--"
        
        # 投注信息
        self.positions = ["冠军", "亚军", "季军", "第四", "第五", "第六", "第七", "第八", "第九", "第十"]
        
        # 默认选中的位置
        self.selected_position = "冠军"  # 默认选中冠军
        
        # 每个位置的选中号码和投注金额
        self.position_data = {}
        for position in self.positions:
            self.position_data[position] = {
                "selected_numbers": [],
                "amount": tk.StringVar(value="1")
            }
        
        # 总投注金额
        self.total_amount = tk.StringVar(value="0")
        
        # 设置默认投注模式为元
        self.bet_unit = tk.StringVar(value="1")
        self.bet_unit_text = tk.StringVar(value="元")
        
        # 设置默认倍数为1
        self.multiple = tk.StringVar(value="1")
        self.multiple_options = ["1", "5", "10", "15", "20", "50", "100", "200", "500", "1000", "2000"]
        
        # 自动刷新相关
        self._auto_refresh_thread = None
        self._auto_refresh_running = False
        self.refresh_interval = 30  # 自动刷新间隔（秒）
        
        # API更新线程
        self._api_update_thread = None
        self._api_update_running = False
        self.api_update_interval = 5  # API更新间隔（秒）
        
        # 日志记录
        self.logger = debug_log
        
        # 初始化选项卡内容
        self.init_tab_content()
        
        # 监听选项卡切换
        self.notebook.bind("<<NotebookTabChanged>>", self.on_tab_changed)
    
    def init_tab_content(self):
        """初始化选项卡内容"""
        # 创建主容器（水平分隔）
        self.main_paned = ttk.PanedWindow(self.tab, orient=tk.HORIZONTAL)
        self.main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 左侧面板（投注区域）
        self.left_frame = ttk.Frame(self.main_paned)
        self.main_paned.add(self.left_frame, weight=5)
        
        # 右侧面板（状态信息区域）
        self.right_frame = ttk.Frame(self.main_paned)
        self.main_paned.add(self.right_frame, weight=1)
        
        # 初始化左侧面板
        self.init_left_panel()
        
        # 初始化右侧面板
        self.init_right_panel()
        
        # 选择默认位置
        self.select_position(self.selected_position)
        
        # 启动API游戏信息更新线程
        self._start_api_update_task()

    def init_left_panel(self):
        """初始化左侧面板"""
        # 游戏信息区域
        self.game_info_frame = ttk.LabelFrame(self.left_frame, text="游戏信息")
        self.game_info_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 游戏信息内容
        game_info_content = ttk.Frame(self.game_info_frame)
        game_info_content.pack(fill=tk.X, padx=5, pady=5)
        
        # 游戏图标和名称
        game_header = ttk.Frame(game_info_content)
        game_header.pack(fill=tk.X, pady=2)
        
        self.game_icon_label = ttk.Label(game_header)
        self.game_icon_label.pack(side=tk.LEFT, padx=5)
        
        self.game_name_label = ttk.Label(game_header, text=self.game_name, font=("宋体", 12, "bold"))
        self.game_name_label.pack(side=tk.LEFT, padx=5)
        
        refresh_button = ttk.Button(game_header, text="刷新", width=8, command=lambda: self.update_game_info_from_api(manual_refresh=True))
        refresh_button.pack(side=tk.RIGHT, padx=5)
        
        # 期号和状态
        period_frame = ttk.Frame(game_info_content)
        period_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(period_frame, text="期号:").pack(side=tk.LEFT, padx=5)
        self.period_label = ttk.Label(period_frame, text=self.period)
        self.period_label.pack(side=tk.LEFT, padx=5)
        
        ttk.Label(period_frame, text="状态:").pack(side=tk.LEFT, padx=5)
        self.status_label = ttk.Label(period_frame, text=self.status)
        self.status_label.pack(side=tk.LEFT, padx=5)
        
        # 倒计时
        countdown_frame = ttk.Frame(game_info_content)
        countdown_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(countdown_frame, text="倒计时:").pack(side=tk.LEFT, padx=5)
        self.countdown_label = ttk.Label(countdown_frame, text=self.countdown, font=("宋体", 10, "bold"))
        self.countdown_label.pack(side=tk.LEFT, padx=5)
        
        # 上期开奖号码
        last_result_frame = ttk.Frame(game_info_content)
        last_result_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(last_result_frame, text="上期开奖号码:").pack(side=tk.LEFT, padx=5)
        self.last_result_label = ttk.Label(last_result_frame, text=self.last_result)
        self.last_result_label.pack(side=tk.LEFT, padx=5)
        
        # 账号信息区域
        self.account_frame = ttk.LabelFrame(self.left_frame, text="试玩账号信息")
        self.account_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 账号信息内容
        account_content = ttk.Frame(self.account_frame)
        account_content.pack(fill=tk.X, padx=5, pady=5)
        
        # 账号和余额
        account_info = ttk.Frame(account_content)
        account_info.pack(fill=tk.X, pady=2)
        
        ttk.Label(account_info, text="账号:").pack(side=tk.LEFT, padx=5)
        self.account_label = ttk.Label(account_info, text="未登录")
        self.account_label.pack(side=tk.LEFT, padx=5)
        
        ttk.Label(account_info, text="余额:").pack(side=tk.LEFT, padx=5)
        self.balance_label = ttk.Label(account_info, text="0")
        self.balance_label.pack(side=tk.LEFT, padx=5)
        
        # 按钮区域
        button_frame = ttk.Frame(account_content)
        button_frame.pack(fill=tk.X, pady=2)
        
        self.get_trial_button = ttk.Button(button_frame, text="获取试玩账号", width=15, command=self.confirm_get_trial)
        self.get_trial_button.pack(side=tk.LEFT, padx=5)
        
        self.refresh_balance_button = ttk.Button(button_frame, text="刷新余额", width=10, command=self.refresh_balance)
        self.refresh_balance_button.pack(side=tk.LEFT, padx=5)
        
        self.auto_refresh_var = tk.BooleanVar(value=False)
        self.auto_refresh_button = ttk.Checkbutton(button_frame, text="自动刷新", variable=self.auto_refresh_var, command=self.toggle_auto_refresh)
        self.auto_refresh_button.pack(side=tk.LEFT, padx=5)
        
        self.game_button = ttk.Button(button_frame, text="进入游戏", width=10, command=self.go_to_game)
        self.game_button.pack(side=tk.RIGHT, padx=5)
        
        # 投注区域
        self.bet_frame = ttk.LabelFrame(self.left_frame, text="投注")
        self.bet_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 投注区域内容
        bet_content = ttk.Frame(self.bet_frame)
        bet_content.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 位置选择区域
        position_frame = ttk.Frame(bet_content)
        position_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(position_frame, text="位置:").pack(side=tk.LEFT, padx=5)
        
        # 位置选择按钮
        self.position_buttons = {}
        position_buttons_frame = ttk.Frame(position_frame)
        position_buttons_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        for i, position in enumerate(self.positions):
            btn = ttk.Button(position_buttons_frame, text=position, width=6, 
                             command=lambda pos=position: self.select_position(pos))
            btn.grid(row=i//5, column=i%5, padx=2, pady=2)
            self.position_buttons[position] = btn
        
        # 号码选择区域
        numbers_frame = ttk.LabelFrame(bet_content, text="选号")
        numbers_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 号码按钮区域
        self.number_buttons = {}
        numbers_grid = ttk.Frame(numbers_frame)
        numbers_grid.pack(padx=5, pady=5)
        
        # 创建10行1列的号码按钮网格
        for i in range(10):
            for j in range(10):
                num = i * 10 + j + 1
                if num > 10:
                    break
                num_str = f"{num:02d}"  # 格式化为两位数字
                btn = ttk.Button(numbers_grid, text=num_str, width=4,
                                command=lambda n=num_str: self.toggle_number(n))
                btn.grid(row=i, column=j, padx=2, pady=2)
                self.number_buttons[num_str] = btn
        
        # 全选和清除按钮
        select_buttons_frame = ttk.Frame(numbers_frame)
        select_buttons_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(select_buttons_frame, text="全选", width=10,
                  command=self.select_all_numbers).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(select_buttons_frame, text="清除", width=10,
                  command=self.clear_numbers).pack(side=tk.LEFT, padx=5)
        
        # 投注金额区域
        amount_frame = ttk.Frame(bet_content)
        amount_frame.pack(fill=tk.X, pady=5)
        
        # 单位选择
        unit_frame = ttk.Frame(amount_frame)
        unit_frame.pack(side=tk.LEFT, padx=5)
        
        ttk.Label(unit_frame, text="单位:").pack(side=tk.LEFT)
        
        # 元角分单选按钮
        unit_buttons_frame = ttk.Frame(unit_frame)
        unit_buttons_frame.pack(side=tk.LEFT)
        
        ttk.Radiobutton(unit_buttons_frame, text="元", variable=self.bet_unit, value="1",
                       command=lambda: self.set_bet_unit("1", "元")).pack(side=tk.LEFT)
        
        ttk.Radiobutton(unit_buttons_frame, text="角", variable=self.bet_unit, value="0.1",
                       command=lambda: self.set_bet_unit("0.1", "角")).pack(side=tk.LEFT)
        
        ttk.Radiobutton(unit_buttons_frame, text="分", variable=self.bet_unit, value="0.01",
                       command=lambda: self.set_bet_unit("0.01", "分")).pack(side=tk.LEFT)
        
        # 倍数设置
        multiple_frame = ttk.Frame(amount_frame)
        multiple_frame.pack(side=tk.LEFT, padx=10)
        
        ttk.Label(multiple_frame, text="倍数:").pack(side=tk.LEFT)
        
        self.multiple_entry = ttk.Entry(multiple_frame, width=6, textvariable=self.multiple)
        self.multiple_entry.pack(side=tk.LEFT, padx=2)
        self.multiple_entry.bind("<KeyRelease>", self.update_multiple)
        
        # 倍数选择菜单
        self.multiple_menu = tk.Menu(self.root, tearoff=0)
        for option in self.multiple_options:
            self.multiple_menu.add_command(
                label=option,
                command=lambda val=option: self.set_multiple(val)
            )
        
        # 倍数下拉按钮
        ttk.Button(multiple_frame, text="↓", width=2,
                  command=lambda: self.multiple_menu.post(
                      self.multiple_entry.winfo_rootx(),
                      self.multiple_entry.winfo_rooty() + self.multiple_entry.winfo_height()
                  )).pack(side=tk.LEFT)
        
        # 总金额显示
        total_frame = ttk.Frame(amount_frame)
        total_frame.pack(side=tk.RIGHT, padx=5)
        
        ttk.Label(total_frame, text="总金额:").pack(side=tk.LEFT)
        
        self.total_amount_label = ttk.Label(total_frame, textvariable=self.total_amount)
        self.total_amount_label.pack(side=tk.LEFT, padx=2)
        
        ttk.Label(total_frame, textvariable=self.bet_unit_text).pack(side=tk.LEFT)
        
        # 投注按钮区域
        bet_buttons_frame = ttk.Frame(bet_content)
        bet_buttons_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(bet_buttons_frame, text="确认选号", width=15,
                  command=self.confirm_selection).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(bet_buttons_frame, text="投注", width=15,
                  command=self.place_bet).pack(side=tk.RIGHT, padx=5)
        
        # 投注历史区域
        history_frame = ttk.LabelFrame(bet_content, text="投注历史")
        history_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 创建树形视图显示投注历史
        self.history_tree = ttk.Treeview(history_frame, columns=("period", "amount", "status", "time"),
                                       show="headings", height=5)
        
        # 设置列标题
        self.history_tree.heading("period", text="期号")
        self.history_tree.heading("amount", text="金额")
        self.history_tree.heading("status", text="状态")
        self.history_tree.heading("time", text="时间")
        
        # 设置列宽度
        self.history_tree.column("period", width=100)
        self.history_tree.column("amount", width=80)
        self.history_tree.column("status", width=80)
        self.history_tree.column("time", width=120)
        
        # 添加滚动条
        history_scrollbar = ttk.Scrollbar(history_frame, orient="vertical", command=self.history_tree.yview)
        self.history_tree.configure(yscrollcommand=history_scrollbar.set)
        
        # 放置树形视图和滚动条
        self.history_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        history_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定双击事件显示详情
        self.history_tree.bind("<Double-1>", self.show_bet_details)
        
        # 位置按钮
        self.position_buttons = {}
        for i, position in enumerate(self.positions):
            style_name = f"Position.TButton.{position}"
            ttk.Style().configure(style_name, font=("宋体", 9))
            
            button = ttk.Button(position_frame, text=position, width=8, style=style_name,
                             command=lambda pos=position: self.select_position(pos))
            button.grid(row=i//5, column=i%5, padx=2, pady=2)
            self.position_buttons[position] = button
        
        # 号码选择区域
        self.number_frames = {}
        self.number_buttons = {}
        
        for position in self.positions:
            frame = ttk.Frame(self.bet_frame)
            self.number_frames[position] = frame
            
            # 号码按钮
            buttons = {}
            for i in range(1, 11):  # 1-10号码
                number = str(i).zfill(2)  # 格式化为两位数
                button = ttk.Button(frame, text=number, width=4,
                                  command=lambda pos=position, num=number: self.toggle_number(pos, num))
                button.grid(row=0, column=i-1, padx=2, pady=2)
                buttons[number] = button
            self.number_buttons[position] = buttons
            
            # 操作按钮和金额输入
            control_frame = ttk.Frame(frame)
            control_frame.grid(row=1, column=0, columnspan=10, pady=5, sticky=tk.W)
            
            # 全选和清除按钮
            self.all_buttons = {}
            self.clear_buttons = {}
            
            all_button = ttk.Button(control_frame, text="全选", width=6,
                                 command=lambda pos=position: self.select_all_numbers(pos))
            all_button.pack(side=tk.LEFT, padx=2)
            self.all_buttons[position] = all_button
            
            clear_button = ttk.Button(control_frame, text="清除", width=6,
                                   command=lambda pos=position: self.clear_numbers(pos))
            clear_button.pack(side=tk.LEFT, padx=2)
            self.clear_buttons[position] = clear_button
            
            # 金额输入
            ttk.Label(control_frame, text="金额:").pack(side=tk.LEFT, padx=(10, 2))
            
            self.amount_entries = {}
            amount_entry = ttk.Entry(control_frame, width=8, textvariable=self.position_data[position]["amount"])
            amount_entry.pack(side=tk.LEFT, padx=2)
            self.amount_entries[position] = amount_entry
            
            # 绑定金额输入框事件
            amount_entry.bind("<FocusOut>", lambda event, pos=position: self.update_position_amount(pos))
            amount_entry.bind("<Return>", lambda event, pos=position: self.update_position_amount(pos))
        
        # 投注控制区域
        control_frame = ttk.Frame(self.bet_frame)
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 投注单位
        unit_frame = ttk.LabelFrame(control_frame, text="投注单位")
        unit_frame.pack(side=tk.LEFT, padx=5, fill=tk.Y)
        
        self.bet_unit_buttons = {}
        
        yuan_button = ttk.Radiobutton(unit_frame, text="元", value="1", variable=self.bet_unit,
                                   command=lambda: self.set_bet_unit("1", "元"))
        yuan_button.pack(side=tk.LEFT, padx=5, pady=2)
        self.bet_unit_buttons["1"] = yuan_button
        
        jiao_button = ttk.Radiobutton(unit_frame, text="角", value="0.1", variable=self.bet_unit,
                                    command=lambda: self.set_bet_unit("0.1", "角"))
        jiao_button.pack(side=tk.LEFT, padx=5, pady=2)
        self.bet_unit_buttons["0.1"] = jiao_button
        
        fen_button = ttk.Radiobutton(unit_frame, text="分", value="0.01", variable=self.bet_unit,
                                   command=lambda: self.set_bet_unit("0.01", "分"))
        fen_button.pack(side=tk.LEFT, padx=5, pady=2)
        self.bet_unit_buttons["0.01"] = fen_button
        
        # 倍数
        multiple_frame = ttk.LabelFrame(control_frame, text="倍数")
        multiple_frame.pack(side=tk.LEFT, padx=5, fill=tk.Y)
        
        self.multiple_entry = ttk.Entry(multiple_frame, width=8, textvariable=self.multiple)
        self.multiple_entry.pack(side=tk.LEFT, padx=5, pady=2)
        
        self.multiple_button = ttk.Button(multiple_frame, text="▼", width=2)
        self.multiple_button.pack(side=tk.LEFT, padx=5, pady=2)
        
        # 创建倍数下拉菜单
        self.multiple_menu = tk.Menu(self.multiple_button, tearoff=0)
        for value in self.multiple_options:
            self.multiple_menu.add_command(
                label=value,
                command=lambda val=value: self.set_multiple(val)
            )
        
        # 绑定倍数按钮事件
        self.multiple_button.bind("<Button-1>", self.show_multiple_menu)
        
        # 总金额和确认按钮
        total_frame = ttk.Frame(control_frame)
        total_frame.pack(side=tk.RIGHT, padx=5, fill=tk.Y)
        
        ttk.Label(total_frame, text="总金额:").pack(side=tk.LEFT, padx=2)
        
        total_amount_label = ttk.Label(total_frame, textvariable=self.total_amount, font=("宋体", 10, "bold"))
        total_amount_label.pack(side=tk.LEFT, padx=2)
        
        ttk.Label(total_frame, textvariable=self.bet_unit_text).pack(side=tk.LEFT)
        
        self.confirm_button = ttk.Button(total_frame, text="确认选号", width=10, command=self.confirm_selection)
        self.confirm_button.pack(side=tk.LEFT, padx=10)
        
        # 投注记录区域
        history_frame = ttk.LabelFrame(self.left_frame, text="投注记录")
        history_frame.pack(fill=tk.BOTH, padx=5, pady=5)
        
        # 创建表格
        columns = ("期号", "金额", "状态", "时间")
        self.history_tree = ttk.Treeview(history_frame, columns=columns, show="headings", height=5)
        
        # 设置列标题
        for col in columns:
            self.history_tree.heading(col, text=col)
            self.history_tree.column(col, width=100)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(history_frame, orient="vertical", command=self.history_tree.yview)
        self.history_tree.configure(yscrollcommand=scrollbar.set)
        
        # 放置表格和滚动条
        self.history_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定双击事件
        self.history_tree.bind("<Double-1>", self.show_bet_details)
    def init_right_panel(self):
        """初始化右侧面板"""
        # 创建状态信息标签框
        status_frame = ttk.LabelFrame(self.right_frame, text="状态信息")
        status_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建滚动文本区域
        self.status_text = scrolledtext.ScrolledText(status_frame, wrap=tk.WORD, width=40, height=30)
        self.status_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.status_text.config(state=tk.DISABLED)  # 设置为只读
    
    def update_status_from_log(self, message):
        """
        从日志更新状态显示
        
        Args:
            message: 日志消息
        """
        # 在UI线程中更新状态信息
        self.status_text.config(state=tk.NORMAL)
        self.status_text.insert(tk.END, f"{message}\n")
        self.status_text.see(tk.END)  # 滚动到最后
        self.status_text.config(state=tk.DISABLED)
    
    def _start_api_update_task(self):
        """启动API更新任务"""
        if self._api_update_thread is not None and self._api_update_thread.is_alive():
            return  # 已经在运行中
        
        self._api_update_running = True
        self._api_update_thread = threading.Thread(target=self._api_update_task, daemon=True)
        self._api_update_thread.start()
    
    def _stop_api_update_task(self):
        """停止API更新任务"""
        self._api_update_running = False
        if self._api_update_thread is not None:
            self._api_update_thread.join(timeout=1.0)
            self._api_update_thread = None
    
    def _api_update_task(self):
        """运行API更新任务"""
        while self._api_update_running:
            try:
                # 只有在当前选项卡被选中时才更新
                current_tab = self.notebook.select()
                if current_tab == str(self.tab):
                    self.update_game_info_from_api()
                
                # 等待5秒
                for _ in range(5):
                    if not self._api_update_running:
                        break
                    time.sleep(1)
            except Exception as e:
                self.logger.error(f"API更新任务异常: {str(e)}")
                time.sleep(5)  # 出错后等待5秒再重试
    
    def update_game_info_from_api(self, manual_refresh=False):
        """
        从API更新游戏信息
        
        Args:
            manual_refresh: 是否为手动刷新
        """
        try:
            # 构建API请求URL
            current_time = datetime.datetime.now()
            timestamp = int(current_time.timestamp() * 1000)  # 毫秒级时间戳
            
            api_url = f"{BASE_URL}/lotData/getNewPeriod.do?code=JSSC168&_={timestamp}"
            
            # 发送请求
            response = self.session.get(api_url)
            response.raise_for_status()
            
            # 解析JSON
            data = response.json()
            
            if data.get("success"):
                # 提取游戏信息
                game_data = data.get("data", {})
                
                # 更新游戏名称（只在手动刷新时更新）
                if manual_refresh and "fname" in game_data:
                    self.game_name_label.config(text=game_data.get("fname", "极速赛车"))
                
                # 更新期号
                period = game_data.get("fnumberofperiod", "--")
                self.period_label.config(text=period)
                
                # 更新状态
                status_code = game_data.get("fstatus", 0)
                status_text = "投注中" if status_code == 1 else "已封盘" if status_code == 2 else "未知"
                self.status_label.config(text=status_text)
                
                # 更新倒计时
                server_time = data.get("serverTime", 0)
                close_time = game_data.get("fclosetime", 0)
                
                if close_time > server_time:
                    remaining_seconds = (close_time - server_time) // 1000
                    minutes = remaining_seconds // 60
                    seconds = remaining_seconds % 60
                    self.countdown_label.config(text=f"{minutes:02d}:{seconds:02d}")
                else:
                    self.countdown_label.config(text="00:00")
                
                # 更新上期开奖号码
                prev_period = game_data.get("fpreviousperiod", "--")
                prev_result = game_data.get("fpreviousresult", "--")
                
                if prev_period and prev_result:
                    self.last_result_label.config(text=f"{prev_period}: {prev_result}")
                else:
                    self.last_result_label.config(text="--")
                
                # 如果是手动刷新，显示日志
                if manual_refresh:
                    self.logger.info(f"游戏信息已刷新: 期号={period}, 状态={status_text}")
            else:
                self.logger.error(f"获取游戏信息失败: {data.get('msg', '未知错误')}")
        
        except Exception as e:
            self.logger.error(f"更新游戏信息异常: {str(e)}")

    
    def on_account_updated(self, account, balance):
        """
        账号信息更新回调
        
        Args:
            account: 账号名称
            balance: 账号余额
        """
        # 直接更新UI显示
        self.account_label.config(text=account)
        self.balance_label.config(text=balance)
    
    def on_game_info_updated(self, game_info):
        """
        游戏信息更新回调
        
        Args:
            game_info: 游戏信息字典
        """
        # 直接更新UI显示
        if "game_name" in game_info:
            self.game_name_label.config(text=game_info["game_name"])
        if "period" in game_info:
            self.period_label.config(text=game_info["period"])
        if "status" in game_info:
            self.status_label.config(text=game_info["status"])
        if "countdown" in game_info:
            self.countdown_label.config(text=game_info["countdown"])
        if "last_result" in game_info:
            self.last_result_label.config(text=game_info["last_result"])
    
    def on_bet_result(self, success, message, result):
        """
        投注结果回调
        
        Args:
            success: 是否成功
            message: 结果消息
            result: 结果数据
        """
        if success:
            messagebox.showinfo("投注成功", message)
            # 刷新投注记录
            self.load_order_history()
        else:
            messagebox.showerror("投注失败", message)
    
    def update_status_from_log(self, message):
        """
        从日志更新状态显示
        
        Args:
            message: 日志消息
        """
        # 在UI线程中更新状态信息
        self.status_text.config(state=tk.NORMAL)
        self.status_text.insert(tk.END, f"{message}\n")
        self.status_text.see(tk.END)  # 滚动到最后
        self.status_text.config(state=tk.DISABLED)
    
    def refresh_balance(self):
        """刷新账号余额"""
        try:
            # 调用API获取账号信息
            self.logger.info("正在刷新账号余额...")
            # 在实际实现中添加账号刷新逻辑
        except Exception as e:
            self.logger.error(f"刷新账号余额失败: {str(e)}")
    
    def toggle_auto_refresh(self):
        """切换自动刷新状态"""
        try:
            self.auto_refresh = not self.auto_refresh
            text = "关闭自动刷新" if self.auto_refresh else "开启自动刷新"
            self.auto_refresh_button.config(text=text)
            self.logger.info(f"自动刷新已{'开启' if self.auto_refresh else '关闭'}")
            return self.auto_refresh
        except Exception as e:
            self.logger.error(f"切换自动刷新状态失败: {str(e)}")
            return False
    
    def on_bet_result(self, success, message, result):
        """
        处理投注结果
        
        Args:
            success: 是否成功
            message: 结果消息
            result: 结果数据
        """
        if success:
            messagebox.showinfo("投注成功", message)
            # 刷新投注记录
            self.load_order_history()
        else:
            messagebox.showerror("投注失败", message)
    
    def update_status_from_log(self, message):
        """
        从日志更新状态显示
        
        Args:
            message: 日志消息
        """
        try:
            # 在UI线程中安全地更新状态文本
            self.status_text.config(state=tk.NORMAL)
            self.status_text.insert(tk.END, f"{message}\n")
            self.status_text.see(tk.END)  # 滚动到底部
            self.status_text.config(state=tk.DISABLED)  # 设置为只读
        except Exception as e:
            print(f"更新状态显示失败: {str(e)}")
    
    def go_to_game(self):
        """进入游戏页面"""
        try:
            self.logger.info("正在进入游戏页面...")
            # 刷新游戏信息
            self.update_game_info_from_api(manual_refresh=True)
            # 启动API更新任务
            self._start_api_update_task()
            return True
        except Exception as e:
            self.logger.error(f"进入游戏页面失败: {str(e)}")
            return False
    
    def _start_api_update_task(self):
        """
        启动API更新任务
        """
        try:
            # 如果已经有线程在运行，先停止它
            if hasattr(self, '_api_update_thread') and self._api_update_thread is not None and self._api_update_thread.is_alive():
                self.logger.info("已有API更新任务在运行，无需重新启动")
                return
                
            # 设置运行标志
            self._api_update_running = True
            
            # 创建并启动线程
            import threading
            self._api_update_thread = threading.Thread(
                target=self._api_update_task,
                daemon=True,
                name="API更新线程"
            )
            self._api_update_thread.start()
            self.logger.info("API更新任务已启动")
        except Exception as e:
            self.logger.error(f"启动API更新任务失败: {str(e)}")
    
    def _stop_api_update_task(self):
        """
        安全停止API更新任务
        """
        try:
            # 设置停止标志
            self._api_update_running = False
            
            # 等待线程结束（最多等待2秒）
            if hasattr(self, '_api_update_thread') and self._api_update_thread is not None:
                import time
                start_time = time.time()
                while self._api_update_thread.is_alive() and time.time() - start_time < 2:
                    time.sleep(0.1)
                
                # 如果线程仍然在运行，记录日志
                if self._api_update_thread.is_alive():
                    self.logger.warning("API更新线程未能在规定时间内停止，将强制结束")
                else:
                    self.logger.info("API更新线程已成功停止")
                    
            return True
        except Exception as e:
            self.logger.error(f"停止API更新任务失败: {str(e)}")
            return False
    
    def _api_update_task(self):
        """
        API更新任务，在后台线程中运行
        """
        import time
        self.logger.info("API更新任务开始运行")
        
        try:
            while self._api_update_running:
                # 检查是否需要自动刷新
                if hasattr(self, 'auto_refresh') and self.auto_refresh:
                    try:
                        # 更新游戏信息
                        self.update_game_info_from_api(manual_refresh=False)
                    except Exception as e:
                        self.logger.error(f"自动更新游戏信息失败: {str(e)}")
                
                # 等待5秒
                time.sleep(5)
        except Exception as e:
            self.logger.error(f"API更新任务异常: {str(e)}")
        finally:
            self.logger.info("API更新任务已结束")
    
    def refresh_game_info(self):
        """刷新游戏信息"""
        try:
            # 手动刷新游戏信息
            self.update_game_info_from_api(manual_refresh=True)
            self.logger.info("游戏信息已刷新")
            return True
        except Exception as e:
            self.logger.error(f"刷新游戏信息失败: {str(e)}")
            return False
    
    def update_game_info_from_api(self, manual_refresh=False):
        """
        从 API 获取游戏信息
        
        Args:
            manual_refresh: 是否是手动刷新，如果是，会更新游戏名称
        """
        try:
            import requests
            import json
            import datetime
            import time
            
            # 生成毫秒级时间戳
            now = datetime.datetime.now()
            timestamp = int(now.timestamp() * 1000)
            formatted_time = now.strftime("%Y-%m-%d %H:%M:%S.%f")
            
            # 构建 API 请求URL
            api_url = f"/lotData/getNewPeriod.do?code=JSSC168&_={timestamp}"
            full_url = f"https://www.example.com{api_url}"  # 实际使用时替换为真实域名
            
            self.logger.info(f"请求游戏信息 API: {api_url}")
            self.logger.debug(f"当前时间: {formatted_time}, 时间戳: {timestamp}")
            
            # 模拟 API 响应（实际应用中应该发送真实请求）
            # 这里使用模拟数据代替真实请求
            mock_response = {
                "fnumberofperiod": f"********-{int(time.time()) % 100:03d}",
                "fclosetime": int(time.time() * 1000) + 180000,  # 当前时间加3分钟
                "fstatus": 1,  # 1=投注中，2=已封盘
                "fpreviousperiod": f"********-{(int(time.time())-300) % 100:03d}",
                "fpreviousresult": "01,03,05,07,09",
                "ServerTime": int(time.time() * 1000),
                "fnextperiod": f"********-{(int(time.time())+300) % 100:03d}"
            }
            
            # 在实际应用中，使用以下代码发送请求
            # response = requests.get(full_url, headers={
            #     "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            # })
            # data = response.json()
            
            # 使用模拟数据
            data = mock_response
            
            # 解析游戏信息
            current_period = data.get("fnumberofperiod", "--")
            status_code = data.get("fstatus", 0)
            status_text = "已封盘" if status_code == 2 else "投注中"
            
            # 计算倒计时
            close_time = data.get("fclosetime", 0)
            server_time = data.get("ServerTime", 0)
            countdown_seconds = max(0, (close_time - server_time) // 1000)
            minutes, seconds = divmod(countdown_seconds, 60)
            countdown_text = f"{minutes:02d}:{seconds:02d}"
            
            # 获取上期开奖号码
            prev_period = data.get("fpreviousperiod", "--")
            prev_result = data.get("fpreviousresult", "--")
            
            # 更新UI显示
            if manual_refresh:
                # 手动刷新时更新游戏名称
                self.game_name_label.config(text="江苏时时彩")
            
            # 更新期号和状态
            self.period_label.config(text=current_period)
            self.status_label.config(text=status_text)
            self.countdown_label.config(text=countdown_text)
            self.last_result_label.config(text=prev_result)
            
            # 记录日志
            if manual_refresh:
                self.logger.info(f"游戏信息已更新: 期号={current_period}, 状态={status_text}, 倒计时={countdown_text}")
            else:
                self.logger.debug(f"游戏信息已自动更新: 期号={current_period}, 状态={status_text}, 倒计时={countdown_text}")
            
            return data
        except Exception as e:
            self.logger.error(f"从 API 获取游戏信息失败: {str(e)}")
            return None
    
    def start_game_info_auto_update(self):
        """启动游戏信息自动更新"""
        try:
            # 启动API更新任务
            self._start_api_update_task()
            self.logger.info("游戏信息自动更新已启动")
        except Exception as e:
            self.logger.error(f"启动游戏信息自动更新失败: {str(e)}")
            
    def refresh_balance(self):
        """刷新账户余额"""
        try:
            import requests
            import re
            import random
            import time
            
            self.logger.info("正在刷新账户余额...")
            
            # 在实际应用中，应该从网站获取账户信息
            # 这里使用模拟数据代替真实请求
            
            # 模拟账号信息
            mock_account = f"test{random.randint(1000, 9999)}"
            mock_balance = round(random.uniform(1000, 10000), 2)
            
            # 更新UI显示
            self.account_label.config(text=mock_account)
            self.balance_label.config(text=f"{mock_balance:.2f}")
            
            self.logger.info(f"账户信息已更新: 账号={mock_account}, 余额={mock_balance:.2f}")
            
            # 在实际应用中，使用以下代码发送请求并解析响应
            # url = "https://www.example.com/userinfo"
            # headers = {
            #     "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            #     "Referer": "https://www.example.com/game"
            # }
            # 
            # # 尝试多种编码解析响应
            # response = requests.get(url, headers=headers)
            # content = None
            # 
            # # 尝试不同的编码方式
            # for encoding in ['utf-8', 'gbk', 'gb2312', 'gb18030', 'big5']:
            #     try:
            #         content = response.content.decode(encoding)
            #         break
            #     except UnicodeDecodeError:
            #         continue
            # 
            # if content:
            #     # 使用正则表达式提取账号和余额
            #     account_match = re.search(r'\b账号[\s\:]*([\w\d]+)\b', content)
            #     balance_match = re.search(r'\b余额[\s\:]*([\d\.]+)\b', content)
            #     
            #     account = account_match.group(1) if account_match else "--"
            #     balance = balance_match.group(1) if balance_match else "0.00"
            #     
            #     # 更新UI显示
            #     self.account_label.config(text=account)
            #     self.balance_label.config(text=balance)
            #     
            #     self.logger.info(f"账户信息已更新: 账号={account}, 余额={balance}")
            # else:
            #     self.logger.error("无法解析响应内容")
            
            return True
        except Exception as e:
            self.logger.error(f"刷新账户余额失败: {str(e)}")
            return False
    
    def load_order_history(self):
        """加载投注记录"""
        try:
            # 在实际实现中，这里应该调用API获取投注历史数据
            # 模拟一些历史数据用于测试
            history_data = [
                {"period": "********-001", "amount": "100.00", "status": "已中奖", "time": "2025-06-03 10:00:00"},
                {"period": "********-002", "amount": "50.00", "status": "未中奖", "time": "2025-06-03 10:05:00"}
            ]
            
            # 清空表格
            for item in self.history_tree.get_children():
                self.history_tree.delete(item)
            
            # 添加数据到表格
            for item in history_data:
                period = item.get("period", "--")
                amount = item.get("amount", "0.00")
                status = item.get("status", "--")
                time = item.get("time", "--")
                
                self.history_tree.insert("", tk.END, values=(period, amount, status, time))
                
            return history_data
        except Exception as e:
            self.logger.error(f"加载投注记录失败: {str(e)}")
            return []
    
    def select_position(self, position):
        """
        选择投注位置
        
        Args:
            position: 位置名称
        """
        try:
            # 更新选中位置
            self.selected_position = position
            
            # 更新位置按钮状态
            if hasattr(self, 'position_buttons') and isinstance(self.position_buttons, dict):
                for pos, btn in self.position_buttons.items():
                    if pos == position:
                        btn.config(bg="lightblue")  # 选中状态
                    else:
                        btn.config(bg="SystemButtonFace")  # 非选中状态
            elif hasattr(self, 'position_buttons') and isinstance(self.position_buttons, list):
                for btn in self.position_buttons:
                    if btn["text"] == position:
                        btn.config(bg="lightblue")
                    else:
                        btn.config(bg="SystemButtonFace")
            
            # 记录当前选中的位置
            self.current_position = position
            
            # 更新号码按钮状态
            self._update_number_buttons_state()
            
            self.logger.info(f"已选择位置: {position}")
            return True
        except Exception as e:
            self.logger.error(f"选择位置失败: {str(e)}")
            return False
    
    def _update_number_buttons_state(self):
        """
        更新号码按钮状态
        """
        try:
            # 确保选中号码字典中有该位置
            if self.selected_position not in self.selected_numbers:
                self.selected_numbers[self.selected_position] = []
            
            # 更新号码按钮状态
            if hasattr(self, 'number_buttons') and self.selected_position in self.number_buttons:
                for number in range(0, 10):
                    if number in self.number_buttons[self.selected_position]:
                        btn = self.number_buttons[self.selected_position][number]
                        
                        # 如果号码已选中，显示选中状态
                        if number in self.selected_numbers[self.selected_position]:
                            btn.config(bg="lightblue")
                        else:
                            btn.config(bg="SystemButtonFace")
            
            return True
        except Exception as e:
            self.logger.error(f"更新号码按钮状态失败: {str(e)}")
            return False
    
    def toggle_number(self, position, number):
        """
        切换号码选中状态
        
        Args:
            position: 位置名称
            number: 号码
        """
        try:
            # 找到对应位置的号码按钮
            if position in self.number_buttons and number in self.number_buttons[position]:
                btn = self.number_buttons[position][number]
                
                # 切换选中状态
                if btn.cget("bg") == "lightblue":
                    btn.config(bg="SystemButtonFace")
                    selected = False
                else:
                    btn.config(bg="lightblue")
                    selected = True
                    
                # 更新选中的号码列表
                if position not in self.selected_numbers:
                    self.selected_numbers[position] = []
                
                if selected and number not in self.selected_numbers[position]:
                    self.selected_numbers[position].append(number)
                elif not selected and number in self.selected_numbers[position]:
                    self.selected_numbers[position].remove(number)
                    
                self.logger.info(f"已{'选中' if selected else '取消选中'}位置 {position} 的号码 {number}")
                
                # 更新总金额
                self.update_total_amount()
                
                return True
            return False
        except Exception as e:
            self.logger.error(f"切换号码选中状态失败: {str(e)}")
            return False
    
    def clear_numbers(self):
        """
        清空所有选中的号码
        """
        try:
            # 检查是否有号码按钮和选中的号码
            if not hasattr(self, 'number_buttons') or not hasattr(self, 'selected_numbers'):
                return
            
            # 清空所有选中的号码
            for position in self.number_buttons:
                for number, btn in self.number_buttons[position].items():
                    # 重置按钮样式
                    if btn.cget("bg") == "lightblue":
                        btn.config(bg="SystemButtonFace")
            
            # 清空选中的号码列表
            self.selected_numbers = {}
            
            # 更新总金额
            self.update_total_amount()
            
            self.logger.info("已清空所有选中的号码")
        except Exception as e:
            self.logger.error(f"清空选中的号码失败: {str(e)}")
    
    def load_order_history(self):
        """
        加载投注记录
        """
        try:
            # 清空当前表格
            for item in self.order_tree.get_children():
                self.order_tree.delete(item)
            
            # 获取当前账号
            account = self.account_label.cget("text") if hasattr(self, 'account_label') else "--"
            if account == "--":
                self.logger.warning("未登录试玩账号，无法加载投注记录")
                return False
            
            # 在实际应用中，这里应该发送API请求获取投注记录
            # 这里使用模拟数据模拟投注记录
            
            # 模拟数据：生成当前时间附近的5条投注记录
            current_time = time.time()
            mock_orders = []
            
            for i in range(5):
                # 生成随机数据
                order_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(current_time - i * 300))
                period = f"********-{(i+1):03d}"
                amount = round(random.uniform(1, 100), 2)
                status = random.choice(["已中奖", "未中奖", "待开奖"])
                numbers = ", ".join([f"{random.randint(1, 28):02d}" for _ in range(random.randint(1, 5))])
                
                mock_orders.append({
                    "time": order_time,
                    "period": period,
                    "numbers": numbers,
                    "amount": amount,
                    "status": status
                })
            
            # 添加到表格中
            for order in mock_orders:
                self.order_tree.insert(
                    "", "end", 
                    values=(order["time"], order["period"], order["numbers"], f"{order['amount']:.2f}", order["status"])
                )
            
            self.logger.info(f"已加载{len(mock_orders)}条投注记录")
            return True
        except Exception as e:
            self.logger.error(f"加载投注记录失败: {str(e)}")
            return False
    
    def select_all_numbers(self, position):
        """
        全选号码
        
        Args:
            position: 位置名称
        """
        try:
            # 检查是否有号码按钮
            if not hasattr(self, 'number_buttons') or position not in self.number_buttons:
                self.logger.warning(f"位置 {position} 没有号码按钮")
                return False
            
            # 初始化选中的号码列表
            if position not in self.selected_numbers:
                self.selected_numbers[position] = []
            
            # 全选该位置的所有号码
            for number, btn in self.number_buttons[position].items():
                # 设置按钮选中状态
                btn.config(bg="lightblue")
                
                # 添加到选中列表（如果还没有选中）
                if number not in self.selected_numbers[position]:
                    self.selected_numbers[position].append(number)
            
            # 更新总金额
            self.update_total_amount()
            
            self.logger.info(f"已全选位置 {position} 的所有号码，共 {len(self.number_buttons[position])} 个")
            return True
        except Exception as e:
            self.logger.error(f"全选号码失败: {str(e)}")
            return False
    
    def clear_all_numbers(self, position):
        """
        清空指定位置的所有选中号码
        
        Args:
            position: 位置名称
        """
        try:
            # 检查是否有号码按钮
            if not hasattr(self, 'number_buttons') or position not in self.number_buttons:
                self.logger.warning(f"位置 {position} 没有号码按钮")
                return False
            
            # 清空该位置的所有选中号码
            for number, btn in self.number_buttons[position].items():
                # 重置按钮样式
                if btn.cget("bg") == "lightblue":
                    btn.config(bg="SystemButtonFace")
            
            # 清空该位置的选中列表
            if position in self.selected_numbers:
                self.selected_numbers[position] = []
            
            # 更新总金额
            self.update_total_amount()
            
            self.logger.info(f"已清空位置 {position} 的所有选中号码")
            return True
        except Exception as e:
            self.logger.error(f"清空位置号码失败: {str(e)}")
            return False
    
    def set_bet_unit(self, unit, text):
        """
        设置投注单位
        
        Args:
            unit: 单位值
            text: 单位文本
        """
        try:
            # 设置当前单位
            self.current_unit = unit
            self.current_unit_text = text
            
            # 更新单位按钮状态
            for btn in self.unit_buttons:
                if btn["text"] == text:
                    btn.config(bg="lightblue")
                else:
                    btn.config(bg="SystemButtonFace")
            
            self.logger.info(f"投注单位已设置为 {text} (值: {unit})")
            
            # 更新总金额
            self.update_total_amount()
        except Exception as e:
            self.logger.error(f"设置投注单位失败: {str(e)}")
    
    def update_multiple(self, event=None):
        """
        更新倍数
        
        Args:
            event: 事件对象
        """
        try:
            # 获取输入框中的倍数
            multiple_str = self.multiple_entry.get()
            try:
                multiple = int(multiple_str)
                if multiple <= 0:
                    raise ValueError("倍数必须大于0")
                    
                # 更新倍数
                self.current_multiple = multiple
                self.logger.info(f"倍数已更新为 {multiple}")
            except ValueError:
                self.logger.warning(f"无效的倍数输入: {multiple_str}")
                # 重置为默认值
                self.multiple_entry.delete(0, tk.END)
                self.multiple_entry.insert(0, "1")
                self.current_multiple = 1
                
            # 更新总金额
            self.update_total_amount()
        except Exception as e:
            self.logger.error(f"更新倍数失败: {str(e)}")
    
    def set_multiple(self, value):
        """
        设置倍数
        
        Args:
            value: 倍数值
        """
        try:
            # 验证倍数值
            try:
                multiple = int(value)
                if multiple <= 0:
                    raise ValueError("倍数必须大于0")
            except ValueError:
                self.logger.warning(f"无效的倍数值: {value}")
                multiple = 1
                
            # 更新倍数输入框
            self.multiple_entry.delete(0, tk.END)
            self.multiple_entry.insert(0, str(multiple))
            
            # 设置当前倍数
            self.current_multiple = multiple
            self.logger.info(f"倍数已设置为 {multiple}")
            
            # 更新总金额
            self.update_total_amount()
        except Exception as e:
            self.logger.error(f"设置倍数失败: {str(e)}")
    
    def show_bet_details(self, event=None):
        """
        显示投注详情
        
        Args:
            event: 事件对象
        """
        try:
            # 获取选中的记录
            selected_items = self.order_tree.selection()
            if not selected_items:
                messagebox.showinfo("提示", "请先选择一条投注记录")
                return
            
            # 获取选中记录的数据
            item = selected_items[0]  # 只处理第一条选中的记录
            values = self.order_tree.item(item, "values")
            
            # 构建详情信息
            details = f"投注时间: {values[0]}\n"
            details += f"期号: {values[1]}\n"
            details += f"投注号码: {values[2]}\n"
            details += f"投注金额: {values[3]}元\n"
            details += f"状态: {values[4]}"
            
            # 如果是已中奖状态，添加中奖金额信息
            if values[4] == "已中奖":
                # 模拟中奖金额，实际应用中应从数据中获取
                win_amount = round(float(values[3].replace(",", "")) * random.uniform(1.5, 10), 2)
                details += f"\n中奖金额: {win_amount:.2f}元"
            
            # 显示详情对话框
            messagebox.showinfo("投注详情", details)
            
            self.logger.info(f"已显示投注记录详情: {values[1]}")
            return True
        except Exception as e:
            self.logger.error(f"显示投注详情失败: {str(e)}")
            messagebox.showerror("错误", f"显示投注详情时发生错误: {str(e)}")
            return False
    
    def refresh_all(self):
        """
        综合刷新所有信息
        """
        try:
            self.logger.info("正在刷新所有信息...")
            
            # 刷新账户余额
            self.refresh_balance()
            
            # 刷新游戏信息
            self.refresh_game_info()
            
            # 加载投注记录
            self.load_order_history()
            
            # 清空选中的号码
            self.clear_numbers()
            
            # 重置倍数为1
            self.set_multiple(1)
            
            # 重置单位为元
            self.set_bet_unit(1.0, "元")
            
            # 更新总金额
            self.update_total_amount()
            
            # 如果自动刷新未开启，启动它
            if not hasattr(self, 'auto_refresh') or not self.auto_refresh:
                self.toggle_auto_refresh()
            
            # 确保API更新任务在运行
            self._start_api_update_task()
            
            self.logger.info("所有信息刷新完成")
            return True
        except Exception as e:
            self.logger.error(f"刷新所有信息失败: {str(e)}")
            return False
    
    def update_status_from_log(self, message, level="INFO"):
        """
        从日志信息更新状态栏显示
        
        Args:
            message: 日志消息
            level: 日志级别
        """
        try:
            # 如果状态标签不存在，则直接返回
            if not hasattr(self, 'status_label') or not self.status_label:
                return
                
            # 根据日志级别设置不同的颜色
            color = "black"  # 默认颜色
            if level == "ERROR":
                color = "red"
            elif level == "WARNING":
                color = "orange"
            elif level == "SUCCESS":
                color = "green"
                
            # 更新状态标签文本和颜色
            self.status_label.config(text=message, fg=color)
            
            # 确保UI更新
            self.tab.update_idletasks()
        except Exception as e:
            # 这里不使用logger避免循环调用
            print(f"更新状态信息失败: {str(e)}")
    
    def cleanup(self):
        """
        清理资源，在应用关闭时调用
        """
        try:
            self.logger.info("正在清理资源...")
            
            # 停止API更新任务
            if hasattr(self, '_stop_api_update_task'):
                self._stop_api_update_task()
            
            # 取消注册状态回调
            from utils import unregister_status_callback
            if hasattr(self, 'update_status_from_log'):
                try:
                    unregister_status_callback(self.update_status_from_log)
                    self.logger.info("状态回调已取消注册")
                except Exception as e:
                    self.logger.error(f"取消注册状态回调失败: {str(e)}")
            
            # 等待后台线程结束
            if hasattr(self, '_api_update_thread') and self._api_update_thread is not None:
                try:
                    if self._api_update_thread.is_alive():
                        self._api_update_thread.join(timeout=1.0)
                        if self._api_update_thread.is_alive():
                            self.logger.warning("API更新线程未能在规定时间内结束")
                        else:
                            self.logger.info("API更新线程已结束")
                except Exception as e:
                    self.logger.error(f"等待线程结束失败: {str(e)}")
            
            # 清空选中的号码
            self.clear_numbers()
            
            # 清空状态信息
            if hasattr(self, 'status_text'):
                try:
                    self.status_text.config(state=tk.NORMAL)
                    self.status_text.delete(1.0, tk.END)
                    self.status_text.insert(tk.END, "应用已关闭\n")
                    self.status_text.config(state=tk.DISABLED)
                except Exception as e:
                    self.logger.error(f"清空状态信息失败: {str(e)}")
            
            self.logger.info("资源清理完成")
            return True
        except Exception as e:
            self.logger.error(f"资源清理失败: {str(e)}")
            return False
    
    def refresh_balance(self):
        """
        刷新账户余额
        """
        try:
            self.logger.info("正在刷新账户余额...")
            
            # 模拟从服务器获取余额
            # 实际应用中应调用API接口获取真实余额
            try:
                # 如果已有余额，生成一个小浮动的新余额
                if hasattr(self, 'current_balance') and self.current_balance:
                    try:
                        current = float(self.current_balance)
                        # 生成-5到+10之间的浮动金额
                        change = random.uniform(-5, 10)
                        new_balance = max(0, current + change)  # 确保余额不会为负
                        self.current_balance = f"{new_balance:.2f}"
                    except ValueError:
                        # 如果当前余额不是有效数字，设置一个默认值
                        self.current_balance = "1000.00"
                else:
                    # 首次设置余额
                    self.current_balance = "1000.00"
                    
                # 如果没有账号，设置一个模拟账号
                if not hasattr(self, 'current_account') or not self.current_account:
                    self.current_account = f"test{random.randint(10000, 99999)}"
                
                # 更新UI显示
                self.on_account_updated(self.current_account, self.current_balance)
                
                self.logger.info(f"账户余额已更新: {self.current_balance}元")
                return True
            except Exception as e:
                self.logger.error(f"获取余额数据失败: {str(e)}")
                # 如果失败，使用默认值
                self.current_balance = "1000.00"
                self.current_account = "test_user"
                self.on_account_updated(self.current_account, self.current_balance)
                return False
        except Exception as e:
            self.logger.error(f"刷新账户余额失败: {str(e)}")
            return False
    
    def refresh_game_info(self):
        """
        刷新游戏信息
        """
        try:
            self.logger.info("正在刷新游戏信息...")
            
            # 调用API更新方法获取最新游戏信息
            return self.update_game_info_from_api(manual_refresh=True)
        except Exception as e:
            self.logger.error(f"刷新游戏信息失败: {str(e)}")
            return False
    
    def on_tab_changed(self, event=None):
        """
        标签页切换事件处理
        
        Args:
            event: 事件对象
        """
        try:
            # 获取当前选中的标签页
            current_tab = self.notebook.select()
            tab_text = self.notebook.tab(current_tab, "text")
            
            # 判断是否切换到了试玩标签页
            if tab_text == "试玩" or (hasattr(self, 'tab_name') and tab_text == self.tab_name):
                self.logger.info(f"切换到{tab_text}标签页")
                
                # 如果账号已加载，刷新余额和游戏信息
                if hasattr(self, 'account_label') and self.account_label.cget("text") != "--":
                    # 刷新余额
                    self.refresh_balance()
                    
                    # 刷新游戏信息
                    self.refresh_game_info()
                
                # 确保API更新任务在运行（如果自动刷新开启）
                if hasattr(self, 'auto_refresh') and self.auto_refresh:
                    self._start_api_update_task()
            else:
                # 如果切换到其他标签页，可以停止API更新任务以节省资源
                # 这里暂时不停止，以保持实时性
                # self._stop_api_update_task()
                pass
                
            return True
        except Exception as e:
            self.logger.error(f"处理标签页切换事件失败: {str(e)}")
            return False
    
    def _start_api_update_task(self):
        """
        启动API更新任务
        """
        try:
            # 如果已有任务在运行，则不重复启动
            if hasattr(self, '_api_update_thread') and self._api_update_thread is not None and self._api_update_thread.is_alive():
                self.logger.info("API更新任务已在运行中")
                return True
            
            # 创建停止事件
            self._api_update_stop_event = threading.Event()
            
            # 创建并启动线程
            self._api_update_thread = threading.Thread(
                target=self._api_update_task,
                args=(self._api_update_stop_event,),
                daemon=True
            )
            self._api_update_thread.start()
            
            self.logger.info("API更新任务已启动")
            return True
        except Exception as e:
            self.logger.error(f"启动API更新任务失败: {str(e)}")
            return False
    
    def _stop_api_update_task(self):
        """
        停止API更新任务
        """
        try:
            # 设置停止事件
            if hasattr(self, '_api_update_stop_event'):
                self._api_update_stop_event.set()
                self.logger.info("API更新任务停止事件已触发")
            
            # 等待线程结束（最多等待1秒）
            if hasattr(self, '_api_update_thread') and self._api_update_thread is not None:
                if self._api_update_thread.is_alive():
                    self._api_update_thread.join(timeout=1.0)
                    if self._api_update_thread.is_alive():
                        self.logger.warning("API更新线程未能在规定时间内结束")
                    else:
                        self.logger.info("API更新线程已结束")
            
            return True
        except Exception as e:
            self.logger.error(f"停止API更新任务失败: {str(e)}")
            return False
    
    def _api_update_task(self, stop_event):
        """
        API更新任务函数，在后台线程中运行
        
        Args:
            stop_event: 停止事件
        """
        try:
            self.logger.info("API更新任务已启动运行")
            
            # 循环更新，直到收到停止信号
            while not stop_event.is_set():
                try:
                    # 只在自动刷新开启时更新
                    if hasattr(self, 'auto_refresh') and self.auto_refresh:
                        # 更新游戏信息
                        self.update_game_info_from_api()
                        
                        # 每隔5秒更新一次
                        for _ in range(5):
                            if stop_event.is_set():
                                break
                            time.sleep(1)
                    else:
                        # 如果自动刷新关闭，则每秒检查一次是否需要重新开启
                        time.sleep(1)
                except Exception as e:
                    self.logger.error(f"API更新任务执行异常: {str(e)}")
                    # 出错后等待一段时间再重试
                    time.sleep(3)
            
            self.logger.info("API更新任务已停止")
        except Exception as e:
            self.logger.error(f"API更新任务异常终止: {str(e)}")
            return False
    
    def clear_numbers(self, position=None):
        """
        清除选中的号码
        
        Args:
            position: 位置名称，如果为空，则清除所有位置的选中号码
        """
        try:
            # 如果指定了位置，只清除该位置的选中号码
            if position is not None:
                # 清除选中的号码列表
                if position in self.selected_numbers:
                    self.selected_numbers[position] = []
                
                # 更新UI
                for number in range(0, 10):
                    if position in self.number_buttons and number in self.number_buttons[position]:
                        btn = self.number_buttons[position][number]
                        btn.config(bg="SystemButtonFace")
                
                self.logger.info(f"已清除位置 {position} 的选中号码")
            else:
                # 清除所有位置的选中号码
                for pos in self.selected_numbers.keys():
                    self.selected_numbers[pos] = []
                    
                    # 更新UI
                    for number in range(0, 10):
                        if pos in self.number_buttons and number in self.number_buttons[pos]:
                            btn = self.number_buttons[pos][number]
                            btn.config(bg="SystemButtonFace")
                
                self.logger.info("已清除所有选中号码")
            
            # 更新总金额
            self.update_total_amount()
            return True
        except Exception as e:
            self.logger.error(f"清除选中号码失败: {str(e)}")
            return False
    
    def load_order_history(self):
        """
        加载投注记录
        """
        try:
            self.logger.info("正在加载投注记录...")
            
            # 清空当前的记录
            for item in self.order_tree.get_children():
                self.order_tree.delete(item)
            
            # 模拟加载5条投注记录
            # 实际应用中应调用API接口获取真实投注记录
            current_time = datetime.datetime.now()
            
            # 生成模拟数据
            for i in range(5):
                # 模拟投注时间，每条记录间隔几分钟
                bet_time = current_time - datetime.timedelta(minutes=i*5)
                bet_time_str = bet_time.strftime("%Y-%m-%d %H:%M:%S")
                
                # 模拟期号，使用当前日期+随机数
                period = f"{current_time.strftime('%Y%m%d')}{random.randint(1, 120):03d}"
                
                # 模拟投注号码，随机生成一个位置的号码
                positions = ["万位", "千位", "百位", "十位", "个位"]
                position = random.choice(positions)
                numbers = [f"{random.randint(0, 9):01d}" for _ in range(random.randint(1, 3))]
                bet_numbers = f"{position}: {','.join(numbers)}"
                
                # 模拟投注金额，基于选中的号码数量
                amount = 2.0 * len(numbers) * random.randint(1, 5)  # 基础金额 * 号码数 * 随机倍数
                
                # 模拟状态，80%概率为已开奖，20%为未开奖
                status = "已中奖" if random.random() < 0.2 else ("未中奖" if random.random() < 0.75 else "未开奖")
                
                # 插入记录
                self.order_tree.insert("", tk.END, values=(bet_time_str, period, bet_numbers, f"{amount:.2f}", status))
            
            self.logger.info(f"已加载5条投注记录")
            return True
        except Exception as e:
            self.logger.error(f"加载投注记录失败: {str(e)}")
            return False
    
    def go_to_game(self):
        """
        进入游戏页面
        """
        try:
            self.logger.info("正在进入游戏页面...")
            
            # 刷新游戏信息
            self.update_game_info_from_api(manual_refresh=True)
            
            # 启动API更新任务
            self._start_api_update_task()
            
            # 加载投注记录
            self.load_order_history()
            
            # 刷新账户余额
            self.refresh_balance()
            
            # 清空选中的号码
            self.clear_numbers()
            
            # 重置倍数为1
            self.set_multiple(1)
            
            # 重置单位为元
            self.set_bet_unit(1.0, "元")
            
            # 更新总金额
            self.update_total_amount()
            
            self.logger.info("已进入游戏页面")
            return True
        except Exception as e:
            self.logger.error(f"进入游戏页面失败: {str(e)}")
            return False
    
    def toggle_number(self, position, number, button):
        """
        切换号码选中状态
        
        Args:
            position: 位置名称
            number: 号码
            button: 按钮对象
        """
        try:
            # 确保选中号码字典中有该位置
            if position not in self.selected_numbers:
                self.selected_numbers[position] = []
            
            # 切换选中状态
            if number in self.selected_numbers[position]:
                # 如果已选中，则取消选中
                self.selected_numbers[position].remove(number)
                button.config(bg="SystemButtonFace")  # 恢复默认背景色
                self.logger.info(f"取消选中 {position} 位置的号码 {number}")
            else:
                # 如果未选中，则选中
                self.selected_numbers[position].append(number)
                button.config(bg="lightblue")  # 设置选中状态的背景色
                self.logger.info(f"选中 {position} 位置的号码 {number}")
            
            # 更新总金额
            self.update_total_amount()
            
            # 保存按钮引用
            if position not in self.number_buttons:
                self.number_buttons[position] = {}
            self.number_buttons[position][number] = button
            
            return True
        except Exception as e:
            self.logger.error(f"切换号码选中状态失败: {str(e)}")
            return False
    
    def select_all_numbers(self, position):
        """
        选中指定位置的所有号码
        
        Args:
            position: 位置名称
        """
        try:
            # 确保选中号码字典中有该位置
            if position not in self.selected_numbers:
                self.selected_numbers[position] = []
            
            # 选中该位置的所有号码（0-9）
            for number in range(0, 10):
                # 如果该号码还未选中，则选中它
                if number not in self.selected_numbers[position]:
                    self.selected_numbers[position].append(number)
                    
                    # 更新按钮外观
                    if position in self.number_buttons and number in self.number_buttons[position]:
                        btn = self.number_buttons[position][number]
                        btn.config(bg="lightblue")
            
            self.logger.info(f"已选中 {position} 位置的所有号码")
            
            # 更新总金额
            self.update_total_amount()
            return True
        except Exception as e:
            self.logger.error(f"选中所有号码失败: {str(e)}")
            return False
    
    def confirm_bet(self):
        """
        确认投注
        """
        try:
            # 检查是否有选中的号码
            has_selected = False
            for position, numbers in self.selected_numbers.items():
                if numbers:
                    has_selected = True
                    break
            
            if not has_selected:
                messagebox.showwarning("投注提示", "请先选择号码")
                return False
            
            # 获取当前期号
            current_period = ""
            if hasattr(self, 'current_game_info') and self.current_game_info and 'fnumberofperiod' in self.current_game_info:
                current_period = self.current_game_info['fnumberofperiod']
            
            if not current_period:
                messagebox.showwarning("投注提示", "无法获取当前期号，请刷新游戏信息")
                return False
            
            # 检查游戏状态
            if hasattr(self, 'current_game_info') and self.current_game_info and 'fstatus' in self.current_game_info:
                game_status = self.current_game_info['fstatus']
                if game_status == 2:  # 已封盘
                    messagebox.showwarning("投注提示", "当前期已封盘，请等待下一期")
                    return False
            
            # 准备投注参数
            bet_data = {
                'gameType': 'JSSC168',  # 游戏类型
                'period': current_period,  # 期号
                'source': 'web',  # 来源
                'terminal': 'PC',  # 终端
                'bets': []  # 投注详情
            }
            
            # 收集所有选中的号码
            for position, numbers in self.selected_numbers.items():
                if not numbers:
                    continue
                
                # 将号码转换为两位数字格式（如 01, 02 而不是 1, 2）
                formatted_numbers = [f"{num:02d}" for num in numbers]
                
                # 根据位置生成投注类型
                bet_type = self._get_bet_type_by_position(position)
                
                # 计算投注金额
                amount = self._calculate_bet_amount(len(numbers))
                
                # 添加到投注列表
                bet_data['bets'].append({
                    'type': bet_type,
                    'numbers': ','.join(formatted_numbers),
                    'amount': amount,
                    'multiple': self.current_multiple,
                    'unit': self._get_unit_name(self.current_unit)
                })
            
            # 显示投注确认对话框
            total_amount = self._calculate_total_bet_amount()
            confirm_message = f"您将投注第 {current_period} 期\n"
            
            for bet in bet_data['bets']:
                confirm_message += f"{self._get_position_name(bet['type'])}: {bet['numbers']}\n"
            
            confirm_message += f"\n总金额: {total_amount:.2f} 元"
            
            if messagebox.askyesno("投注确认", confirm_message):
                # 模拟投注请求
                # 实际应用中应调用API接口发送投注请求
                self.logger.info(f"发送投注请求: {bet_data}")
                
                # 模拟成功响应
                success = True
                message = "投注成功"
                result = {
                    'orderId': f"ORD{int(time.time())}",
                    'amount': total_amount,
                    'period': current_period
                }
                
                # 回调投注结果处理函数
                self.on_bet_result(success, message, result)
                
                # 清空选中的号码
                self.clear_numbers()
                
                # 刷新账户余额
                self.refresh_balance()
                
                return True
            else:
                self.logger.info("用户取消投注")
                return False
        except Exception as e:
            self.logger.error(f"投注确认失败: {str(e)}")
            messagebox.showerror("投注错误", f"投注过程中发生错误: {str(e)}")
            return False
    
    def _get_bet_type_by_position(self, position):
        """
        根据位置名称获取投注类型码
        """
        position_map = {
            "万位": "w",
            "千位": "q",
            "百位": "b",
            "十位": "s",
            "个位": "g"
        }
        return position_map.get(position, "g")
    
    def _get_position_name(self, bet_type):
        """
        根据投注类型码获取位置名称
        """
        type_map = {
            "w": "万位",
            "q": "千位",
            "b": "百位",
            "s": "十位",
            "g": "个位"
        }
        return type_map.get(bet_type, "个位")
    
    def _get_unit_name(self, unit_value):
        """
        根据单位值获取单位名称
        """
        unit_map = {
            1.0: "元",
            0.1: "角",
            0.01: "分"
        }
        return unit_map.get(unit_value, "元")
    
    def _calculate_bet_amount(self, num_selected):
        """
        计算单个位置的投注金额
        """
        # 基础金额，每注基础为2元
        base_amount = 2.0
        
        # 计算金额: 基础金额 * 倍数 * 单位值 * 选中号码数
        return base_amount * self.current_multiple * self.current_unit * num_selected
    
    def _calculate_total_bet_amount(self):
        """
        计算总投注金额
        """
        total = 0.0
        base_amount = 2.0
        
        for position, numbers in self.selected_numbers.items():
            if numbers:
                # 每个位置的金额: 基础金额 * 倍数 * 单位值 * 选中号码数
                position_amount = base_amount * self.current_multiple * self.current_unit * len(numbers)
                total += position_amount
        
        return total
    
    def update_total_amount(self):
        """
        更新总投注金额
        
        总金额计算公式：基础金额(2元) * 倍数 * 单位值(元/角/分) * 选中号码数
        """
        try:
            # 基础金额，每注基础为2元
            base_amount = 2.0
            
            # 获取当前倍数
            try:
                multiple = float(self.multiple.get())
            except (ValueError, AttributeError):
                multiple = 1.0
            
            # 获取当前单位值（元=1.0，角=0.1，分=0.01）
            try:
                unit = float(self.bet_unit.get())
            except (ValueError, AttributeError):
                unit = 1.0
            
            # 计算选中的号码数量
            selected_count = 0
            if hasattr(self, 'selected_numbers'):
                for position, numbers in self.selected_numbers.items():
                    selected_count += len(numbers)
            
            # 计算总金额
            total_amount = base_amount * multiple * unit * selected_count
            
            # 更新UI显示
            if hasattr(self, 'total_amount_var') and isinstance(self.total_amount_var, tk.StringVar):
                self.total_amount_var.set(f"{total_amount:.2f}")
            elif hasattr(self, 'total_amount_label'):
                self.total_amount_label.config(text=f"{total_amount:.2f}")
            
            self.logger.debug(f"总金额已更新: {total_amount:.2f} = {base_amount} * {multiple} * {unit} * {selected_count}")
            return total_amount
        except Exception as e:
            self.logger.error(f"更新总金额失败: {str(e)}")
            return 0.0
    
    def confirm_selection(self):
        """
        确认选号并准备投注
        """
        try:
            # 检查是否有选中的号码
            if not hasattr(self, 'selected_numbers') or not self.selected_numbers:
                messagebox.showwarning("提示", "请先选择号码")
                return False
            
            # 检查每个位置是否都有选中的号码
            selected_count = 0
            for position, numbers in self.selected_numbers.items():
                selected_count += len(numbers)
            
            if selected_count == 0:
                messagebox.showwarning("提示", "请至少选择一个号码")
                return False
            
            # 获取当前投注信息
            bet_info = {
                "positions": {},
                "multiple": self.current_multiple if hasattr(self, 'current_multiple') else 1,
                "unit": self.current_unit if hasattr(self, 'current_unit') else 1.0,
                "unit_text": self.current_unit_text if hasattr(self, 'current_unit_text') else "元",
                "total_amount": self.update_total_amount()
            }
            
            # 收集选中的号码
            for position, numbers in self.selected_numbers.items():
                if numbers:  # 只添加有选中号码的位置
                    bet_info["positions"][position] = sorted(list(numbers))
            
            # 构建确认信息
            confirm_message = f"您将投注以下号码:\n"
            for position, numbers in bet_info["positions"].items():
                # 确保号码是两位数字格式（如 01, 02 而不是 1, 2）
                formatted_numbers = ["{:02d}".format(num) for num in numbers]
                confirm_message += f"{position}: {', '.join(formatted_numbers)}\n"
            
            confirm_message += f"\n倍数: {bet_info['multiple']}\n"
            confirm_message += f"单位: {bet_info['unit_text']}\n"
            confirm_message += f"总金额: {bet_info['total_amount']:.2f}元"
            
            # 弹窗确认
            if messagebox.askyesno("确认投注", confirm_message):
                # 调用投注方法
                return self.place_bet(bet_info)
            else:
                self.logger.info("用户取消了投注")
                return False
        except Exception as e:
            self.logger.error(f"确认选号失败: {str(e)}")
            messagebox.showerror("错误", f"确认选号时发生错误: {str(e)}")
            return False
    
    def place_bet(self, bet_info):
        """
        执行投注
        
        Args:
            bet_info: 投注信息字典
        """
        try:
            # 获取当前游戏状态
            game_status = self.status_label.cget("text") if hasattr(self, 'status_label') else ""
            if game_status == "已封盘":
                messagebox.showwarning("提示", "当前期已封盘，无法投注")
                return False
            
            # 获取当前余额
            balance_text = self.balance_label.cget("text") if hasattr(self, 'balance_label') else "0.00"
            try:
                balance = float(balance_text)
            except ValueError:
                balance = 0.0
            
            # 检查余额是否足够
            if balance < bet_info["total_amount"]:
                messagebox.showwarning("提示", f"账户余额不足，需要{bet_info['total_amount']:.2f}元，当前余额{balance:.2f}元")
                return False
            
            # 获取当前期号
            current_period = self.period_label.cget("text") if hasattr(self, 'period_label') else ""
            if not current_period or current_period == "--":
                messagebox.showwarning("提示", "无法获取当前期号，请刷新游戏信息")
                return False
            
            # 构建投注参数
            bet_params = {
                "period": current_period,
                "gameType": "JSSC168",  # 游戏类型
                "source": "web",       # 来源
                "terminal": "pc",      # 终端
                "multiple": bet_info["multiple"],
                "unit": bet_info["unit"],
                "positions": {}
            }
            
            # 格式化号码为两位数字字符串
            for position, numbers in bet_info["positions"].items():
                bet_params["positions"][position] = ["{:02d}".format(num) for num in numbers]
            
            # 在实际应用中，这里应该发送API请求执行投注
            # 这里使用模拟数据模拟投注成功
            
            # 模拟投注成功
            success = True
            message = "投注成功"
            result = {
                "period": current_period,
                "amount": bet_info["total_amount"],
                "time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            }
            
            # 更新余额
            new_balance = balance - bet_info["total_amount"]
            self.balance_label.config(text=f"{new_balance:.2f}")
            
            # 清空选中的号码
            self.clear_numbers()
            
            # 重置倍数为1
            self.set_multiple(1)
            
            # 刷新投注记录
            self.load_order_history()
            
            # 调用投注结果回调
            self.on_bet_result(success, message, result)
            
            return True
        except Exception as e:
            self.logger.error(f"投注失败: {str(e)}")
            messagebox.showerror("错误", f"投注时发生错误: {str(e)}")
            
            # 调用投注结果回调（失败）
            self.on_bet_result(False, f"投注失败: {str(e)}", None)
            
            return False
    
    def show_multiple_menu(self, event):
        """
        显示倍数菜单
        
        Args:
            event: 事件对象
        """
        try:
            self.ui.multiple_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.ui.multiple_menu.grab_release()
    
    # 此方法已移至上方实现，此处为避免重复代码
    def place_bet(self, bet_info=None):
        """
        发送投注请求
        
        Args:
            bet_info: 投注信息字典
        """
        try:
            if bet_info is None:
                self.logger.error("投注信息为空")
                messagebox.showerror("投注失败", "投注信息不完整")
                return False
                
            # 获取当前游戏信息
            game_name = self.game_name_label.cget("text")
            period = self.period_label.cget("text")
            status_text = self.status_label.cget("text")
            
            # 检查游戏状态
            if "已封盘" in status_text or "开奖中" in status_text:
                messagebox.showerror("投注失败", "当前期已封盘，无法投注")
                return False
                
            # 检查账户余额
            try:
                balance_text = self.balance_label.cget("text")
                balance = float(balance_text)
                if balance < bet_info["total_amount"]:
                    messagebox.showerror("投注失败", f"账户余额不足\n当前余额: {balance} 元\n需要金额: {bet_info['total_amount']:.2f} 元")
                    return False
            except (ValueError, TypeError):
                self.logger.warning(f"无法解析余额: {balance_text}")
                
            # 构建投注请求参数
            bet_params = {
                "game": game_name,
                "period": period,
                "positions": {},
                "multiple": bet_info["multiple"],
                "unit": bet_info["unit"],
                "total_amount": bet_info["total_amount"]
            }
            
            # 添加每个位置的选号信息
            for position_name, position_data in bet_info["positions"].items():
                # 将数字转换为两位数字格式（如 01, 02 而不是 1, 2）
                formatted_numbers = [f"{num:02d}" for num in position_data["selected_numbers"]]
                bet_params["positions"][position_name] = {
                    "numbers": formatted_numbers,
                    "amount": position_data["amount"]
                }
                
            # 模拟投注成功
            self.logger.info(f"投注成功: {game_name} 第 {period} 期, 总金额: {bet_info['total_amount']:.2f} 元")
            
            # 更新账户余额
            try:
                new_balance = balance - bet_info["total_amount"]
                self.balance_label.config(text=f"{new_balance:.2f}")
            except (ValueError, TypeError):
                pass
                
            # 清除选中的号码
            for position in self.position_buttons:
                position_name = position["text"]
                self.clear_numbers(position_name)
                
            # 刷新投注记录
            self.load_order_history()
            
            # 显示成功消息
            messagebox.showinfo("投注成功", f"投注成功\n游戏: {game_name}\n期号: {period}\n金额: {bet_info['total_amount']:.2f} 元")
            return True
        except Exception as e:
            self.logger.error(f"投注失败: {str(e)}")
            messagebox.showerror("投注失败", f"投注处理过程中出错: {str(e)}")
            return False
    
    def show_bet_details(self, event):
        """
        显示投注详情
        
        Args:
            event: 事件对象
        """
        try:
            # 获取选中的项目
            selection = self.history_tree.selection()
            if not selection:
                return
            
            # 获取选中项的值
            item = self.history_tree.item(selection[0])
            values = item["values"]
            
            if not values or len(values) < 4:
                return
            
            # 显示详情
            period, amount, status, time = values
            details = f"期号: {period}\n金额: {amount}\n状态: {status}\n时间: {time}"
            messagebox.showinfo("投注详情", details)
        except Exception as e:
            self.logger.error(f"显示投注详情失败: {str(e)}")
    
    def on_tab_changed(self, event):
        """
        当选项卡切换时触发
        
        Args:
            event: 事件对象
        """
        try:
            # 获取当前选中的选项卡
            current_tab = self.notebook.select()
            
            # 如果当前选项卡是试玩选项卡
            if current_tab == str(self.tab):
                self.logger.info("试玩选项卡被选中")
                
                # 如果账号已经加载，刷新游戏信息
                if hasattr(self, 'account_label') and self.account_label.cget("text"):
                    self.refresh_balance()
                    self.refresh_game_info()
                    
                # 如果自动刷新已开启，确保自动刷新任务在运行
                if hasattr(self, 'auto_refresh') and self.auto_refresh:
                    self._start_api_update_task()
        except Exception as e:
            self.logger.error(f"处理选项卡切换事件失败: {str(e)}")
    
    def refresh_all(self):
        """刷新账号信息和投注记录"""
        try:
            self.logger.info("正在刷新所有信息...")
            
            # 刷新账户余额
            self.refresh_balance()
            
            # 刷新游戏信息
            self.refresh_game_info()
            
            # 加载投注记录
            self.load_order_history()
            
            self.logger.info("所有信息刷新完成")
        except Exception as e:
            self.logger.error(f"刷新所有信息失败: {str(e)}")
    
    def _stop_api_update_task(self):
        """
        停止API更新任务
        """
        try:
            # 设置停止标志
            self._api_update_running = False
            
            # 等待线程结束
            if hasattr(self, '_api_update_thread') and self._api_update_thread is not None:
                if self._api_update_thread.is_alive():
                    self.logger.info("正在等待API更新线程结束...")
                    self._api_update_thread.join(timeout=2.0)
                    if self._api_update_thread.is_alive():
                        self.logger.warning("API更新线程未能在指定时间内结束")
                    else:
                        self.logger.info("API更新线程已正常结束")
                else:
                    self.logger.info("API更新线程已经不在运行状态")
        except Exception as e:
            self.logger.error(f"停止API更新任务失败: {str(e)}")
    
    def cleanup(self):
        """清理资源，在应用程序关闭时调用"""
        try:
            self.logger.info("正在清理试玩选项卡资源...")
            
            # 停止所有运行中的任务
            self._stop_api_update_task()
            
            # 取消注册日志回调
            try:
                from utils.logger import unregister_status_callback
                unregister_status_callback(self.update_status_from_log)
                self.logger.info("已取消注册状态更新回调")
            except Exception as e:
                self.logger.error(f"取消注册状态更新回调失败: {str(e)}")
            
            # 清理线程资源
            if hasattr(self, '_api_update_thread') and self._api_update_thread is not None:
                if self._api_update_thread.is_alive():
                    self._api_update_thread.join(timeout=1.0)
                self._api_update_thread = None
                self.logger.info("已清理API更新线程")
            
            self.logger.info("试玩选项卡资源已清理完成")
        except Exception as e:
            self.logger.error(f"清理试玩选项卡资源失败: {str(e)}")
