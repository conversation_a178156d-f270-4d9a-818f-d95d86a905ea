#!/usr/bin/env python
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import datetime

# 导入新的架构组件
from .trial_ui import TrialTabUI
from logic.trial_logic import TrialLogic
from utils.logger import Logger

class TrialTab:
    def __init__(self, parent, notebook, app, root=None):
        """初始化试玩选项卡"""
        self.parent = parent
        self.notebook = notebook
        self.app = app
        self.session = app.session
        self.root = root if root is not None else parent

        # 创建日志记录器
        self.logger = Logger("试玩选项卡")

        # 创建试玩选项卡
        self.tab = ttk.Frame(notebook)
        notebook.add(self.tab, text="试玩")

        # 创建UI组件
        self.ui = TrialTabUI(self.tab, self.root)

        # 创建业务逻辑组件
        self.logic = TrialLogic(self.session, self.logger)

        # 注册回调函数
        self.logic.register_callbacks(
            on_account_updated=self.ui.set_account_info,
            on_game_info_updated=self.ui.set_game_info,
            on_bet_result=self.on_bet_result
        )

        # 绑定UI事件
        self.bind_ui_events()

        # 监听选项卡切换
        self.notebook.bind("<<NotebookTabChanged>>", self.on_tab_changed)
    
    def bind_ui_events(self):
        """绑定UI事件"""
        # 绑定游戏信息刷新按钮
        self.ui.refresh_button.config(command=self.logic.refresh_game_info)

        # 绑定账号相关按钮
        self.ui.get_trial_button.config(command=self.confirm_get_trial)
        self.ui.refresh_balance_button.config(command=self.logic.refresh_balance)
        self.ui.auto_refresh_button.config(command=self.toggle_auto_refresh)

        # 绑定位置选择按钮
        for position, button in self.ui.position_buttons.items():
            button.config(command=lambda pos=position: self.select_position(pos))

        # 绑定号码按钮
        for position in self.ui.positions:
            for number, button in self.ui.number_buttons[position].items():
                button.config(command=lambda pos=position, num=number: self.toggle_number(pos, num))
            
            # 绑定全选和清除按钮
            self.ui.all_buttons[position].config(command=lambda pos=position: self.select_all_numbers(pos))
            self.ui.clear_buttons[position].config(command=lambda pos=position: self.clear_numbers(pos))

        # 绑定投注单位按钮
        for unit, button in self.ui.bet_unit_buttons.items():
            unit_text = {"1": "元", "0.1": "角", "0.01": "分"}[unit]
            button.config(command=lambda u=unit, t=unit_text: self.logic.set_bet_unit(u, t))

        # 绑定倍数菜单
        for i in range(self.ui.multiple_menu.index(tk.END) + 1):
            try:
                value = self.ui.multiple_menu.entryconfig(i, "label")[-1]
                self.ui.multiple_menu.entryconfig(i, command=lambda v=value: self.logic.set_multiple(v))
            except:
                pass

        # 绑定确认和投注按钮
        self.ui.confirm_button.config(command=self.confirm_selection)
        self.ui.place_bet_button.config(command=self.place_bet)

    def confirm_get_trial(self):
        """确认获取试玩账号"""
        if messagebox.askyesno("确认", "是否获取试玩账号？\n\n注意：试玩次数有限制，请谨慎使用"):
            self.logic.load_trial_account()

    def toggle_auto_refresh(self):
        """切换自动刷新状态"""
        is_auto = self.logic.toggle_auto_refresh()
        self.ui.set_auto_refresh_button_text(is_auto)

    def select_position(self, position):
        """选择位置"""
        self.ui.select_position(position)
        self.logic.select_position(position)

    def toggle_number(self, position, number):
        """切换号码选中状态"""
        is_selected = self.logic.toggle_number(position, int(number))
        self.ui.update_number_button_style(position, int(number), is_selected)
        self.update_total_amount()

    def select_all_numbers(self, position):
        """全选号码"""
        selected_numbers = self.logic.select_all_numbers(position)
        for number in selected_numbers:
            self.ui.update_number_button_style(position, number, True)
        self.update_total_amount()

    def clear_numbers(self, position):
        """清除选中的号码"""
        self.logic.clear_numbers(position)
        for number in range(1, 11):
            self.ui.update_number_button_style(position, number, False)
        self.update_total_amount()

    def update_total_amount(self):
        """更新总金额"""
        total = self.logic.calculate_total_amount()
        self.ui.set_total_amount(total)

    def confirm_selection(self):
        """确认选号"""
        total = self.logic.calculate_total_amount()
        if total > 0:
            messagebox.showinfo("确认选号", f"已选择号码，总金额: {total:.2f} 元")
        else:
            messagebox.showwarning("提示", "请先选择号码")

    def place_bet(self):
        """投注"""
        success, message = self.logic.place_bet()
        if success:
            messagebox.showinfo("投注成功", message)
            self.load_order_history()
        else:
            messagebox.showerror("投注失败", message)

    def load_order_history(self):
        """加载投注记录"""
        history_data = self.logic.load_order_history()
        self.ui.clear_history_table()
        for item in history_data:
            self.ui.add_history_item(
                item.get("period", "--"),
                item.get("amount", "0.00"),
                item.get("status", "--"),
                item.get("time", "--")
            )

    def on_bet_result(self, success, message, result):
        """投注结果回调"""
        if success:
            messagebox.showinfo("投注成功", message)
            self.load_order_history()
        else:
            messagebox.showerror("投注失败", message)

    def on_tab_changed(self, event):
        """选项卡切换事件"""
        try:
            current_tab = self.notebook.select()
            tab_text = self.notebook.tab(current_tab, "text")
            
            if tab_text == "试玩":
                self.logger.info("切换到试玩选项卡")
                # 启动游戏信息自动更新
                self.logic.start_game_info_auto_update()
            else:
                # 停止游戏信息自动更新
                self.logic.stop_game_info_auto_update()
        except Exception as e:
            self.logger.error(f"处理选项卡切换事件时出错: {str(e)}")

    def cleanup(self):
        """清理资源"""
        try:
            self.logic.cleanup()
            self.logger.info("试玩选项卡资源已清理")
        except Exception as e:
            self.logger.error(f"清理试玩选项卡资源时出错: {str(e)}")
