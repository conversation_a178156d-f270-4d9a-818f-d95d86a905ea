#!/usr/bin/env python
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, messagebox
import datetime
import time
import threading
from urllib.parse import urljoin
import requests
import json
from bs4 import BeautifulSoup
import urllib3

# 禁用未验证HTTPS请求的警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

from utils.logger import Logger
from core.session import SessionConfig
from game_info import GameInfo

# 从SessionConfig获取BASE_URL
BASE_URL = SessionConfig.BASE_URL

class GameInfoUI:
    def __init__(self, parent, session, game_info):
        """初始化游戏信息UI组件
        
        Args:
            parent: 父容器
            session: 请求会话
            game_info: GameInfo实例
        """
        # 创建日志记录器
        self.logger = Logger("GameInfoUI")
        self.parent = parent
        self.session = session
        self.game_info = game_info
        
        # 创建游戏信息框架
        self.game_info_frame = ttk.LabelFrame(parent, text="游戏信息", padding=10)
        self.game_info_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 游戏信息布局
        self.game_info_container = ttk.Frame(self.game_info_frame)
        self.game_info_container.pack(fill=tk.X, expand=True)
        
        # 游戏图标区域
        self.game_icon_label = ttk.Label(self.game_info_container)
        self.game_icon_label.grid(row=0, column=0, rowspan=2, padx=5, pady=5)
        
        # 游戏名称区域
        self.game_name_label = ttk.Label(self.game_info_container, text="168极速赛车", font=("Arial", 12, "bold"))
        self.game_name_label.grid(row=0, column=1, sticky=tk.W, padx=5)
        
        # 官方开奖网链接
        self.official_site_link = ttk.Label(self.game_info_container, text="官方开奖网", foreground="blue", cursor="hand2")
        self.official_site_link.grid(row=0, column=2, sticky=tk.W, padx=5)
        self.official_site_link.bind("<Button-1>", self.open_official_site)
        
        # 期号信息区域
        self.period_frame = ttk.Frame(self.game_info_container)
        self.period_frame.grid(row=1, column=1, columnspan=2, sticky=tk.W, padx=5)
        
        self.period_label = ttk.Label(self.period_frame, text="第 0000000 期")
        self.period_label.pack(side=tk.LEFT, padx=5)
        
        self.period_status_label = ttk.Label(self.period_frame, text="等待下期开盘")
        self.period_status_label.pack(side=tk.LEFT, padx=5)
        
        # 上一期信息区域
        self.previous_period_frame = ttk.Frame(self.game_info_container)
        self.previous_period_frame.grid(row=2, column=0, columnspan=5, sticky=tk.W, padx=5, pady=(5, 0))
        
        ttk.Label(self.previous_period_frame, text="上期期号:").pack(side=tk.LEFT, padx=(0, 5))
        self.previous_period_label = ttk.Label(self.previous_period_frame, text="--")
        self.previous_period_label.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Label(self.previous_period_frame, text="开奖号码:").pack(side=tk.LEFT, padx=(0, 5))
        self.previous_result_label = ttk.Label(self.previous_period_frame, text="--")
        self.previous_result_label.pack(side=tk.LEFT)
        
        # 倒计时区域
        self.countdown_frame = ttk.Frame(self.game_info_container)
        self.countdown_frame.grid(row=0, column=3, rowspan=2, padx=10)
        
        self.countdown_label = ttk.Label(self.countdown_frame, text="00:00:00", font=("Arial", 14, "bold"))
        self.countdown_label.pack()
        
        ttk.Label(self.countdown_frame, text="剩余投注时间").pack()
        
        # 刷新游戏信息按钮
        self.refresh_game_info_btn = ttk.Button(
            self.game_info_container,
            text="刷新游戏信息",
            command=self.refresh_game_info
        )
        self.refresh_game_info_btn.grid(row=0, column=4, rowspan=2, padx=10)
        
        # 倒计时线程相关
        self.countdown_running = False
        self.countdown_thread = None
        self.remaining_seconds = 0
        
        # 初始化投注界面
        self.init_betting_ui()
        
        # 启动游戏信息自动更新
        self.start_game_info_auto_update()

    def init_betting_ui(self):
        """初始化投注界面"""
        try:
            # 创建投注界面框架
            self.betting_frame = ttk.LabelFrame(self.parent, text="投注界面", padding=10)
            self.betting_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
            
            # 创建投注面板
            self.betting_panel = ttk.PanedWindow(self.betting_frame, orient=tk.HORIZONTAL)
            self.betting_panel.pack(fill=tk.BOTH, expand=True)
            
            # 左侧投注区域
            self.left_betting_frame = ttk.Frame(self.betting_panel)
            self.betting_panel.add(self.left_betting_frame, weight=3)
            
            # 右侧投注记录区域
            self.right_betting_frame = ttk.Frame(self.betting_panel)
            self.betting_panel.add(self.right_betting_frame, weight=1)
            
            # 初始化投注区域
            self.init_left_betting_area()
            
            # 初始化投注记录区域
            self.init_right_betting_area()
            
            self.logger.info("投注界面初始化完成")
        except Exception as e:
            self.logger.info(f"初始化投注界面时出错: {str(e)}")

    def init_left_betting_area(self):
        """初始化左侧投注区域"""
        try:
            # 创建位置选择框架
            self.position_frame = ttk.LabelFrame(self.left_betting_frame, text="位置选择", padding=5)
            self.position_frame.pack(fill=tk.X, padx=5, pady=5)
            
            # 创建位置选择下拉框
            self.positions = ["冠军", "亚军", "季军", "第四", "第五", "第六", "第七", "第八", "第九", "第十"]
            self.selected_position = tk.StringVar(value=self.positions[0])
            
            position_combo = ttk.Combobox(
                self.position_frame,
                textvariable=self.selected_position,
                values=self.positions,
                state="readonly"
            )
            position_combo.pack(fill=tk.X, padx=5, pady=5)
            
            # 创建号码选择框架
            self.numbers_frame = ttk.LabelFrame(self.left_betting_frame, text="号码选择", padding=5)
            self.numbers_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
            
            # 创建号码选择网格
            self.number_buttons = []
            for i in range(10):
                row_frame = ttk.Frame(self.numbers_frame)
                row_frame.pack(fill=tk.X, padx=5, pady=2)
                
                for j in range(10):
                    button = ttk.Button(
                        row_frame,
                        text=f"{i*10+j+1:02d}",
                        width=4,
                        command=lambda num=i*10+j+1: self.toggle_number(num)
                    )
                    button.pack(side=tk.LEFT, padx=2, pady=2)
                    self.number_buttons.append(button)
            
            # 创建快捷按钮框架
            self.quick_buttons_frame = ttk.Frame(self.left_betting_frame)
            self.quick_buttons_frame.pack(fill=tk.X, padx=5, pady=5)
            
            # 添加快捷按钮
            quick_buttons = ["全", "大", "小", "单", "双", "清"]
            for btn_text in quick_buttons:
                ttk.Button(
                    self.quick_buttons_frame,
                    text=btn_text,
                    width=4,
                    command=lambda text=btn_text: self.handle_quick_button(text)
                ).pack(side=tk.LEFT, padx=5)
            
            self.logger.info("左侧投注区域初始化完成")
        except Exception as e:
            self.logger.info(f"初始化左侧投注区域时出错: {str(e)}")

    def init_right_betting_area(self):
        """初始化右侧投注记录区域"""
        try:
            # 创建投注记录框架
            self.bet_records_frame = ttk.LabelFrame(self.right_betting_frame, text="投注记录", padding=5)
            self.bet_records_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
            
            # 创建投注记录列表
            self.bet_records_list = ttk.Treeview(
                self.bet_records_frame,
                columns=("位置", "号码", "金额", "状态"),
                show="headings"
            )
            
            # 设置列标题
            self.bet_records_list.heading("位置", text="位置")
            self.bet_records_list.heading("号码", text="号码")
            self.bet_records_list.heading("金额", text="金额")
            self.bet_records_list.heading("状态", text="状态")
            
            # 设置列宽度
            self.bet_records_list.column("位置", width=50)
            self.bet_records_list.column("号码", width=100)
            self.bet_records_list.column("金额", width=50)
            self.bet_records_list.column("状态", width=50)
            
            self.bet_records_list.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
            
            # 创建滚动条
            scrollbar = ttk.Scrollbar(
                self.bet_records_frame,
                orient=tk.VERTICAL,
                command=self.bet_records_list.yview
            )
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            self.bet_records_list.configure(yscrollcommand=scrollbar.set)
            
            self.logger.info("右侧投注记录区域初始化完成")
        except Exception as e:
            self.logger.info(f"初始化右侧投注记录区域时出错: {str(e)}")

    def toggle_number(self, number):
        """切换号码选择状态
        
        Args:
            number: 要切换的号码
        """
        try:
            button = self.number_buttons[number-1]
            current_style = button.cget("style")
            
            if current_style == "TButton":
                button.configure(style="Selected.TButton")
            else:
                button.configure(style="TButton")
                
            self.logger.info(f"切换号码 {number} 的选择状态")
        except Exception as e:
            self.logger.info(f"切换号码状态时出错: {str(e)}")

    def handle_quick_button(self, button_type):
        """处理快捷按钮点击
        
        Args:
            button_type: 快捷按钮类型 ("全", "大", "小", "单", "双", "清")
        """
        try:
            self.logger.info(f"处理快捷按钮: {button_type}")
            
            if button_type == "全":
                self.select_all_numbers()
            elif button_type == "大":
                self.select_large_numbers()
            elif button_type == "小":
                self.select_small_numbers()
            elif button_type == "单":
                self.select_odd_numbers()
            elif button_type == "双":
                self.select_even_numbers()
            elif button_type == "清":
                self.clear_all_numbers()
        except Exception as e:
            self.logger.info(f"处理快捷按钮时出错: {str(e)}")

    def select_all_numbers(self):
        """选择所有号码"""
        for button in self.number_buttons:
            button.configure(style="Selected.TButton")

    def select_large_numbers(self):
        """选择大号 (6-10位)"""
        for i, button in enumerate(self.number_buttons):
            if 60 <= i+1 <= 100:
                button.configure(style="Selected.TButton")
            else:
                button.configure(style="TButton")

    def select_small_numbers(self):
        """选择小号 (1-5位)"""
        for i, button in enumerate(self.number_buttons):
            if 1 <= i+1 <= 50:
                button.configure(style="Selected.TButton")
            else:
                button.configure(style="TButton")

    def select_odd_numbers(self):
        """选择奇数"""
        for i, button in enumerate(self.number_buttons):
            if (i+1) % 2 != 0:
                button.configure(style="Selected.TButton")
            else:
                button.configure(style="TButton")

    def select_even_numbers(self):
        """选择偶数"""
        for i, button in enumerate(self.number_buttons):
            if (i+1) % 2 == 0:
                button.configure(style="Selected.TButton")
            else:
                button.configure(style="TButton")

    def clear_all_numbers(self):
        """清除所有号码选择"""
        for button in self.number_buttons:
            button.configure(style="TButton")

    def add_bet_record(self, position, numbers, amount, status="待确认"):
        """添加投注记录
        
        Args:
            position: 投注位置
            numbers: 投注号码列表
            amount: 投注金额
            status: 投注状态
        """
        try:
            # 将号码列表转换为字符串
            numbers_str = ",".join(map(str, numbers))
            
            # 添加到记录列表
            self.bet_records_list.insert("", "end", values=(position, numbers_str, amount, status))
            
            self.logger.info(f"添加投注记录: 位置={position}, 号码={numbers_str}, 金额={amount}, 状态={status}")
        except Exception as e:
            self.logger.info(f"添加投注记录时出错: {str(e)}")

    def update_game_info_from_api(self, manual_refresh=False):
        """使用API接口数据更新游戏信息
        
        Args:
            manual_refresh: 是否是手动刷新，如果是手动刷新则更新游戏名称
            
        Returns:
            bool: 是否成功更新游戏信息
        """
        try:
            # 生成时间戳
            current_time = datetime.datetime.now()
            timestamp = int(current_time.timestamp() * 1000)
            self.logger.info(f"当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S.%f')}")
            
            # 构建 API URL
            api_url = f"{BASE_URL}/lotData/getNewPeriod.do?code=JSSC168&_={timestamp}"
            self.logger.info(f"请求游戏信息 API URL: {api_url}")
            
            # 添加请求头
            headers = {
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'X-Requested-With': 'XMLHttpRequest',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }
            
            # 发送请求
            response = self.session.get(api_url, headers=headers, verify=False, timeout=10)
            response.raise_for_status()
            
            # 解析响应
            data = response.json()
            self.logger.info(f"游戏信息 API 获取成功，当前期号: {data.get('fnumberofperiod', '--')}, 状态: {data.get('fstatus', '--')}")
            
            # 更新游戏信息
            # 使用API返回的游戏名称，如果有的话
            game_name = None
            
            # 尝试从数据中获取游戏名称
            if 'gameName' in data:
                game_name = data['gameName']
            elif 'game_name' in data:
                game_name = data['game_name']
            
            # 如果是手动刷新或者游戏名称为空，才更新游戏名称
            current_name = self.game_name_label.cget("text")
            if manual_refresh or not current_name or current_name == "--":
                if game_name:
                    self.game_name_label.config(text=game_name)
                    self.game_info.game_name = game_name
                elif 'code' in data:
                    game_code = data['code']
                    if game_code == "JSSC168":
                        self.game_name_label.config(text="168极速赛车")
                        self.game_info.game_name = "168极速赛车"
                    else:
                        self.game_name_label.config(text=game_code)
                        self.game_info.game_name = game_code
            
            # 更新期号信息
            if 'fnumberofperiod' in data:
                current_period = data['fnumberofperiod']
                self.period_label.config(text=f"第 {current_period} 期")
                self.game_info.current_period = current_period
            
            # 更新期号状态
            if 'fstatus' in data:
                status = data['fstatus']
                status_text = "未知状态"
                if status == 1:
                    status_text = "投注中"
                elif status == 2:
                    status_text = "已封盘"
                self.period_status_label.config(text=status_text)
                self.game_info.period_status = status_text
            
            # 更新倒计时
            remaining_time = "未知"
            self.remaining_seconds = 0
            
            if 'fclosetime' in data and 'ServerTime' in data:
                try:
                    # 解析时间
                    close_time = datetime.datetime.strptime(data['fclosetime'], "%Y/%m/%d %H:%M:%S")
                    server_time = datetime.datetime.strptime(data['ServerTime'], "%Y/%m/%d %H:%M:%S")
                    
                    # 计算剩余时间
                    time_diff = (close_time - server_time).total_seconds()
                    if time_diff > 0:
                        self.remaining_seconds = int(time_diff)
                        self.logger.info(f"获取到剩余投注时间: {self.remaining_seconds} 秒")
                        
                        minutes = int(time_diff // 60)
                        seconds = int(time_diff % 60)
                        remaining_time = f"{minutes:02d}:{seconds:02d}"
                    else:
                        remaining_time = "00:00"
                        self.remaining_seconds = 0
                except Exception as e:
                    self.logger.info(f"计算倒计时时出错: {str(e)}")
                    remaining_time = "--:--"
                    self.remaining_seconds = 0
            
            # 只有当没有正在运行的倒计时线程时才更新显示
            if not self.countdown_running:
                self.countdown_label.config(text=remaining_time)
            
            # 更新游戏信息对象中的倒计时
            self.game_info.remaining_time_str = remaining_time
            
            # 显示上一期开奖号码
            if 'fpreviousperiod' in data and 'fpreviousresult' in data:
                previous_period = data['fpreviousperiod']
                previous_result = data['fpreviousresult']
                self.logger.info(f"上一期({previous_period})开奖号码: {previous_result}")
                
                self.previous_period_label.config(text=previous_period)
                
                numbers = previous_result.split(',')
                formatted_result = ' '.join(numbers)
                self.previous_result_label.config(text=formatted_result)
                
                self.game_info.previous_result = previous_result
                self.game_info.previous_period = previous_period
            
            self.logger.info("使用 API 数据更新游戏信息成功")
            return True
        except Exception as e:
            self.logger.info(f"使用 API 数据更新游戏信息时出错: {str(e)}")
            return False

    def refresh_game_info(self):
        """刷新游戏信息（手动刷新）"""
        try:
            self.update_game_info_from_api(manual_refresh=True)
            self.logger.info("游戏信息刷新成功")
        except Exception as e:
            self.logger.info(f"刷新游戏信息时出错: {str(e)}")

    def open_official_site(self, event):
        """打开官方开奖网站"""
        try:
            import webbrowser
            webbrowser.open("https://www.168jss.com")
        except Exception as e:
            self.logger.info(f"打开官方开奖网站时出错: {str(e)}")

    def start_game_info_auto_update(self):
        """启动游戏信息自动更新"""
        try:
            if hasattr(self, '_api_update_thread'):
                self.stop_game_info_auto_update()
            
            self._api_update_thread = threading.Thread(target=self._api_update_task, daemon=True)
            self._api_update_thread.start()
            self.logger.info("启动游戏信息自动更新")
        except Exception as e:
            self.logger.info(f"启动游戏信息自动更新时出错: {str(e)}")

    def _api_update_task(self):
        """游戏信息自动更新后台任务"""
        try:
            while True:
                try:
                    self.update_game_info_from_api()
                    self.logger.info("游戏信息自动更新成功")
                except Exception as e:
                    self.logger.info(f"游戏信息自动更新时出错: {str(e)}")
                
                # 每5秒更新一次
                time.sleep(5)
        except Exception as e:
            self.logger.info(f"游戏信息自动更新线程出错: {str(e)}")

    def start_countdown(self, seconds):
        """启动倒计时线程
        
        Args:
            seconds: 倒计时秒数
        """
        try:
            if self.countdown_running:
                return
            
            self.remaining_seconds = seconds
            self.countdown_running = True
            
            self.countdown_thread = threading.Thread(target=self._countdown_task, args=(seconds,), daemon=True)
            self.countdown_thread.start()
            
            self.logger.info(f"启动倒计时线程，总时间: {seconds} 秒")
        except Exception as e:
            self.logger.info(f"启动倒计时线程时出错: {str(e)}")

    def place_bet(self):
        """立即下注"""
        try:
            # 检查是否有选择的号码
            has_selections = False
            total_bet_amount = 0
            bet_positions = []
            
            # 检查每个位置的选择情况
            for position in self.positions:
                selected_numbers = self.position_data[position]["selected_numbers"]
                if selected_numbers:
                    has_selections = True
                    try:
                        amount = float(self.position_data[position]["amount"].get())
                        position_total = amount * len(selected_numbers)
                        total_bet_amount += position_total
                        bet_positions.append(position)
                    except ValueError:
                        messagebox.showwarning("提示", f"{position} 的投注金额无效")
                        return
            
            if not has_selections:
                messagebox.showwarning("提示", "请至少选择一个位置和号码")
                return
            
            # 构建投注内容的描述信息
            bet_description = ""
            for position in bet_positions:
                selected_numbers = self.position_data[position]["selected_numbers"]
                amount = self.position_data[position]["amount"].get()
                bet_description += f"{position}: {', '.join(selected_numbers)} @ {amount}\n"
            
            # 确保总金额显示为整数
            total_bet_amount = int(total_bet_amount)  # 转换为整数
            
            # 确认投注
            confirm = messagebox.askyesno("确认投注", 
                                        f"投注详情:\n{bet_description}\n"
                                        f"总金额: {total_bet_amount}")
            if not confirm:
                return
            
            # 构建投注数据
            order_list = []
            for position in bet_positions:
                selected_numbers = self.position_data[position]["selected_numbers"]
                amount = float(self.position_data[position]["amount"].get())
                multiple = int(self.multiple.get())
                
                # 直接使用用户输入的金额
                amount = self.position_data[position]["amount"].get()
                total_amount = amount  # 直接使用用户输入的金额值
                
                # 获取车道编号（dwd_1到dwd_10）
                lane_number = self.positions.index(position) + 1
                lane_code = f"dwd_{lane_number}"
                
                for number in selected_numbers:
                    order_list.append({
                        "i": lane_code,      # 玩法代码
                        "c": number,         # 投注号码
                        "n": 1,              # 默认值
                        "t": str(multiple),  # 倍数转换为字符串
                        "k": 0,              # 默认值
                        "m": 1,              # 默认值
                        "a": int(total_amount)  # 总金额转换为整数
                    })
            
            # 构建请求参数
            params = {
                "code": "JSSC168",
                "qiHao": self.game_info.get_period(),  # 从游戏信息中获取期号
                "orderList": order_list,  # 直接使用列表
                "gameType": "official",  # 游戏类型
                "source": "0",           # 来源
                "terminal": "1"          # 终端类型
            }
            
            # 检查所有参数的类型
            self.logger.info("\n=== 参数类型检查 ===")
            for key, value in params.items():
                self.logger.info(f"参数 {key} 类型: {type(value).__name__}")
                if isinstance(value, list):
                    self.logger.info(f"  列表长度: {len(value)}")
                    for i, item in enumerate(value):
                        self.logger.info(f"  列表项 {i} 类型: {type(item).__name__}")
                        if isinstance(item, dict):
                            for sub_key, sub_value in item.items():
                                self.logger.info(f"    {sub_key}: {sub_value} ({type(sub_value).__name__})")
            
            # 序列化JSON
            json_data = json.dumps(params, separators=(',', ':'), ensure_ascii=False)
            self.logger.info(f"\n=== 序列化后的JSON ===")
            self.logger.info(f"JSON数据: {json_data}")
            self.logger.info(f"JSON长度: {len(json_data)}字节")
            
            # 发送投注请求
            url = urljoin(BASE_URL, "/bet/place")
            headers = {
                "User-Agent": "Mozilla/5.0",
                "Content-Type": "application/json",
                "Accept": "application/json"
            }
            try:
                # 在发送请求前打印完整的请求信息
                self.logger.info("\n=== 请求信息 ===")
                self.logger.info(f"请求URL: {url}")
                self.logger.info(f"请求头: {headers}")
                self.logger.info(f"请求体: {json_data}")
                
                # 发送请求
                response = self.session.post(
                    url,
                    data=json_data,
                    headers=headers,
                    verify=False,
                    timeout=10  # 设置超时时间
                )
                
                # 打印响应信息
                self.logger.info("\n=== 响应信息 ===")
                self.logger.info(f"响应状态码: {response.status_code}")
                self.logger.info(f"响应头: {dict(response.headers)}")
                self.logger.info(f"响应内容: {response.text}")
                
                # 尝试解析响应内容
                try:
                    result = response.json()
                    self.logger.info("\n=== 解析后的响应 ===")
                    self.logger.info(f"响应数据: {result}")
                    
                    # 检查响应中的错误信息
                    if isinstance(result, dict):
                        if 'error' in result:
                            self.logger.info(f"错误信息: {result['error']}")
                        if 'message' in result:
                            self.logger.info(f"错误消息: {result['message']}")
                        if 'code' in result and result['code'] != 0:
                            self.logger.info(f"状态码: {result['code']}")
                except json.JSONDecodeError:
                    self.logger.info("响应不是有效的JSON格式")
                    self.logger.info(f"原始响应: {response.text}")
                
                # 检查状态码
                if response.status_code != 200:
                    self.logger.info(f"非200状态码: {response.status_code}")
                    raise requests.exceptions.HTTPError(f"HTTP {response.status_code}")
                
            except requests.exceptions.Timeout:
                self.logger.info("请求超时")
                raise
            except requests.exceptions.ConnectionError:
                self.logger.info("连接错误")
                raise
            except requests.exceptions.RequestException as e:
                self.logger.info(f"请求异常: {str(e)}")
                raise
            except Exception as e:
                self.logger.info(f"未知错误: {str(e)}")
                raise
            
            # 处理响应
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    self.logger.info("投注成功")
                    messagebox.showinfo("成功", "投注成功！")
                else:
                    self.logger.info(f"投注失败: {result.get('message', '未知错误')}")
                    messagebox.showerror("失败", f"投注失败: {result.get('message', '未知错误')}")
            else:
                self.logger.info(f"服务器返回错误: {response.status_code}")
                messagebox.showerror("失败", f"服务器返回错误: {response.status_code}")
                
        except Exception as e:
            self.logger.info(f"下注时出错: {str(e)}")
            messagebox.showerror("错误", f"下注失败: {str(e)}")

    def _countdown_task(self, total_seconds):
        """倒计时任务
        
        Args:
            total_seconds: 总倒计时秒数
        """
        try:
            while self.remaining_seconds > 0:
                minutes = self.remaining_seconds // 60
                seconds = self.remaining_seconds % 60
                self.countdown_label.config(text=f"{minutes:02d}:{seconds:02d}")
                
                time.sleep(1)
                self.remaining_seconds -= 1
            
            self.countdown_label.config(text="00:00")
            self.countdown_running = False
            self.logger.info("倒计时结束")
        except Exception as e:
            self.logger.info(f"倒计时任务出错: {str(e)}")
            self.countdown_running = False

    def stop_game_info_auto_update(self):
        """停止游戏信息自动更新"""
        try:
            if hasattr(self, '_api_update_thread'):
                # 确保_api_update_thread是线程对象才调用is_alive方法
                if isinstance(self._api_update_thread, threading.Thread) and self._api_update_thread.is_alive():
                    # 使用join方法等待线程结束，而不是_stop
                    self._api_update_thread.join(0.1)
                del self._api_update_thread
            self.logger.info("停止游戏信息自动更新")
        except Exception as e:
            self.logger.info(f"停止游戏信息自动更新时出错: {str(e)}")

    def cleanup(self):
        """清理资源"""
        try:
            self.stop_game_info_auto_update()
            if self.countdown_running:
                self.countdown_running = False
                # 确保countdown_thread是线程对象才调用is_alive方法
                if self.countdown_thread and isinstance(self.countdown_thread, threading.Thread) and self.countdown_thread.is_alive():
                    # 使用join方法等待线程结束，而不是_stop
                    self.countdown_thread.join(0.1)
            self.logger.info("游戏信息 UI 组件资源已清理")
        except Exception as e:
            self.logger.info(f"清理游戏信息 UI 组件资源时出错: {str(e)}")
