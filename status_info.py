#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
状态信息模块
处理应用程序状态信息的显示
"""

import tkinter as tk
from tkinter import ttk
import time
import threading
from utils.logger import Logger, register_status_callback, unregister_status_callback

class StatusInfo:
    def __init__(self, parent):
        """初始化状态信息显示
        
        Args:
            parent: 父级容器
        """
        # 创建日志记录器
        self.logger = Logger("StatusInfo")
        self.parent = parent
        
        # 状态信息框架
        self.status_frame = None
        
        # 状态信息文本框
        self.status_text = None
        
        # 状态信息列表
        self.status_messages = []
        
        # 最大状态信息数量
        self.max_messages = 100
        
        # 锁，用于线程安全
        self.lock = threading.Lock()
        
        # 初始化UI
        self.init_ui()
        
        # 注册状态更新回调
        register_status_callback(self.update_status_from_log)
    
    def init_ui(self):
        """初始化状态信息UI"""
        # 创建状态信息框架
        self.status_frame = ttk.LabelFrame(self.parent, text="状态信息", padding=10)
        self.status_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建状态信息文本框
        self.status_text = tk.Text(self.status_frame, wrap=tk.WORD, height=10)
        self.status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(self.status_frame, orient=tk.VERTICAL, command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 设置文本框为只读
        self.status_text.config(state=tk.DISABLED)
        
        # 添加操作按钮
        button_frame = ttk.Frame(self.status_frame)
        button_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(button_frame, text="清空", command=self.clear_status).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="保存", command=self.save_status).pack(side=tk.LEFT, padx=5)
        
        self.logger.info("状态信息UI初始化完成")
    
    def update_status_from_log(self, message):
        """从日志更新状态信息
        
        Args:
            message: 日志消息
        """
        with self.lock:
            # 添加时间戳
            timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
            status_message = f"[{timestamp}] {message}"
            
            # 添加到状态信息列表
            self.status_messages.append(status_message)
            
            # 如果超过最大数量，删除最早的消息
            if len(self.status_messages) > self.max_messages:
                self.status_messages.pop(0)
            
            # 更新文本框
            self.status_text.config(state=tk.NORMAL)
            self.status_text.delete(1.0, tk.END)
            
            for msg in self.status_messages:
                self.status_text.insert(tk.END, f"{msg}\n")
            
            # 滚动到底部
            self.status_text.see(tk.END)
            
            # 设置为只读
            self.status_text.config(state=tk.DISABLED)
    
    def add_status(self, message):
        """添加状态信息
        
        Args:
            message: 状态消息
        """
        # 直接调用日志函数，会通过回调更新状态信息
        self.logger.info(message)
    
    def clear_status(self):
        """清空状态信息"""
        with self.lock:
            # 清空状态信息列表
            self.status_messages = []
            
            # 清空文本框
            self.status_text.config(state=tk.NORMAL)
            self.status_text.delete(1.0, tk.END)
            self.status_text.config(state=tk.DISABLED)
            
            self.logger.info("已清空状态信息")
    
    def save_status(self):
        """保存状态信息到文件"""
        try:
            # 获取当前时间作为文件名
            timestamp = time.strftime("%Y%m%d%H%M%S")
            filename = f"status_{timestamp}.log"
            
            # 写入文件
            with open(filename, "w", encoding="utf-8") as f:
                for message in self.status_messages:
                    f.write(f"{message}\n")
            
            self.logger.info(f"状态信息已保存到文件: {filename}")
        except Exception as e:
            self.logger.info(f"保存状态信息失败: {str(e)}")
    
    def cleanup(self):
        """清理资源"""
        # 取消注册状态更新回调
        unregister_status_callback(self.update_status_from_log)
        
        self.logger.info("状态信息资源已清理")
