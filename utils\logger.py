#!/usr/bin/env python
# -*- coding: utf-8 -*-

import datetime
import os

# 全局状态更新回调函数列表
status_update_callbacks = []

def register_status_callback(callback):
    """
    注册状态更新回调函数
    
    Args:
        callback: 接收消息字符串的函数
    """
    if callback not in status_update_callbacks:
        status_update_callbacks.append(callback)

def unregister_status_callback(callback):
    """
    取消注册状态更新回调函数
    
    Args:
        callback: 之前注册的回调函数
    """
    if callback in status_update_callbacks:
        status_update_callbacks.remove(callback)

def debug_log(message):
    """
    记录调试信息并更新状态
    
    Args:
        message: 要记录的消息
    """
    log_file = "login_debug.log"
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # 确保日志目录存在
    log_dir = os.path.dirname(log_file)
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir)
        
    with open(log_file, "a", encoding="utf-8") as f:
        f.write(f"[{timestamp}] {message}\n")
    print(message)
    
    # 调用所有注册的状态更新回调函数
    for callback in status_update_callbacks:
        try:
            callback(message)
        except Exception as e:
            print(f"调用状态更新回调时出错: {str(e)}")
            # 出错时不中断其他回调

class Logger:
    """
    日志记录器类，支持不同级别的日志记录和格式化
    """
    
    LEVELS = {
        "DEBUG": 10,
        "INFO": 20,
        "WARNING": 30,
        "ERROR": 40,
        "CRITICAL": 50
    }
    
    def __init__(self, name, log_file=None, level="DEBUG"):
        """
        初始化日志记录器
        
        Args:
            name: 日志记录器名称
            log_file: 日志文件路径，默认为None（使用默认日志文件）
            level: 日志级别，默认为DEBUG
        """
        self.name = name
        self.log_file = log_file or "login_debug.log"
        self.level = self.LEVELS.get(level.upper(), 10)
        
    def _log(self, level, message):
        """
        记录日志的内部方法
        
        Args:
            level: 日志级别
            message: 日志消息
        """
        if self.LEVELS.get(level.upper(), 0) >= self.level:
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            log_message = f"[{timestamp}] [{level}] [{self.name}] {message}"
            
            # 确保日志目录存在
            log_dir = os.path.dirname(self.log_file)
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir)
                
            with open(self.log_file, "a", encoding="utf-8") as f:
                f.write(log_message + "\n")
                
            print(log_message)
            
            # 调用所有注册的状态更新回调函数
            for callback in status_update_callbacks:
                try:
                    callback(message)
                except Exception as e:
                    print(f"调用状态更新回调时出错: {str(e)}")
    
    def debug(self, message):
        """记录DEBUG级别日志"""
        self._log("DEBUG", message)
    
    def info(self, message):
        """记录INFO级别日志"""
        self._log("INFO", message)
    
    def warning(self, message):
        """记录WARNING级别日志"""
        self._log("WARNING", message)
    
    def error(self, message):
        """记录ERROR级别日志"""
        self._log("ERROR", message)
    
    def critical(self, message):
        """记录CRITICAL级别日志"""
        self._log("CRITICAL", message)

# 创建默认日志记录器
default_logger = Logger("Default")
