#!/usr/bin/env python
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk
import warnings
import urllib3

# 禁用未验证HTTPS请求的警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 导入自定义模块
from login_tab import LoginTab
from user_info_tab import UserInfoTab
from dan_tab import DanTab
from ui.trial_tab import TrialTab  # 使用新的UI架构
from core.session import SessionManager
from utils.logger import Logger

class LoginApp:
    def __init__(self, root):
        """初始化应用程序"""
        self.root = root
        self.root.title("自动登录系统")
        self.root.geometry("1700x1250")
        
        # 创建日志记录器
        self.logger = Logger("主程序")
        
        # 初始化会话管理器
        self.session_manager = SessionManager()
        self.session = self.session_manager.create_session()
        self.username = ""
        self.balance = "0"
        self.user_links = []
        self.is_logged_in = False
        
        # 尝试加载之前的会话
        self.load_session_and_validate()
        
        # 创建选项卡控件
        self.notebook = ttk.Notebook(root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 初始化各个选项卡
        self.init_tabs()
        
        # 检查是否已登录，如果已登录则切换到用户信息选项卡
        if self.is_logged_in:
            self.notebook.select(self.user_info_tab.tab)
    
    def get_new_session(self):
        """创建新的会话"""
        return self.session_manager.create_session()
    
    def load_session_and_validate(self):
        """加载会话并验证"""
        try:
            # 加载会话
            session_data = self.session_manager.load_session()
            # 确保session_data是字典类型
            if session_data and isinstance(session_data, dict):
                self.session = session_data.get('session')
                self.username = session_data.get('username', '')
                self.balance = session_data.get('balance', '0')
                self.user_links = session_data.get('user_links', [])
                self.is_logged_in = session_data.get('is_logged_in', False)
                
                # 验证会话是否有效
                if self.session_manager.verify_session(self.session, self.is_logged_in):
                    # 会话有效
                    self.logger.info("会话有效，自动登录成功")
                    self.is_logged_in = True
                else:
                    # 会话无效，需要重新登录
                    self.logger.warning("会话无效，需要重新登录")
                    self.is_logged_in = False
                    # 创建新会话
                    self.session = self.get_new_session()
            else:
                self.logger.warning("没有找到会话文件，需要重新登录")
                self.is_logged_in = False
        except Exception as e:
            self.logger.error(f"加载会话时出错: {str(e)}")
            self.is_logged_in = False
            self.session = self.get_new_session()
    
    def init_tabs(self):
        """初始化所有选项卡"""
        # 初始化登录选项卡
        self.login_tab = LoginTab(self.root, self.notebook, self)
        
        # 初始化用户信息选项卡
        self.user_info_tab = UserInfoTab(self.root, self.notebook, self)
        
        # 初始化胆选项卡
        self.dan_tab = DanTab(self.root, self.notebook, self)
        
        # 初始化试玩选项卡
        self.trial_tab = TrialTab(self.root, self.notebook, self)
    
    def update_user_info_display(self):
        """更新用户信息显示"""
        try:
            # 如果存在用户信息选项卡，更新显示
            if hasattr(self, 'user_info_tab'):
                self.user_info_tab.update_display()
            else:
                self.logger.warning("用户信息选项卡未初始化，先初始化")
                self.user_info_tab = UserInfoTab(self.root, self.notebook, self)
                self.user_info_tab.update_display()
        except Exception as e:
            self.logger.error(f"更新用户信息显示时出错: {str(e)}")

# 定义主函数
def main():
    # 创建主窗口
    root = tk.Tk()
    app = LoginApp(root)
    
    # 添加关闭时的清理函数
    def on_closing():
        try:
            # 清理所有选项卡的资源
            if hasattr(app, 'trial_tab') and app.trial_tab:
                app.trial_tab.cleanup()
            
            # 清理胆选项卡的资源
            if hasattr(app, 'dan_tab') and app.dan_tab:
                app.dan_tab.cleanup()
            
            # 清理其他可能的资源
            if hasattr(app, 'user_info_tab') and app.user_info_tab:
                if hasattr(app.user_info_tab, 'cleanup'):
                    app.user_info_tab.cleanup()
            
            app.logger.info("应用程序正在关闭，资源清理完成")
        except Exception as e:
            app.logger.error(f"应用程序关闭时清理资源出错: {str(e)}")
        root.destroy()
    
    # 绑定关闭事件
    root.protocol("WM_DELETE_WINDOW", on_closing)
    
    # 启动主循环
    root.mainloop()

# 程序入口
if __name__ == "__main__":
    main()
