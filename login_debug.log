[2025-06-04 18:50:28] [INFO] [SessionManager] 没有找到会话文件，需要重新登录
[2025-06-04 18:50:28] [WARNING] [主程序] 没有找到会话文件，需要重新登录
[2025-06-04 18:50:28] [INFO] [LoginTab] 登录表单初始化完成
[2025-06-04 18:50:30] [INFO] [UserInfoTab] 用户信息选项卡初始化完成
[2025-06-04 18:50:30] [INFO] [DanTab] 胆选项卡初始化完成
[2025-06-04 18:51:11] [INFO] [试玩选项卡] 切换到试玩选项卡
[2025-06-04 18:51:11] [DEBUG] [ApiClient] 发送 GET 请求到: https://jk7859.jvvpbv2580513aknu.com:59789/trial.do
[2025-06-04 18:51:12] [DEBUG] [ApiClient] 请求成功，状态码: 404
[2025-06-04 18:51:12] [ERROR] [ApiClient] 获取试玩页面失败，状态码: 404
[2025-06-04 18:51:12] [ERROR] [GameInfo] 获取游戏页面失败
[2025-06-04 18:51:21] [INFO] [试玩选项卡] 切换到试玩选项卡
[2025-06-04 18:51:21] [DEBUG] [ApiClient] 发送 GET 请求到: https://jk7859.jvvpbv2580513aknu.com:59789/trial.do
[2025-06-04 18:51:21] [DEBUG] [ApiClient] 请求成功，状态码: 404
[2025-06-04 18:51:21] [ERROR] [ApiClient] 获取试玩页面失败，状态码: 404
[2025-06-04 18:51:21] [ERROR] [GameInfo] 获取游戏页面失败
[2025-06-04 18:51:23] [INFO] [试玩选项卡] 切换到试玩选项卡
[2025-06-04 18:51:23] [DEBUG] [ApiClient] 发送 GET 请求到: https://jk7859.jvvpbv2580513aknu.com:59789/trial.do
[2025-06-04 18:51:24] [DEBUG] [ApiClient] 请求成功，状态码: 404
[2025-06-04 18:51:24] [ERROR] [ApiClient] 获取试玩页面失败，状态码: 404
[2025-06-04 18:51:24] [ERROR] [GameInfo] 获取游戏页面失败
[2025-06-04 18:51:26] [INFO] [试玩选项卡] 切换到试玩选项卡
[2025-06-04 18:51:26] [DEBUG] [ApiClient] 发送 GET 请求到: https://jk7859.jvvpbv2580513aknu.com:59789/trial.do
[2025-06-04 18:51:27] [DEBUG] [ApiClient] 请求成功，状态码: 404
[2025-06-04 18:51:27] [ERROR] [ApiClient] 获取试玩页面失败，状态码: 404
[2025-06-04 18:51:27] [ERROR] [GameInfo] 获取游戏页面失败
[2025-06-04 18:51:29] [INFO] [试玩选项卡] 试玩逻辑资源已清理
[2025-06-04 18:51:29] [INFO] [试玩选项卡] 试玩选项卡资源已清理
[2025-06-04 18:51:29] [INFO] [主程序] 应用程序正在关闭，资源清理完成
[2025-06-04 18:55:23] [INFO] [SessionManager] 没有找到会话文件，需要重新登录
[2025-06-04 18:55:23] [WARNING] [主程序] 没有找到会话文件，需要重新登录
[2025-06-04 18:55:23] [INFO] [LoginTab] 登录表单初始化完成
[2025-06-04 18:55:24] [INFO] [UserInfoTab] 用户信息选项卡初始化完成
[2025-06-04 18:55:24] [INFO] [DanTab] 胆选项卡初始化完成
[2025-06-04 18:56:41] [INFO] [SessionManager] 没有找到会话文件，需要重新登录
[2025-06-04 18:56:41] [WARNING] [主程序] 没有找到会话文件，需要重新登录
[2025-06-04 18:56:41] [INFO] [LoginTab] 登录表单初始化完成
[2025-06-04 18:56:42] [INFO] [UserInfoTab] 用户信息选项卡初始化完成
[2025-06-04 18:56:42] [INFO] [DanTab] 胆选项卡初始化完成
[2025-06-04 18:56:44] [INFO] [试玩选项卡] 切换到试玩选项卡
[2025-06-04 18:56:44] [DEBUG] [ApiClient] 尝试获取试玩页面: normalTrialPlay.do
[2025-06-04 18:56:44] [DEBUG] [ApiClient] 发送 GET 请求到: https://jk7859.jvvpbv2580513aknu.com:59789/normalTrialPlay.do
[2025-06-04 18:56:46] [DEBUG] [ApiClient] 请求成功，状态码: 200
[2025-06-04 18:56:46] [INFO] [ApiClient] 成功获取试玩页面: normalTrialPlay.do
[2025-06-04 18:56:46] [INFO] [GameInfo] 成功找到游戏信息区域
[2025-06-04 18:56:46] [DEBUG] [GameInfo] 游戏图标: https://qt.xh88888888tp.com:7859/wvadSKkTC.gif
[2025-06-04 18:56:46] [DEBUG] [GameInfo] 游戏名称: 极速赛车
[2025-06-04 18:56:46] [INFO] [GameInfo] 成功获取游戏信息: 极速赛车, 期号: , 状态: 等待下期开盘
[2025-06-04 18:56:48] [WARNING] [试玩选项卡] 请先获取试玩账号
[2025-06-04 18:56:50] [WARNING] [试玩选项卡] 请先获取试玩账号
[2025-06-04 18:56:51] [DEBUG] [ApiClient] 尝试获取试玩页面: normalTrialPlay.do
[2025-06-04 18:56:51] [DEBUG] [ApiClient] 发送 GET 请求到: https://jk7859.jvvpbv2580513aknu.com:59789/normalTrialPlay.do
[2025-06-04 18:56:52] [DEBUG] [ApiClient] 请求成功，状态码: 200
[2025-06-04 18:56:52] [INFO] [ApiClient] 成功获取试玩页面: normalTrialPlay.do
[2025-06-04 18:56:52] [INFO] [GameInfo] 成功找到游戏信息区域
[2025-06-04 18:56:52] [DEBUG] [GameInfo] 游戏图标: https://qt.xh88888888tp.com:7859/wvadSKkTC.gif
[2025-06-04 18:56:52] [DEBUG] [GameInfo] 游戏名称: 极速赛车
[2025-06-04 18:56:52] [INFO] [GameInfo] 成功获取游戏信息: 极速赛车, 期号: , 状态: 等待下期开盘
[2025-06-04 18:56:57] [WARNING] [试玩选项卡] 请先获取试玩账号
[2025-06-04 18:56:57] [DEBUG] [ApiClient] 尝试获取试玩页面: normalTrialPlay.do
[2025-06-04 18:56:57] [DEBUG] [ApiClient] 发送 GET 请求到: https://jk7859.jvvpbv2580513aknu.com:59789/normalTrialPlay.do
[2025-06-04 18:56:58] [DEBUG] [ApiClient] 请求成功，状态码: 200
[2025-06-04 18:56:58] [INFO] [ApiClient] 成功获取试玩页面: normalTrialPlay.do
[2025-06-04 18:56:58] [INFO] [GameInfo] 成功找到游戏信息区域
[2025-06-04 18:56:58] [DEBUG] [GameInfo] 游戏图标: https://qt.xh88888888tp.com:7859/wvadSKkTC.gif
[2025-06-04 18:56:58] [DEBUG] [GameInfo] 游戏名称: 极速赛车
[2025-06-04 18:56:58] [INFO] [GameInfo] 成功获取游戏信息: 极速赛车, 期号: , 状态: 等待下期开盘
[2025-06-04 18:57:01] [WARNING] [试玩选项卡] 请先获取试玩账号
[2025-06-04 18:57:03] [DEBUG] [ApiClient] 尝试获取试玩页面: normalTrialPlay.do
[2025-06-04 18:57:03] [DEBUG] [ApiClient] 发送 GET 请求到: https://jk7859.jvvpbv2580513aknu.com:59789/normalTrialPlay.do
[2025-06-04 18:57:04] [INFO] [试玩选项卡] 已开启自动刷新 (间隔: 30秒)
[2025-06-04 18:57:04] [DEBUG] [ApiClient] 请求成功，状态码: 200
[2025-06-04 18:57:04] [INFO] [ApiClient] 成功获取试玩页面: normalTrialPlay.do
[2025-06-04 18:57:04] [INFO] [GameInfo] 成功找到游戏信息区域
[2025-06-04 18:57:04] [DEBUG] [GameInfo] 游戏图标: https://qt.xh88888888tp.com:7859/wvadSKkTC.gif
[2025-06-04 18:57:04] [DEBUG] [GameInfo] 游戏名称: 极速赛车
[2025-06-04 18:57:04] [INFO] [GameInfo] 成功获取游戏信息: 极速赛车, 期号: , 状态: 等待下期开盘
[2025-06-04 18:57:07] [INFO] [试玩选项卡] 已关闭自动刷新
[2025-06-04 18:57:09] [DEBUG] [ApiClient] 尝试获取试玩页面: normalTrialPlay.do
[2025-06-04 18:57:09] [DEBUG] [ApiClient] 发送 GET 请求到: https://jk7859.jvvpbv2580513aknu.com:59789/normalTrialPlay.do
[2025-06-04 18:57:09] [INFO] [试玩选项卡] 已开启自动刷新 (间隔: 30秒)
[2025-06-04 18:57:10] [DEBUG] [ApiClient] 请求成功，状态码: 200
[2025-06-04 18:57:10] [INFO] [ApiClient] 成功获取试玩页面: normalTrialPlay.do
[2025-06-04 18:57:10] [INFO] [GameInfo] 成功找到游戏信息区域
[2025-06-04 18:57:10] [DEBUG] [GameInfo] 游戏图标: https://qt.xh88888888tp.com:7859/wvadSKkTC.gif
[2025-06-04 18:57:10] [DEBUG] [GameInfo] 游戏名称: 极速赛车
[2025-06-04 18:57:10] [INFO] [GameInfo] 成功获取游戏信息: 极速赛车, 期号: , 状态: 等待下期开盘
[2025-06-04 18:57:12] [INFO] [试玩选项卡] 已关闭自动刷新
[2025-06-04 18:57:14] [INFO] [试玩选项卡] 正在获取试玩账号...
[2025-06-04 18:57:14] [DEBUG] [ApiClient] 发送 GET 请求到: https://jk7859.jvvpbv2580513aknu.com:59789/normalTrialPlay.do
[2025-06-04 18:57:15] [DEBUG] [ApiClient] 请求成功，状态码: 200
[2025-06-04 18:57:15] [INFO] [ApiClient] 成功获取试玩账号信息
[2025-06-04 18:57:15] [INFO] [试玩选项卡] 成功获取试玩账号: trial_4635, 余额: 10000.00
[2025-06-04 18:57:15] [DEBUG] [ApiClient] 尝试获取试玩页面: normalTrialPlay.do
[2025-06-04 18:57:15] [DEBUG] [ApiClient] 发送 GET 请求到: https://jk7859.jvvpbv2580513aknu.com:59789/normalTrialPlay.do
[2025-06-04 18:57:16] [DEBUG] [ApiClient] 请求成功，状态码: 200
[2025-06-04 18:57:16] [INFO] [ApiClient] 成功获取试玩页面: normalTrialPlay.do
[2025-06-04 18:57:16] [WARNING] [GameInfo] 使用 div.game_info 选择器未找到游戏信息区域
[2025-06-04 18:57:16] [WARNING] [GameInfo] 使用 find('div', class_='game_info') 仍然未找到游戏信息区域
[2025-06-04 18:57:16] [WARNING] [GameInfo] 使用部分匹配仍然未找到游戏信息区域
[2025-06-04 18:57:16] [INFO] [GameInfo] 使用硬编码的游戏信息
[2025-06-04 18:57:21] [DEBUG] [ApiClient] 尝试获取试玩页面: normalTrialPlay.do
[2025-06-04 18:57:21] [DEBUG] [ApiClient] 发送 GET 请求到: https://jk7859.jvvpbv2580513aknu.com:59789/normalTrialPlay.do
[2025-06-04 18:57:21] [DEBUG] [ApiClient] 请求成功，状态码: 200
[2025-06-04 18:57:21] [INFO] [ApiClient] 成功获取试玩页面: normalTrialPlay.do
[2025-06-04 18:57:21] [WARNING] [GameInfo] 使用 div.game_info 选择器未找到游戏信息区域
[2025-06-04 18:57:21] [WARNING] [GameInfo] 使用 find('div', class_='game_info') 仍然未找到游戏信息区域
[2025-06-04 18:57:21] [WARNING] [GameInfo] 使用部分匹配仍然未找到游戏信息区域
[2025-06-04 18:57:21] [INFO] [GameInfo] 使用硬编码的游戏信息
[2025-06-04 18:57:25] [INFO] [试玩选项卡] 试玩逻辑资源已清理
[2025-06-04 18:57:25] [INFO] [试玩选项卡] 试玩选项卡资源已清理
[2025-06-04 18:57:25] [INFO] [主程序] 应用程序正在关闭，资源清理完成
[2025-06-04 18:57:48] [INFO] [试玩选项卡] 试玩逻辑资源已清理
[2025-06-04 18:57:48] [INFO] [试玩选项卡] 试玩选项卡资源已清理
[2025-06-04 18:57:48] [INFO] [主程序] 应用程序正在关闭，资源清理完成
