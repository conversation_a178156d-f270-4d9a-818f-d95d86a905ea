    def update_status_from_log(self, message):
        """
        从日志更新状态显示
        
        Args:
            message: 日志消息
        """
        self.status_text.config(state=tk.NORMAL)
        self.status_text.insert(tk.END, f"{message}\n")
        self.status_text.see(tk.END)
        self.status_text.config(state=tk.DISABLED)
    
    def _start_api_update_task(self):
        """启动API更新任务"""
        if self._api_update_thread is not None and self._api_update_thread.is_alive():
            return  # 已经在运行中
        
        self._api_update_running = True
        self._api_update_thread = threading.Thread(target=self._api_update_task, daemon=True)
        self._api_update_thread.start()
    
    def _stop_api_update_task(self):
        """停止API更新任务"""
        self._api_update_running = False
        if self._api_update_thread is not None:
            self._api_update_thread.join(timeout=1.0)
            self._api_update_thread = None
    
    def _api_update_task(self):
        """API更新任务"""
        while self._api_update_running:
            try:
                # 只有在当前选项卡被选中时才更新
                current_tab = self.notebook.select()
                if current_tab == str(self.tab):
                    self.update_game_info_from_api()
                
                # 等待5秒
                for _ in range(5):
                    if not self._api_update_running:
                        break
                    time.sleep(1)
            except Exception as e:
                self.logger.error(f"API更新任务异常: {str(e)}")
                time.sleep(5)  # 出错后等待5秒再重试
    
    def update_game_info_from_api(self, manual_refresh=False):
        """
        从API更新游戏信息
        
        Args:
            manual_refresh: 是否为手动刷新
        """
        try:
            # 构建API请求URL
            current_time = datetime.datetime.now()
            timestamp = int(current_time.timestamp() * 1000)  # 毫秒级时间戳
            
            api_url = f"{BASE_URL}/lotData/getNewPeriod.do?code=JSSC168&_={timestamp}"
            
            # 发送请求
            response = self.session.get(api_url)
            response.raise_for_status()
            
            # 解析JSON
            data = response.json()
            
            if data.get("success"):
                # 提取游戏信息
                game_data = data.get("data", {})
                
                # 更新游戏名称（只在手动刷新时更新）
                if manual_refresh and "fname" in game_data:
                    self.game_name_label.config(text=game_data.get("fname", "极速赛车"))
                
                # 更新期号
                period = game_data.get("fnumberofperiod", "--")
                self.period_label.config(text=period)
                
                # 更新状态
                status_code = game_data.get("fstatus", 0)
                status_text = "投注中" if status_code == 1 else "已封盘" if status_code == 2 else "未知"
                self.status_label.config(text=status_text)
                
                # 更新倒计时
                server_time = data.get("serverTime", 0)
                close_time = game_data.get("fclosetime", 0)
                
                if close_time > server_time:
                    remaining_seconds = (close_time - server_time) // 1000
                    minutes = remaining_seconds // 60
                    seconds = remaining_seconds % 60
                    self.countdown_label.config(text=f"{minutes:02d}:{seconds:02d}")
                else:
                    self.countdown_label.config(text="00:00")
                
                # 更新上期开奖号码
                prev_period = game_data.get("fpreviousperiod", "--")
                prev_result = game_data.get("fpreviousresult", "--")
                
                if prev_period and prev_result:
                    self.last_result_label.config(text=f"{prev_period}: {prev_result}")
                else:
                    self.last_result_label.config(text="--")
                
                # 如果是手动刷新，显示日志
                if manual_refresh:
                    self.logger.info(f"游戏信息已刷新: 期号={period}, 状态={status_text}")
            else:
                self.logger.error(f"获取游戏信息失败: {data.get('msg', '未知错误')}")
        
        except Exception as e:
            self.logger.error(f"更新游戏信息异常: {str(e)}")
    
    def confirm_get_trial(self):
        """确认是否获取试玩账号"""
        if messagebox.askyesno("确认", "是否获取试玩账号？"):
            self.load_trial_account()
    
    def load_trial_account(self):
        """加载试玩账号信息"""
        self.logger.info("正在获取试玩账号...")
        
        try:
            # 构建请求URL
            url = f"{BASE_URL}/trial/getTrialAccount.do"
            
            # 发送请求
            response = self.session.get(url)
            response.raise_for_status()
            
            # 尝试不同的编码方式解析响应
            encodings = ['utf-8', 'gbk', 'gb2312', 'gb18030', 'big5']
            response_text = None
            
            for encoding in encodings:
                try:
                    response_text = response.content.decode(encoding)
                    break
                except UnicodeDecodeError:
                    continue
            
            if response_text is None:
                response_text = response.text  # 使用默认编码
            
            # 解析JSON
            try:
                data = json.loads(response_text)
                
                if data.get("success"):
                    account_data = data.get("data", {})
                    account = account_data.get("account", "未知")
                    balance = account_data.get("balance", "0")
                    
                    # 更新账号信息
                    self.account = account
                    self.balance = balance
                    self.account_label.config(text=account)
                    self.balance_label.config(text=balance)
                    
                    self.has_loaded_trial = True
                    self.logger.info(f"试玩账号已加载: 账号={account}, 余额={balance}")
                    
                    # 加载投注记录
                    self.load_order_history()
                    
                    # 刷新游戏信息
                    self.update_game_info_from_api(manual_refresh=True)
                    
                    return True
                else:
                    error_msg = data.get("msg", "未知错误")
                    self.logger.error(f"获取试玩账号失败: {error_msg}")
                    messagebox.showerror("获取失败", f"获取试玩账号失败: {error_msg}")
                    return False
            
            except json.JSONDecodeError:
                # 尝试使用正则表达式从响应中提取账号和余额
                account_match = re.search(r'账号[：:]\s*([A-Za-z0-9]+)', response_text)
                balance_match = re.search(r'余额[：:]\s*(\d+(?:\.\d+)?)', response_text)
                
                if account_match and balance_match:
                    account = account_match.group(1)
                    balance = balance_match.group(1)
                    
                    # 更新账号信息
                    self.account = account
                    self.balance = balance
                    self.account_label.config(text=account)
                    self.balance_label.config(text=balance)
                    
                    self.has_loaded_trial = True
                    self.logger.info(f"试玩账号已加载: 账号={account}, 余额={balance}")
                    
                    # 加载投注记录
                    self.load_order_history()
                    
                    # 刷新游戏信息
                    self.update_game_info_from_api(manual_refresh=True)
                    
                    return True
                else:
                    # 检查是否需要登录
                    if "login" in response_text.lower() or "登录" in response_text:
                        self.logger.error("会话已过期，请重新登录")
                        messagebox.showerror("获取失败", "会话已过期，请重新登录")
                    else:
                        self.logger.error("无法解析试玩账号信息")
                        messagebox.showerror("获取失败", "无法解析试玩账号信息")
                    return False
        
        except Exception as e:
            self.logger.error(f"获取试玩账号异常: {str(e)}")
            messagebox.showerror("获取失败", f"获取试玩账号异常: {str(e)}")
            return False
    
    def refresh_balance(self):
        """刷新账号余额"""
        if not self.has_loaded_trial:
            messagebox.showinfo("提示", "请先获取试玩账号")
            return
        
        self.logger.info("正在刷新账号余额...")
        
        try:
            # 构建请求URL
            url = f"{BASE_URL}/trial/getTrialAccount.do"
            
            # 发送请求
            response = self.session.get(url)
            response.raise_for_status()
            
            # 尝试不同的编码方式解析响应
            encodings = ['utf-8', 'gbk', 'gb2312', 'gb18030', 'big5']
            response_text = None
            
            for encoding in encodings:
                try:
                    response_text = response.content.decode(encoding)
                    break
                except UnicodeDecodeError:
                    continue
            
            if response_text is None:
                response_text = response.text  # 使用默认编码
            
            # 解析JSON
            try:
                data = json.loads(response_text)
                
                if data.get("success"):
                    account_data = data.get("data", {})
                    account = account_data.get("account", "未知")
                    balance = account_data.get("balance", "0")
                    
                    # 更新账号信息
                    self.account = account
                    self.balance = balance
                    self.account_label.config(text=account)
                    self.balance_label.config(text=balance)
                    
                    self.logger.info(f"账号余额已刷新: 账号={account}, 余额={balance}")
                    return True
                else:
                    error_msg = data.get("msg", "未知错误")
                    self.logger.error(f"刷新账号余额失败: {error_msg}")
                    messagebox.showerror("刷新失败", f"刷新账号余额失败: {error_msg}")
                    return False
            
            except json.JSONDecodeError:
                # 尝试使用正则表达式从响应中提取账号和余额
                account_match = re.search(r'账号[：:]\s*([A-Za-z0-9]+)', response_text)
                balance_match = re.search(r'余额[：:]\s*(\d+(?:\.\d+)?)', response_text)
                
                if account_match and balance_match:
                    account = account_match.group(1)
                    balance = balance_match.group(1)
                    
                    # 更新账号信息
                    self.account = account
                    self.balance = balance
                    self.account_label.config(text=account)
                    self.balance_label.config(text=balance)
                    
                    self.logger.info(f"账号余额已刷新: 账号={account}, 余额={balance}")
                    return True
                else:
                    # 检查是否需要登录
                    if "login" in response_text.lower() or "登录" in response_text:
                        self.logger.error("会话已过期，请重新登录")
                        messagebox.showerror("刷新失败", "会话已过期，请重新登录")
                    else:
                        self.logger.error("无法解析账号余额信息")
                        messagebox.showerror("刷新失败", "无法解析账号余额信息")
                    return False
        
        except Exception as e:
            self.logger.error(f"刷新账号余额异常: {str(e)}")
            messagebox.showerror("刷新失败", f"刷新账号余额异常: {str(e)}")
            return False
    
    def toggle_auto_refresh(self):
        """切换自动刷新状态"""
        self.auto_refresh = self.auto_refresh_var.get()
        if self.auto_refresh:
            self.logger.info("已开启自动刷新")
            self.start_auto_refresh()
        else:
            self.logger.info("已关闭自动刷新")
            self.stop_auto_refresh()
    
    def start_auto_refresh(self):
        """启动自动刷新"""
        if self.refresh_thread is not None and self.refresh_thread.is_alive():
            return  # 已经在运行中
        
        self.auto_refresh = True
        self.auto_refresh_var.set(True)
        
        def refresh_task():
            while self.auto_refresh:
                # 刷新账号余额
                self.refresh_balance()
                
                # 等待指定时间
                for _ in range(self.refresh_interval):
                    if not self.auto_refresh:
                        break
                    time.sleep(1)
        
        self.refresh_thread = threading.Thread(target=refresh_task, daemon=True)
        self.refresh_thread.start()
    
    def stop_auto_refresh(self):
        """停止自动刷新"""
        self.auto_refresh = False
        self.auto_refresh_var.set(False)
        if self.refresh_thread is not None:
            self.refresh_thread.join(timeout=1.0)
            self.refresh_thread = None
    
    def go_to_game(self):
        """进入游戏页面"""
        if not self.has_loaded_trial:
            messagebox.showinfo("提示", "请先获取试玩账号")
            return
        
        try:
            # 构建游戏URL
            game_url = f"{BASE_URL}/trial/index.do"
            
            # 尝试打开浏览器
            import webbrowser
            webbrowser.open(game_url)
            
            self.logger.info(f"已打开游戏页面: {game_url}")
        except Exception as e:
            self.logger.error(f"打开游戏页面异常: {str(e)}")
            messagebox.showerror("打开失败", f"打开游戏页面异常: {str(e)}")
    
    def load_order_history(self):
        """加载投注记录"""
        if not self.has_loaded_trial:
            return
        
        self.logger.info("正在加载投注记录...")
        
        try:
            # 清空现有记录
            for item in self.history_tree.get_children():
                self.history_tree.delete(item)
            
            # 构建请求URL
            url = f"{BASE_URL}/trial/getBetList.do"
            
            # 发送请求
            response = self.session.get(url)
            response.raise_for_status()
            
            # 解析JSON
            data = response.json()
            
            if data.get("success"):
                bet_list = data.get("data", [])
                
                # 添加记录到表格
                for bet in bet_list:
                    period = bet.get("period", "--")
                    amount = bet.get("amount", "0")
                    status = bet.get("status", "未知")
                    bet_time = bet.get("time", "--")
                    
                    self.history_tree.insert("", tk.END, values=(period, amount, status, bet_time))
                
                self.logger.info(f"已加载 {len(bet_list)} 条投注记录")
            else:
                self.logger.error(f"加载投注记录失败: {data.get('msg', '未知错误')}")
        
        except Exception as e:
            self.logger.error(f"加载投注记录异常: {str(e)}")
    
    def select_position(self, position):
        """
        选择位置
        
        Args:
            position: 位置名称
        """
        # 更新选中的位置
        self.selected_position = position
        
        # 更新位置按钮样式
        for pos, button in self.position_buttons.items():
            if pos == position:
                button.state(["pressed"])
            else:
                button.state(["!pressed"])
        
        # 显示对应的号码选择区域
        for pos, frame in self.number_frames.items():
            if pos == position:
                frame.pack(fill=tk.X, padx=5, pady=5)
            else:
                frame.pack_forget()
        
        self.logger.info(f"已选择位置: {position}")
    
    def toggle_number(self, position, number):
        """
        切换号码选中状态
        
        Args:
            position: 位置名称
            number: 号码
        """
        # 获取当前位置的选中号码列表
        selected_numbers = self.position_data[position]["selected_numbers"]
        
        # 切换选中状态
        if number in selected_numbers:
            selected_numbers.remove(number)
            self.number_buttons[position][number].state(["!pressed"])
        else:
            selected_numbers.append(number)
            self.number_buttons[position][number].state(["pressed"])
        
        # 更新总金额
        self.update_total_amount()
        
        self.logger.info(f"位置 {position} 的号码 {number} 选中状态已切换，当前选中: {', '.join(selected_numbers)}")
    
    def select_all_numbers(self, position):
        """
        全选号码
        
        Args:
            position: 位置名称
        """
        # 清空当前选中的号码
        self.position_data[position]["selected_numbers"] = []
        
        # 全选所有号码
        for number in range(1, 11):
            number_str = str(number).zfill(2)
            self.position_data[position]["selected_numbers"].append(number_str)
            self.number_buttons[position][number_str].state(["pressed"])
        
        # 更新总金额
        self.update_total_amount()
        
        self.logger.info(f"位置 {position} 的号码已全选")
    
    def clear_numbers(self, position):
        """
        清除选中的号码
        
        Args:
            position: 位置名称
        """
        # 清空当前选中的号码
        for number in self.position_data[position]["selected_numbers"]:
            self.number_buttons[position][number].state(["!pressed"])
        
        self.position_data[position]["selected_numbers"] = []
        
        # 更新总金额
        self.update_total_amount()
        
        self.logger.info(f"位置 {position} 的号码已清除")
    
    def update_position_amount(self, position):
        """
        更新位置金额
        
        Args:
            position: 位置名称
        """
        try:
            # 获取输入的金额
            amount = self.position_data[position]["amount"].get()
            
            # 验证金额是否为有效数字
            try:
                amount = float(amount)
                if amount <= 0:
                    raise ValueError("金额必须大于0")
                
                # 更新金额
                self.position_data[position]["amount"].set(str(amount))
            except ValueError:
                # 恢复为默认值
                self.position_data[position]["amount"].set("1")
                messagebox.showwarning("无效金额", "请输入有效的金额")
        
        except Exception as e:
            self.logger.error(f"更新位置金额异常: {str(e)}")
        
        # 更新总金额
        self.update_total_amount()
    
    def set_bet_unit(self, unit, text):
        """
        设置投注单位
        
        Args:
            unit: 单位值
            text: 单位文本
        """
        self.bet_unit.set(unit)
        self.bet_unit_text.set(text)
        
        # 更新总金额
        self.update_total_amount()
        
        self.logger.info(f"投注单位已设置为: {text}")
    
    def update_multiple(self, event=None):
        """
        更新倍数
        
        Args:
            event: 事件对象
        """
        try:
            # 获取输入的倍数
            multiple = self.multiple.get()
            
            # 验证倍数是否为有效数字
            try:
                multiple = int(multiple)
                if multiple <= 0:
                    raise ValueError("倍数必须大于0")
                
                # 更新倍数
                self.multiple.set(str(multiple))
            except ValueError:
                # 恢复为默认值
                self.multiple.set("1")
                messagebox.showwarning("无效倍数", "请输入有效的倍数")
        
        except Exception as e:
            self.logger.error(f"更新倍数异常: {str(e)}")
        
        # 更新总金额
        self.update_total_amount()
    
    def set_multiple(self, value):
        """
        设置倍数
        
        Args:
            value: 倍数值
        """
        self.multiple.set(value)
        
        # 更新总金额
        self.update_total_amount()
        
        self.logger.info(f"倍数已设置为: {value}")
    
    def show_multiple_menu(self, event):
        """
        显示倍数菜单
        
        Args:
            event: 事件对象
        """
        try:
            self.multiple_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.multiple_menu.grab_release()
    
    def update_total_amount(self):
        """更新总金额"""
        try:
            # 基础金额（每注2元）
            base_amount = 2
            
            # 获取投注单位
            unit = float(self.bet_unit.get())
            
            # 获取倍数
            try:
                multiple = int(self.multiple.get())
            except ValueError:
                multiple = 1
            
            # 计算总注数
            total_bets = 0
            for position in self.positions:
                total_bets += len(self.position_data[position]["selected_numbers"])
            
            # 计算总金额
            total = base_amount * multiple * unit * total_bets
            
            # 更新显示
            self.total_amount.set(f"{total:.2f}")
        
        except Exception as e:
            self.logger.error(f"更新总金额异常: {str(e)}")
            self.total_amount.set("0.00")
    
    def confirm_selection(self):
        """确认选号"""
        if not self.has_loaded_trial:
            messagebox.showinfo("提示", "请先获取试玩账号")
            return
        
        # 计算总注数和总金额
        total_bets = 0
        for position in self.positions:
            total_bets += len(self.position_data[position]["selected_numbers"])
        
        if total_bets == 0:
            messagebox.showinfo("提示", "请先选择号码")
            return
        
        # 获取总金额
        total = float(self.total_amount.get())
        
        # 显示确认信息
        message = f"您已选择 {total_bets} 注，共 {total:.2f} {self.bet_unit_text.get()}\n确认投注吗？"
        if messagebox.askyesno("确认投注", message):
            self.place_bet()
    
    def place_bet(self):
        """发送投注请求"""
        try:
            # 获取当前期号
            period = self.period_label.cget("text")
            if period == "--":
                messagebox.showinfo("提示", "无法获取当前期号")
                return
            
            # 获取投注单位
            unit = float(self.bet_unit.get())
            unit_text = self.bet_unit_text.get()
            
            # 获取倍数
            multiple = int(self.multiple.get())
            
            # 构建投注数据
            bet_data = []
            
            for position in self.positions:
                selected_numbers = self.position_data[position]["selected_numbers"]
                if not selected_numbers:
                    continue
                
                position_index = self.positions.index(position) + 1
                
                for number in selected_numbers:
                    bet_item = {
                        "position": position_index,
                        "number": number,
                        "amount": float(self.position_data[position]["amount"].get()),
                        "unit": unit,
                        "multiple": multiple
                    }
                    bet_data.append(bet_item)
            
            if not bet_data:
                messagebox.showinfo("提示", "请先选择号码")
                return
            
            # 构建请求URL和参数
            url = f"{BASE_URL}/trial/bet.do"
            
            params = {
                "period": period,
                "bets": json.dumps(bet_data),
                "gameType": "JSSC168",
                "source": "web",
                "terminal": "pc"
            }
            
            # 发送请求
            response = self.session.post(url, data=params)
            response.raise_for_status()
            
            # 解析JSON
            data = response.json()
            
            if data.get("success"):
                # 投注成功
                messagebox.showinfo("投注成功", "投注已成功提交")
                self.logger.info(f"投注成功: 期号={period}, 总金额={self.total_amount.get()}{unit_text}")
                
                # 刷新余额和投注记录
                self.refresh_balance()
                self.load_order_history()
                
                # 清除所有选中的号码
                for position in self.positions:
                    self.clear_numbers(position)
            else:
                # 投注失败
                error_msg = data.get("msg", "未知错误")
                messagebox.showerror("投注失败", f"投注失败: {error_msg}")
                self.logger.error(f"投注失败: {error_msg}")
        
        except Exception as e:
            self.logger.error(f"投注异常: {str(e)}")
            messagebox.showerror("投注异常", f"投注异常: {str(e)}")
    
    def show_bet_details(self, event):
        """
        显示投注详情
        
        Args:
            event: 事件对象
        """
        # 获取选中的项目
        selection = self.history_tree.selection()
        if not selection:
            return
        
        # 获取选中项的值
        item = self.history_tree.item(selection[0])
        values = item["values"]
        
        if not values or len(values) < 4:
            return
        
        # 显示详情
        period, amount, status, time = values
        details = f"期号: {period}\n金额: {amount}\n状态: {status}\n时间: {time}"
        messagebox.showinfo("投注详情", details)
    
    def on_tab_changed(self, event):
        """
        当选项卡切换时触发
        
        Args:
            event: 事件对象
        """
        current_tab = self.notebook.select()
        if current_tab == str(self.tab):
            # 当前选项卡被选中
            self.logger.info("试玩选项卡被选中")
            # 如果已加载试玩账号，刷新游戏信息
            if self.has_loaded_trial:
                self.update_game_info_from_api(manual_refresh=True)
    
    def cleanup(self):
        """清理资源，在应用程序关闭时调用"""
        self.logger.info("正在清理试玩选项卡资源...")
        
        # 停止自动刷新
        self.stop_auto_refresh()
        
        # 停止API更新任务
        self._stop_api_update_task()
        
        # 取消注册状态回调
        unregister_status_callback(self.update_status_from_log)
        
        self.logger.info("试玩选项卡资源已清理完毕")
