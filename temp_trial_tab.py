#!/usr/bin/env python
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin
from utils import debug_log, BASE_URL, register_status_callback, unregister_status_callback
import datetime
import threading
import time
import json
import re
from PIL import Image, ImageTk
import io
from game_info import GameInfo

class TrialTab:
    def __init__(self, parent, notebook, app, root=None):
        """初始化试玩选项卡"""
        self.parent = parent
        self.notebook = notebook
        self.app = app
        self.session = app.session
        self.root = root if root is not None else parent  # 兼容旧代码，优先用root参数
        
        # 注册状态更新回调
        register_status_callback(self.update_status_from_log)
        
        # 创建试玩选项卡
        self.tab = ttk.Frame(notebook)
        notebook.add(self.tab, text="试玩")
        
        # 账号信息
        self.account = ""
        self.balance = "0"
        self.has_loaded_trial = False  # 标记是否已加载试玩账号
        
        # 投注信息
        self.positions = ["冠军", "亚军", "季军", "第四", "第五", "第六", "第七", "第八", "第九", "第十"]
        
        # 默认选中的位置
        self.selected_position = "冠军"  # 默认选中冠军
        self.selected_numbers = []  # 兼容旧代码
        
        # 每个位置的选中号码和投注金额
        self.position_data = {}
        for position in self.positions:
            self.position_data[position] = {
                "selected_numbers": [],
                "amount": tk.StringVar(value="1")
            }
        
        # 总投注金额
        self.total_amount = tk.StringVar(value="0")
        
        # 设置默认投注模式为元
        self.bet_unit = tk.StringVar(value="1")
        self.bet_unit_text = tk.StringVar(value="元")
        
        # 设置默认倍数为1
        self.multiple = tk.StringVar(value="1")
        self.multiple_options = ["1", "5", "10", "15", "20", "50", "100", "200", "500", "1000", "2000"]
        
        # 自动刷新相关
        self.auto_refresh = False
        self.refresh_interval = 30  # 秒
        self.refresh_thread = None
        
        # 游戏信息相关
        self.game_info = GameInfo(self.session)
        self.game_icon_image = None  # 游戏图标图片对象
        self.game_icon_photo = None  # Tkinter PhotoImage对象
        
        # 日志记录
        self.logger = debug_log
        
        # API更新线程
        self._api_update_thread = None
        self._api_update_running = False
        
        # 初始化选项卡内容
        self.init_tab_content()
        
        # 不自动加载试玩账号信息，改为按钮触发
        # 不自动加载投注记录，在加载试玩账号后才加载
        
        # 监听选项卡切换
        self.notebook.bind("<<NotebookTabChanged>>", self.on_tab_changed)

    def init_tab_content(self):
        """初始化选项卡内容"""
        # 创建主容器（水平分隔）
        self.main_paned = ttk.PanedWindow(self.tab, orient=tk.HORIZONTAL)
        self.main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 左侧面板（投注区域）
        self.left_frame = ttk.Frame(self.main_paned)
        self.main_paned.add(self.left_frame, weight=5)
        
        # 右侧面板（状态信息区域）
        self.right_frame = ttk.Frame(self.main_paned)
        self.main_paned.add(self.right_frame, weight=1)
        
        # 初始化左侧面板
        self.init_left_panel()
        
        # 初始化右侧面板
        self.init_right_panel()
        
        # 选择默认位置
        self.select_position(self.selected_position)
        
        # 启动API游戏信息更新线程
        self._start_api_update_task()

    def init_left_panel(self):
        """初始化左侧面板"""
        # 游戏信息区域
        self.game_info_frame = ttk.LabelFrame(self.left_frame, text="游戏信息")
        self.game_info_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 游戏信息内容
        game_info_content = ttk.Frame(self.game_info_frame)
        game_info_content.pack(fill=tk.X, padx=5, pady=5)
        
        # 游戏图标和名称
        game_header = ttk.Frame(game_info_content)
        game_header.pack(fill=tk.X, pady=2)
        
        self.game_icon_label = ttk.Label(game_header)
        self.game_icon_label.pack(side=tk.LEFT, padx=5)
        
        self.game_name_label = ttk.Label(game_header, text="极速赛车", font=("宋体", 12, "bold"))
        self.game_name_label.pack(side=tk.LEFT, padx=5)
        
        refresh_button = ttk.Button(game_header, text="刷新", width=8, command=lambda: self.update_game_info_from_api(manual_refresh=True))
        refresh_button.pack(side=tk.RIGHT, padx=5)
        
        # 期号和状态
        period_frame = ttk.Frame(game_info_content)
        period_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(period_frame, text="期号:").pack(side=tk.LEFT, padx=5)
        self.period_label = ttk.Label(period_frame, text="--")
        self.period_label.pack(side=tk.LEFT, padx=5)
        
        ttk.Label(period_frame, text="状态:").pack(side=tk.LEFT, padx=5)
        self.status_label = ttk.Label(period_frame, text="--")
        self.status_label.pack(side=tk.LEFT, padx=5)
        
        # 倒计时
        countdown_frame = ttk.Frame(game_info_content)
        countdown_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(countdown_frame, text="倒计时:").pack(side=tk.LEFT, padx=5)
        self.countdown_label = ttk.Label(countdown_frame, text="--:--", font=("宋体", 10, "bold"))
        self.countdown_label.pack(side=tk.LEFT, padx=5)
        
        # 上期开奖号码
        last_result_frame = ttk.Frame(game_info_content)
        last_result_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(last_result_frame, text="上期开奖号码:").pack(side=tk.LEFT, padx=5)
        self.last_result_label = ttk.Label(last_result_frame, text="--")
        self.last_result_label.pack(side=tk.LEFT, padx=5)
        
        # 账号信息区域
        self.account_frame = ttk.LabelFrame(self.left_frame, text="试玩账号信息")
        self.account_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 账号信息内容
        account_content = ttk.Frame(self.account_frame)
        account_content.pack(fill=tk.X, padx=5, pady=5)
        
        # 账号和余额
        account_info = ttk.Frame(account_content)
        account_info.pack(fill=tk.X, pady=2)
        
        ttk.Label(account_info, text="账号:").pack(side=tk.LEFT, padx=5)
        self.account_label = ttk.Label(account_info, text="未登录")
        self.account_label.pack(side=tk.LEFT, padx=5)
        
        ttk.Label(account_info, text="余额:").pack(side=tk.LEFT, padx=5)
        self.balance_label = ttk.Label(account_info, text="0")
        self.balance_label.pack(side=tk.LEFT, padx=5)
        
        # 按钮区域
        button_frame = ttk.Frame(account_content)
        button_frame.pack(fill=tk.X, pady=2)
        
        self.get_trial_button = ttk.Button(button_frame, text="获取试玩账号", width=15, command=self.load_trial_account)
        self.get_trial_button.pack(side=tk.LEFT, padx=5)
        
        self.refresh_balance_button = ttk.Button(button_frame, text="刷新余额", width=10, command=self.refresh_balance)
        self.refresh_balance_button.pack(side=tk.LEFT, padx=5)
        
        self.auto_refresh_var = tk.BooleanVar(value=False)
        self.auto_refresh_button = ttk.Checkbutton(button_frame, text="自动刷新", variable=self.auto_refresh_var, command=self.toggle_auto_refresh)
        self.auto_refresh_button.pack(side=tk.LEFT, padx=5)
        
        self.game_button = ttk.Button(button_frame, text="进入游戏", width=10, command=self.go_to_game)
        self.game_button.pack(side=tk.RIGHT, padx=5)
        
        # 投注区域
        self.bet_frame = ttk.LabelFrame(self.left_frame, text="投注")
        self.bet_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 位置选择区域
        position_frame = ttk.Frame(self.bet_frame)
        position_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 位置按钮
        self.position_buttons = {}
        for i, position in enumerate(self.positions):
            style_name = f"Position.TButton.{position}"
            ttk.Style().configure(style_name, font=("宋体", 9))
            
            button = ttk.Button(position_frame, text=position, width=8, style=style_name,
                             command=lambda pos=position: self.select_position(pos))
            button.grid(row=i//5, column=i%5, padx=2, pady=2)
            self.position_buttons[position] = button
        
        # 号码选择区域
        self.number_frames = {}
        self.number_buttons = {}
        
        for position in self.positions:
            frame = ttk.Frame(self.bet_frame)
            self.number_frames[position] = frame
            
            # 号码按钮
            buttons = {}
            for i in range(1, 11):  # 1-10号码
                number = str(i).zfill(2)  # 格式化为两位数
                button = ttk.Button(frame, text=number, width=4,
                                  command=lambda pos=position, num=number: self.toggle_number(pos, num))
                button.grid(row=0, column=i-1, padx=2, pady=2)
                buttons[number] = button
            self.number_buttons[position] = buttons
            
            # 操作按钮和金额输入
            control_frame = ttk.Frame(frame)
            control_frame.grid(row=1, column=0, columnspan=10, pady=5, sticky=tk.W)
            
            # 全选和清除按钮
            self.all_buttons = {}
            self.clear_buttons = {}
            
            all_button = ttk.Button(control_frame, text="全选", width=6,
                                 command=lambda pos=position: self.select_all_numbers(pos))
            all_button.pack(side=tk.LEFT, padx=2)
            self.all_buttons[position] = all_button
            
            clear_button = ttk.Button(control_frame, text="清除", width=6,
                                   command=lambda pos=position: self.clear_numbers(pos))
            clear_button.pack(side=tk.LEFT, padx=2)
            self.clear_buttons[position] = clear_button
            
            # 金额输入
            ttk.Label(control_frame, text="金额:").pack(side=tk.LEFT, padx=(10, 2))
            
            self.amount_entries = {}
            amount_entry = ttk.Entry(control_frame, width=8, textvariable=self.position_data[position]["amount"])
            amount_entry.pack(side=tk.LEFT, padx=2)
            self.amount_entries[position] = amount_entry
            
            # 绑定金额输入框事件
            amount_entry.bind("<FocusOut>", lambda event, pos=position: self.update_position_amount(pos))
            amount_entry.bind("<Return>", lambda event, pos=position: self.update_position_amount(pos))
        
        # 投注控制区域
        control_frame = ttk.Frame(self.bet_frame)
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 投注单位
        unit_frame = ttk.LabelFrame(control_frame, text="投注单位")
        unit_frame.pack(side=tk.LEFT, padx=5, fill=tk.Y)
        
        self.bet_unit_buttons = {}
        
        yuan_button = ttk.Radiobutton(unit_frame, text="元", value="1", variable=self.bet_unit,
                                   command=lambda: self.set_bet_unit("1", "元"))
        yuan_button.pack(side=tk.LEFT, padx=5, pady=2)
        self.bet_unit_buttons["1"] = yuan_button
        
        jiao_button = ttk.Radiobutton(unit_frame, text="角", value="0.1", variable=self.bet_unit,
                                    command=lambda: self.set_bet_unit("0.1", "角"))
        jiao_button.pack(side=tk.LEFT, padx=5, pady=2)
        self.bet_unit_buttons["0.1"] = jiao_button
        
        fen_button = ttk.Radiobutton(unit_frame, text="分", value="0.01", variable=self.bet_unit,
                                   command=lambda: self.set_bet_unit("0.01", "分"))
        fen_button.pack(side=tk.LEFT, padx=5, pady=2)
        self.bet_unit_buttons["0.01"] = fen_button
        
        # 倍数
        multiple_frame = ttk.LabelFrame(control_frame, text="倍数")
        multiple_frame.pack(side=tk.LEFT, padx=5, fill=tk.Y)
        
        self.multiple_entry = ttk.Entry(multiple_frame, width=8, textvariable=self.multiple)
        self.multiple_entry.pack(side=tk.LEFT, padx=5, pady=2)
        
        self.multiple_button = ttk.Button(multiple_frame, text="▼", width=2)
        self.multiple_button.pack(side=tk.LEFT, padx=5, pady=2)
        
        # 创建倍数下拉菜单
        self.multiple_menu = tk.Menu(self.multiple_button, tearoff=0)
        for value in self.multiple_options:
            self.multiple_menu.add_command(
                label=value,
                command=lambda val=value: self.set_multiple(val)
            )
        
        # 绑定倍数按钮事件
        self.multiple_button.bind("<Button-1>", self.show_multiple_menu)
        
        # 总金额和确认按钮
        total_frame = ttk.Frame(control_frame)
        total_frame.pack(side=tk.RIGHT, padx=5, fill=tk.Y)
        
        ttk.Label(total_frame, text="总金额:").pack(side=tk.LEFT, padx=2)
        
        total_amount_label = ttk.Label(total_frame, textvariable=self.total_amount, font=("宋体", 10, "bold"))
        total_amount_label.pack(side=tk.LEFT, padx=2)
        
        ttk.Label(total_frame, textvariable=self.bet_unit_text).pack(side=tk.LEFT)
        
        self.confirm_button = ttk.Button(total_frame, text="确认选号", width=10, command=self.confirm_selection)
        self.confirm_button.pack(side=tk.LEFT, padx=10)
        
        # 投注记录区域
        history_frame = ttk.LabelFrame(self.left_frame, text="投注记录")
        history_frame.pack(fill=tk.BOTH, padx=5, pady=5)
        
        # 创建表格
        columns = ("期号", "金额", "状态", "时间")
        self.history_tree = ttk.Treeview(history_frame, columns=columns, show="headings", height=5)
        
        # 设置列标题
        for col in columns:
            self.history_tree.heading(col, text=col)
            self.history_tree.column(col, width=100)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(history_frame, orient="vertical", command=self.history_tree.yview)
        self.history_tree.configure(yscrollcommand=scrollbar.set)
        
        # 放置表格和滚动条
        self.history_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定双击事件
        self.history_tree.bind("<Double-1>", self.show_bet_details)

    def init_right_panel(self):
        """初始化右侧面板"""
        # 状态信息区域
        status_frame = ttk.LabelFrame(self.right_frame, text="状态信息")
        status_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建文本框
        self.status_text = scrolledtext.ScrolledText(status_frame, wrap=tk.WORD, width=30, height=30)
        self.status_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.status_text.config(state=tk.DISABLED)  # 设置为只读
