#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
import time
import random
import string
from utils.logger import Logger

def generate_random_string(length=8):
    """
    生成指定长度的随机字符串
    
    Args:
        length: 字符串长度，默认为8
        
    Returns:
        随机字符串
    """
    characters = string.ascii_letters + string.digits
    return ''.join(random.choice(characters) for _ in range(length))

# 创建日志记录器
logger = Logger("Helpers")

def safe_json_loads(json_str, default=None):
    """
    安全地解析JSON字符串，出错时返回默认值
    
    Args:
        json_str: JSON字符串
        default: 解析失败时返回的默认值
        
    Returns:
        解析后的对象或默认值
    """
    try:
        return json.loads(json_str)
    except Exception as e:
        logger.error(f"JSON解析错误: {str(e)}")
        return default if default is not None else {}

def safe_file_read(file_path, default="", encoding="utf-8"):
    """
    安全地读取文件内容，出错时返回默认值
    
    Args:
        file_path: 文件路径
        default: 读取失败时返回的默认值
        encoding: 文件编码
        
    Returns:
        文件内容或默认值
    """
    try:
        if not os.path.exists(file_path):
            return default
        with open(file_path, "r", encoding=encoding) as f:
            return f.read()
    except Exception as e:
        logger.error(f"文件读取错误 {file_path}: {str(e)}")
        return default

def safe_file_write(file_path, content, encoding="utf-8"):
    """
    安全地写入文件内容，确保目录存在
    
    Args:
        file_path: 文件路径
        content: 要写入的内容
        encoding: 文件编码
        
    Returns:
        是否成功写入
    """
    try:
        # 确保目录存在
        directory = os.path.dirname(file_path)
        if directory and not os.path.exists(directory):
            os.makedirs(directory)
            
        with open(file_path, "w", encoding=encoding) as f:
            f.write(content)
        return True
    except Exception as e:
        logger.error(f"文件写入错误 {file_path}: {str(e)}")
        return False

def retry_operation(operation, max_retries=3, delay=1, backoff=2, exceptions=(Exception,)):
    """
    重试操作，支持指数退避
    
    Args:
        operation: 要执行的操作函数
        max_retries: 最大重试次数
        delay: 初始延迟时间（秒）
        backoff: 退避因子
        exceptions: 捕获的异常类型
        
    Returns:
        操作结果或None（如果所有重试都失败）
    """
    retries = 0
    current_delay = delay
    
    while retries < max_retries:
        try:
            return operation()
        except exceptions as e:
            retries += 1
            if retries >= max_retries:
                logger.error(f"操作重试失败，达到最大重试次数: {str(e)}")
                return None
                
            logger.warning(f"操作失败，将在 {current_delay} 秒后重试: {str(e)}")
            time.sleep(current_delay)
            current_delay *= backoff
