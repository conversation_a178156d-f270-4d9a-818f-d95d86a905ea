# 自动登录与投注系统（模块化版）

## 项目说明
地址 https://jk7859.jvvpbv2580513aknu.com:59789/
试玩地址 https://jk7859.jvvpbv2580513aknu.com:59789/normalTrialPlay.do

该项目是一个自动登录与投注系统，支持登录、用户信息显示、胆选项卡和试玩投注功能。系统采用模块化结构设计，便于维护和扩展。最新版本对试玩选项卡进行了重构，将其拆分为多个功能模块，提高了代码的可维护性。

## 文件结构

### 核心文件
- `main.py` - 主应用程序入口，包含主框架和应用程序初始化
- `utils.py` - 通用工具函数、全局配置和状态回调机制

### 登录和用户信息
- `login_tab.py` - 登录选项卡功能实现
- `user_info_tab.py` - 用户信息选项卡功能实现
- `session.pkl` - 会话数据存储文件
- `user_info.json` - 用户信息存储文件

### 胆选项卡
- `dan_tab.py` - 胆选项卡功能实现

### 试玩选项卡（重构后 - 新架构）
- `ui/trial_tab.py` - 试玩选项卡控制器（新架构）
- `ui/trial_ui.py` - 试玩UI组件（新架构）
- `logic/trial_logic.py` - 试玩业务逻辑（新架构）
- `core/game_info.py` - 游戏信息管理模块
- `core/api.py` - API客户端模块
- `core/session.py` - 会话管理模块

### 工具模块
- `utils/logger.py` - 日志记录器
- `utils/helpers.py` - 辅助函数

## 如何使用

1. 运行主程序：
   ```
   python main.py
   ```

2. 程序会尝试加载之前的会话，如果会话有效则自动登录，否则需要手动登录。

3. 登录后可以访问以下功能：
   - **用户信息选项卡**：查看当前登录用户的详细信息
   - **胆选项卡**：进行胆码选择和投注
   - **试玩选项卡**：进行游戏试玩和投注模拟

4. 在试玩选项卡中：
   - 游戏信息区域显示当前期号、状态和倒计时
   - 投注区域可以选择号码、设置倍数和单位
   - 投注记录区域显示历史投注和开奖结果
   - 状态信息区域显示系统运行状态和日志

## 模块化架构

系统采用模块化架构设计，特别是试玩选项卡进行了完全的模块化重构：

1. **主框架**：`trial_tab.py` 作为容器，整合各个功能模块

2. **游戏信息模块**：
   - `game_info.py` - 负责从API获取游戏数据
   - `game_display.py` - 负责游戏信息的UI显示

3. **投注模块**：
   - `betting_ui.py` - 负责投注界面的UI组件
   - `betting_logic.py` - 负责投注逻辑和状态管理

4. **记录与状态模块**：
   - `bet_records.py` - 负责投注记录的管理和显示
   - `status_info.py` - 负责状态信息的显示和日志记录

## 新增功能模块

如果需要新增功能模块，请按照以下步骤：

1. 创建新的模块文件，例如 `new_module.py`

2. 在新文件中创建模块类，遵循单一职责原则：
   ```python
   class NewModule:
       def __init__(self, parent, other_dependencies):
           self.parent = parent
           # 初始化依赖关系
           
           # 创建UI组件
           self.init_ui()
           
           # 注册必要的回调
           self.register_callbacks()
       
       def init_ui(self):
           # 实现UI初始化
           pass
           
       def register_callbacks(self):
           # 注册回调函数
           pass
           
       def cleanup(self):
           # 清理资源
           pass
   ```

3. 在主选项卡中集成新模块：
   ```python
   from new_module import NewModule
   
   # 在选项卡初始化方法中：
   self.new_module = NewModule(self.tab, other_dependencies)
   
   # 在选项卡清理方法中：
   self.new_module.cleanup()
   ```

## 技术特性

### API集成
- 使用`/lotData/getNewPeriod.do`接口获取实时游戏信息
- 支持自动刷新和手动刷新
- 精确解析期号和游戏状态

### 投注功能
- 支持多种投注模式（元角分）
- 自动计算投注金额
- 实时显示投注状态和结果

### 倒计时系统
- 精确到毫秒的倒计时显示
- 自动处理投注和开奖阶段
- 倒计时结束自动刷新游戏信息

### 状态管理
- 全局状态信息显示
- 详细的日志记录
- 异常处理和错误恢复

## 注意事项

- 代码使用了 `verify=False` 禁用了 HTTPS 验证，在生产环境中应当启用 HTTPS 验证
- 会话信息和用户信息存储在本地文件中，请确保这些文件的安全性
- 应用程序使用多线程处理自动更新，确保在关闭应用时正确清理资源

## 常见问题

### 试玩页面404错误
如果在日志中看到以下错误信息：
```
[ERROR] [ApiClient] 获取试玩页面失败，状态码: 404
[ERROR] [GameInfo] 获取游戏页面失败
```

这是正常现象，不会影响程序运行。程序会：
1. 自动尝试多个备用URL
2. 如果所有URL都失败，会使用模拟数据
3. 试玩功能仍然可以正常使用

### 网络连接问题
- 检查网络连接是否正常
- 确认服务器地址是否可访问
- 查看 `login_debug.log` 文件获取详细错误信息

## 依赖安装

```
pip install requests beautifulsoup4 pillow
```

## 开发计划

- 优化倒计时功能，解决当前存在的'root'属性错误
- 完善投注记录的中奖检测功能
- 增强用户界面的响应性和美观性
- 添加更多游戏类型的支持


项目根目录/
├── main.py                    # 主程序
├── login_tab.py              # 登录选项卡
├── user_info_tab.py          # 用户信息选项卡
├── dan_tab.py                # 胆选项卡（简化版）
├── utils.py                  # 工具函数
├── README.md                 # 项目说明
├── core/                     # 核心模块
│   ├── __init__.py
│   ├── api.py               # API客户端
│   ├── game_info.py         # 游戏信息管理
│   └── session.py           # 会话管理
├── logic/                    # 业务逻辑
│   ├── __init__.py
│   └── trial_logic.py       # 试玩逻辑
├── ui/                       # 用户界面
│   ├── __init__.py
│   ├── trial_tab.py         # 试玩选项卡控制器
│   └── trial_ui.py          # 试玩UI组件
└── utils/                    # 工具包
    ├── __init__.py
    ├── helpers.py           # 辅助函数
    └── logger.py            # 日志记录器