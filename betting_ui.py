#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
投注界面UI组件模块
包含投注界面的所有UI组件和布局
"""

import tkinter as tk
from tkinter import ttk
from utils.logger import Logger

class BettingUI:
    def __init__(self, parent, tab):
        """初始化投注界面UI组件
        
        Args:
            parent: 父级容器
            tab: 所属的选项卡对象
        """
        # 创建日志记录器
        self.logger = Logger("BettingUI")
        self.parent = parent
        self.tab = tab
        
        # 投注界面框架
        self.bet_frame = None
        self.bet_canvas = None
        self.bet_inner_frame = None
        
        # 投注金额设置框架
        self.amount_frame = None
        self.multiplier_var = None
        self.multiplier_entry = None
        self.unit_var = None
        
        # 投注按钮
        self.bet_buttons = []
        self.selected_numbers = []
        
        # 初始化UI
        self.init_ui()
    
    def init_ui(self):
        """初始化投注界面UI"""
        # 创建投注界面框架
        self.bet_frame = ttk.LabelFrame(self.parent, text="投注界面", padding=10)
        self.bet_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建投注界面的滚动区域，以支持更多内容
        self.bet_canvas = tk.Canvas(self.bet_frame)
        self.bet_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(self.bet_frame, orient=tk.VERTICAL, command=self.bet_canvas.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.bet_canvas.configure(yscrollcommand=scrollbar.set)
        
        # 创建内部框架来存放所有投注元素
        self.bet_inner_frame = ttk.Frame(self.bet_canvas)
        self.bet_canvas.create_window((0, 0), window=self.bet_inner_frame, anchor=tk.NW)
        
        # 配置内部框架大小调整
        self.bet_inner_frame.bind("<Configure>", lambda e: self.bet_canvas.configure(scrollregion=self.bet_canvas.bbox("all")))
        
        # 设置最小宽度，确保内容正常显示
        self.bet_inner_frame.bind("<Configure>", lambda e: self.bet_canvas.configure(width=max(200, e.width)))
        self.bet_canvas.bind("<Configure>", lambda e: self.bet_inner_frame.configure(width=max(200, e.width)))
        
        # 创建金额选择框架
        self.create_amount_frame()
        
        # 创建投注按钮
        self.create_bet_buttons()
        
        self.logger.info("投注界面UI初始化完成")
    
    def create_amount_frame(self):
        """创建金额选择框架"""
        self.amount_frame = ttk.LabelFrame(self.bet_inner_frame, text="投注金额设置", padding=5)
        self.amount_frame.grid(row=4, column=0, columnspan=5, sticky=tk.EW, padx=5, pady=10)
        
        # 倍数选择
        ttk.Label(self.amount_frame, text="倍数:").grid(row=0, column=0, padx=5, pady=5)
        self.multiplier_var = tk.StringVar(value="1")
        self.multiplier_entry = ttk.Entry(self.amount_frame, textvariable=self.multiplier_var, width=5)
        self.multiplier_entry.grid(row=0, column=1, padx=5, pady=5)
        
        # 单位选择（元角分）
        ttk.Label(self.amount_frame, text="单位:").grid(row=0, column=2, padx=5, pady=5)
        self.unit_var = tk.StringVar(value="元")
        unit_frame = ttk.Frame(self.amount_frame)
        unit_frame.grid(row=0, column=3, padx=5, pady=5)
        
        ttk.Radiobutton(unit_frame, text="元", variable=self.unit_var, value="元").pack(side=tk.LEFT)
        ttk.Radiobutton(unit_frame, text="角", variable=self.unit_var, value="角").pack(side=tk.LEFT)
        ttk.Radiobutton(unit_frame, text="分", variable=self.unit_var, value="分").pack(side=tk.LEFT)
        
        # 总金额显示
        ttk.Label(self.amount_frame, text="总金额:").grid(row=0, column=4, padx=5, pady=5)
        self.total_amount_var = tk.StringVar(value="0.00 元")
        ttk.Label(self.amount_frame, textvariable=self.total_amount_var).grid(row=0, column=5, padx=5, pady=5)
        
        # 绑定事件，当倍数或单位改变时更新总金额
        self.multiplier_var.trace_add("write", self.update_total_amount)
        self.unit_var.trace_add("write", self.update_total_amount)
    
    def create_bet_buttons(self):
        """创建投注按钮"""
        # 创建号码选择区域
        numbers_frame = ttk.LabelFrame(self.bet_inner_frame, text="号码选择", padding=5)
        numbers_frame.grid(row=0, column=0, columnspan=5, sticky=tk.EW, padx=5, pady=5)
        
        # 创建10个号码按钮
        self.bet_buttons = []
        for i in range(10):
            number = i + 1
            btn = ttk.Button(
                numbers_frame, 
                text=f"{number:02d}",
                width=5,
                command=lambda n=number: self.toggle_number(n)
            )
            row = i // 5
            col = i % 5
            btn.grid(row=row, column=col, padx=5, pady=5)
            self.bet_buttons.append(btn)
        
        # 创建操作按钮
        operations_frame = ttk.Frame(self.bet_inner_frame)
        operations_frame.grid(row=1, column=0, columnspan=5, sticky=tk.EW, padx=5, pady=5)
        
        ttk.Button(operations_frame, text="全选", command=self.select_all).grid(row=0, column=0, padx=5, pady=5)
        ttk.Button(operations_frame, text="清空", command=self.clear_selection).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(operations_frame, text="投注", command=self.place_bet).grid(row=0, column=2, padx=5, pady=5)
    
    def toggle_number(self, number):
        """切换号码选择状态
        
        Args:
            number: 要切换的号码
        """
        if number in self.selected_numbers:
            self.selected_numbers.remove(number)
            self.bet_buttons[number-1].configure(style='TButton')
        else:
            self.selected_numbers.append(number)
            # 使用特殊样式标记选中的按钮
            # 注意：这里需要确保已经定义了相应的样式
            try:
                self.bet_buttons[number-1].configure(style='Selected.TButton')
            except:
                # 如果样式未定义，可以使用其他方式标记
                self.bet_buttons[number-1].configure(text=f"[{number:02d}]")
        
        # 更新总金额
        self.update_total_amount()
    
    def select_all(self):
        """全选所有号码"""
        self.selected_numbers = list(range(1, 11))
        for i, btn in enumerate(self.bet_buttons):
            try:
                btn.configure(style='Selected.TButton')
            except:
                btn.configure(text=f"[{i+1:02d}]")
        
        # 更新总金额
        self.update_total_amount()
    
    def clear_selection(self):
        """清空所有选择"""
        self.selected_numbers = []
        for i, btn in enumerate(self.bet_buttons):
            btn.configure(style='TButton')
            btn.configure(text=f"{i+1:02d}")
        
        # 更新总金额
        self.update_total_amount()
        
    def select_position(self, position):
        """选择投注位置
        
        Args:
            position: 位置名称
        """
        # BettingUI 不需要处理位置选择，但添加此方法以兼容调用
        self.logger.info(f"选择位置: {position}")
        # 这里可以根据需要添加位置选择的逻辑
    
    def update_total_amount(self, *args):
        """更新总投注金额"""
        try:
            # 获取倍数
            multiplier = int(self.multiplier_var.get())
            
            # 获取单位
            unit = self.unit_var.get()
            unit_value = 1.0 if unit == "元" else 0.1 if unit == "角" else 0.01
            
            # 计算总金额
            base_amount = 2  # 每注基础金额为2元
            total = base_amount * multiplier * unit_value * len(self.selected_numbers)
            
            # 更新显示
            self.total_amount_var.set(f"{total:.2f} 元")
        except ValueError:
            self.total_amount_var.set("输入有误")
    
    def place_bet(self):
        """执行投注操作"""
        if not self.selected_numbers:
            self.logger.warning("未选择任何号码，无法投注")
            return
        
        try:
            # 获取投注参数
            multiplier = int(self.multiplier_var.get())
            unit = self.unit_var.get()
            unit_value = 1.0 if unit == "元" else 0.1 if unit == "角" else 0.01
            
            # 计算总金额
            base_amount = 2  # 每注基础金额为2元
            total = base_amount * multiplier * unit_value * len(self.selected_numbers)
            
            # 获取选中的号码，格式化为两位数字
            formatted_numbers = [f"{n:02d}" for n in self.selected_numbers]
            
            # 调用选项卡的投注方法
            if hasattr(self.tab, 'do_place_bet'):
                self.tab.do_place_bet(formatted_numbers, multiplier, unit, total)
            else:
                self.logger.error("选项卡没有实现do_place_bet方法")
        except ValueError as e:
            self.logger.error(f"投注参数错误: {str(e)}")
    
    def get_betting_data(self):
        """获取当前投注数据
        
        Returns:
            dict: 包含投注数据的字典
        """
        try:
            multiplier = int(self.multiplier_var.get())
            unit = self.unit_var.get()
            unit_value = 1.0 if unit == "元" else 0.1 if unit == "角" else 0.01
            base_amount = 2
            total = base_amount * multiplier * unit_value * len(self.selected_numbers)
            
            return {
                'selected_numbers': self.selected_numbers,
                'formatted_numbers': [f"{n:02d}" for n in self.selected_numbers],
                'multiplier': multiplier,
                'unit': unit,
                'unit_value': unit_value,
                'total_amount': total
            }
        except ValueError:
            return None
