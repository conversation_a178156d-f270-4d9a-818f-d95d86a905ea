<!DOCTYPE HTML PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>- 极速赛车 - Welcome 7859</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta content="" name="keywords" />
    <meta content="" name="description" />
    <link rel="Shortcut Icon" href="/images/favicon.ico">
    <link href="/common/js/artDialog/dialog.css" rel="stylesheet" />
    <link href="/common/js/flipclock/flipclock.css" rel="stylesheet" />
    <link href="/common/lot/css/skin1/creditFrame.css?44" rel="stylesheet" />
    <link href="/common/lot/css/skin1/creditDefault.css?2.31" rel="stylesheet" />
</head><body>
<link rel="stylesheet" href="/common/lot/css/lotdialog.css?v=1.0.0"/>
<div class="Ccenter">
<div class="top">
	<div class="top_in clear">
    		<div class="top_menu">
            	<div class="clear">
            		<a href="/" class="getout" title="返回首页"><span class="return_index">返回首页</span></a>
                	<a href="/chatlink.html" target="_blank" class="service" title="在线客服"><span class="new-msg">在线客服</span></a>
                <a href="javascript:;" class="rule" data-bind="click:showRule" title="规则说明"><i></i><span class="rule_description">规则说明</span></a>
                <a title="退出账号" class="btn-login-out" href="/logout.do"><span>退出账号</span></a>
            </div>
        </div>
        <div class="game_info">
            <div class="game_ico"><img src="https://qt.xh88888888tp.com:7859/wvadSKkTC.gif"></div>
            <div class="game_mane "><span>极速赛车</span>
                    <a class="lot-network" href="https://www.7859kjw15.bet:59789/live.do" target="_blank">官方开奖网</a>
                </div>
            <div class="openinfo">
                <div class="drawNumber"><span class="c-period"><div>第 <b data-bind="text:periodInfo().fnumberofperiod">&nbsp;</b> 期</div><div class="time" data-bind="text:periodTip">&nbsp;</div></span>
                <span class="c-resttime" id="RestTime" style="float:left;width:128px;padding:0;"></span></div>
                <div class="now_time" id="Clock"></div>
            </div>
        </div>
        <div class="lottery_result game_pk10">
            <div data-bind="foreach:{data:prevPeriodResult,afterRender:$root.lotteryEffect||$.noop}" id="LotteryResultBox" class="lr_ball_box">
                <span class="lottery-result-ball" data-bind="css:'ball_'+($data=='?'?'00':$data)" style="position:relative;">
                    <label data-bind="text:$data">&nbsp;</label>
                    
                </span>
                
            </div>
            <div>
                <span class="lr_txt">第<span class="color-NoL fsL" data-bind="text:periodInfo().fpreviousperiod">&nbsp;</span>期</span>
                <span class="lr_txt"><a target="_blank" href="/lotData/result.do?code=FKSC&version=2">开奖结果</a></span>
                <span class="vol-item">
                    <a href="javascript:;" class="volume volume-off" data-bind="click:tracks.mute,css:'volume-'+(tracks.isMute()?'off':'on')"></a>
                    <div class="Ringtone select-box">
                        <div class="select-current"><span data-bind="text:tracks.trackName"></span><i></i></div>
                        <select class="select-list" data-bind="options:tracks.list,value:tracks.track,optionsText:'name',optionsValue:'value'"></select>
                    </div>
                    
                </span>
            </div>
        </div>
    </div>
</div>
<div class="container clear">
<div class="side">
    <div class="userbox">
        <ul class="stc_1 clear">
            <li class="mane">
                <span class="stc_t">
                                                账号：
                </span>
                <span class="stc_c">guest11158</span>
            </li>
            <li class="balance">
                <span class="stc_t">余额：</span><span class="stc_c stc_d" id="AvailableBalance" data-bind="click:refreshBalance.bind($data,'latest')" title="点击可刷新余额" style="cursor:pointer">2000</span>
            </li>
        </ul>
    </div>
    <div>
        <div class="acc-links clear" id="OpenNewWindow">
            <a title="在线存款" class="btn-navacc btn-navacc-deposit" href="/userCenter/finance/recharge/.do" target="_blank"><span title="在线存款">充值</span></a>
            <a title="在线取款" class="btn-navacc btn-navacc-withdraw" href="/userCenter/finance/withdraw.do" target="_blank"><span title="在线取款">提现</span></a>
            <a title="账变记录" class="btn-navacc btn-navacc-mail" href="/userCenter/report/reportOrder.do" target="_blank"><span title="账变记录">账变记录</span></a>
            <a title="游戏记录" class="btn-navacc btn-navacc-current" href="/userCenter/userOrder/lotGcOrder.do" target="_blank"><span>游戏记录</span></a>
            <a title="账户管理" class="btn-navacc btn-navacc-history" href="/userCenter/index.do" target="_blank"><span>账户管理</span></a>
            <a title="会员中心" class="btn-navacc btn-navacc-center" href="/userCenter/index.do" target="_blank"><span title="会员中心">会员中心</span></a>
        </div>
    </div>
    <div class="side-in">
        <div class="side_tab">
			<div class="" id="sideTab" style="overflow:hidden;">
                <div class="st_header clear" tab="header">
                    <a href="javascript:;" class="active black_bg">开奖结果</a>
                    <a href="javascript:;" class="black_bg">投注记录</a>
                </div>
                <div class="st_content" tab="content">
                    <div class="side-result">
                        <ul data-bind="foreach:resultHistoryList().slice(0,10),style:{display:resultHistoryList().length<=0?'none':''}">
                            <li>
                                <span class="sr-index" data-bind="text:$index()+1"></span>
                                <span class="sr-period" style="width:70px" data-bind="text:period"></span>
                                <div class="sr-nums" data-bind="attr:{title:result}"><span><i data-bind="html:result"></i></span></div>
                            </li>
                        </ul>
                        <ul class="top10T" data-bind="style:{display:resultHistoryList().length>=5?'':'none'}"><a target="_blank" href="/lotData/result.do?code=FKSC&version=2">更多开奖结果</a></ul>
                        <ul class="top10T" data-bind="style:{display:resultHistoryList().length<=0?'':'none'}">暂无开奖结果</ul>
                    </div>
                    
                    <div class="stc_2" style="display:none;">
                        <table class="top10-order">
                            <thead class="top10-order-header">
                                <tr class="top10-tr">
                                    <th class="top10-td-1">时间</th>
                                    <th class="top10-td-2">赔率</th>
                                    <th class="top10-td-3 noborder">金额</th>
                                </tr>
                            </thead>
                            <tbody class="top10-order-content">
                                <!-- ko if:top10().length===0 -->
                                <tr><td colspan="3" class="top10T noborder">暂无最新注单信息</td></tr>
                                <!-- /ko -->
                                <!-- ko foreach:top10 -->
                                <tr class="top10-tr">
                                    <td class="top10-td-1 bdtop" align="center" data-bind="text:cTime"></td>
                                    <td class="top10-td-2 bdtop" align="center" data-bind="text:odds"></td>
                                    <td class="top10-td-3 bdtop" align="center" data-bind="text:amount"></td>
                                </tr>
                                <tr><td colspan="3" class="top10-td-4 bdbottom" data-bind="text:content"></td></tr>
                                <!-- /ko -->
                            </tbody>
                            <tfoot class="top10-order-content">
                                <!-- ko if:top10().length > 0 -->
                                <tr><td colspan="3" class="top10T noborder"><a href="/userCenter/userOrder/lotGcOrder.do">更多记录</a></td></tr>
                                <!-- /ko -->
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="side_menu" id="sideMenu">
            <div class="sidem_item sidem_item_1 sidem_item_active">
                <a href="javascript:;" class="sidem_b clear"><i></i><span>赛车飞艇</span></a>
                <ul style="display:block">
<li><a data-key="FKSC" data-val="极速赛车" class="active" href="/credit/index.do?code=FKSC"><span><i></i>极速赛车</span></a></li><li><a data-key="FKFT" data-val="极速飞艇" href="/credit/index.do?code=FKFT"><span><i></i>极速飞艇</span></a></li><li><a data-key="FFSC" data-val="大发赛车" href="/credit/index.do?code=FFSC"><span><i></i>大发赛车</span></a></li><li><a data-key="AZFT" data-val="澳洲飞艇" href="/credit/index.do?code=AZFT"><span><i></i>澳洲飞艇</span></a></li><li><a data-key="AZXYT168" data-val="澳洲幸运10" href="/credit/index.do?code=AZXYT168"><span><i></i>澳洲幸运10</span></a></li><li><a data-key="BJSC" data-val="北京赛车" href="/credit/index.do?code=BJSC"><span><i></i>北京赛车</span></a></li><li><a data-key="XYFT" data-val="幸运飞艇" href="/credit/index.do?code=XYFT"><span><i></i>幸运飞艇</span></a></li><li><a data-key="XYFT2" data-val="168幸运飞艇" href="/credit/index.do?code=XYFT2"><span><i></i>168幸运飞艇</span></a></li><li><a data-key="JSSC168" data-val="168极速赛车" href="/credit/index.do?code=JSSC168"><span><i></i>168极速赛车</span></a></li><li><a data-key="SFSC" data-val="幸运赛车" href="/credit/index.do?code=SFSC"><span><i></i>幸运赛车</span></a></li><li><a data-key="YLSM" data-val="香港跑马" href="/credit/index.do?code=YLSM"><span><i></i>香港跑马</span></a></li>

                </ul>
            </div>
            <div class="sidem_item sidem_item_2">
                <a href="javascript:;" class="sidem_b clear"><i></i><span>分分彩</span></a>
                <ul style="display:none">
<li><a data-key="AMFC" data-val="极速时时彩" href="/credit/index.do?code=AMFC"><span><i></i>极速时时彩</span></a></li><li><a data-key="FFC" data-val="腾讯分分彩" href="/credit/index.do?code=FFC"><span><i></i>腾讯分分彩</span></a></li><li><a data-key="XGFC" data-val="大发时时彩" href="/credit/index.do?code=XGFC"><span><i></i>大发时时彩</span></a></li><li><a data-key="FFC2" data-val="澳洲时时彩" href="/credit/index.do?code=FFC2"><span><i></i>澳洲时时彩</span></a></li><li><a data-key="EFC" data-val="二分时时彩" href="/credit/index.do?code=EFC"><span><i></i>二分时时彩</span></a></li><li><a data-key="WFC" data-val="五分彩" href="/credit/index.do?code=WFC"><span><i></i>五分彩</span></a></li><li><a data-key="AZXYW168" data-val="澳洲幸运5" href="/credit/index.do?code=AZXYW168"><span><i></i>澳洲幸运5</span></a></li><li><a data-key="SFC" data-val="幸运时时彩" href="/credit/index.do?code=SFC"><span><i></i>幸运时时彩</span></a></li>

                </ul>
            </div>
            <div class="sidem_item sidem_item_3">
                <a href="javascript:;" class="sidem_b clear"><i></i><span>六合彩</span></a>
                <ul style="display:none">
<li><a data-key="LHC" data-val="香港六合彩" href="/credit/index.do?code=LHC"><span><i></i>香港六合彩</span></a></li><li><a data-key="AMLHC3" data-val="新澳门六合彩" href="/credit/index.do?code=AMLHC3"><span><i></i>新澳门六合彩</span></a></li><li><a data-key="AMLHC2" data-val="澳门六合彩" href="/credit/index.do?code=AMLHC2"><span><i></i>澳门六合彩</span></a></li><li><a data-key="KLLHC" data-val="快乐8六合彩" href="/credit/index.do?code=KLLHC"><span><i></i>快乐8六合彩</span></a></li>

                </ul>
            </div>
            <div class="sidem_item sidem_item_4">
                <a href="javascript:;" class="sidem_b clear"><i></i><span>时时彩</span></a>
                <ul style="display:none">
<li><a data-key="XJSSC" data-val="新疆时时彩" href="/credit/index.do?code=XJSSC"><span><i></i>新疆时时彩</span></a></li><li><a data-key="TJSSC" data-val="天津时时彩" href="/credit/index.do?code=TJSSC"><span><i></i>天津时时彩</span></a></li><li><a data-key="CQSSC" data-val="重庆时时彩" href="/credit/index.do?code=CQSSC"><span><i></i>重庆时时彩</span></a></li>

                </ul>
            </div>
            <div class="sidem_item sidem_item_5">
                <a href="javascript:;" class="sidem_b clear"><i></i><span>快乐彩</span></a>
                <ul style="display:none">
<li><a data-key="HNKLSF" data-val="湖南快十" href="/credit/index.do?code=HNKLSF"><span><i></i>湖南快十</span></a></li><li><a data-key="GDKLSF" data-val="广东快十" href="/credit/index.do?code=GDKLSF"><span><i></i>广东快十</span></a></li><li><a data-key="KLTF" data-val="快乐十分" href="/credit/index.do?code=KLTF"><span><i></i>快乐十分</span></a></li><li><a data-key="CQXYNC" data-val="幸运农场" href="/credit/index.do?code=CQXYNC"><span><i></i>幸运农场</span></a></li><li><a data-key="AZKL" data-val="澳洲幸运农场" href="/credit/index.do?code=AZKL"><span><i></i>澳洲幸运农场</span></a></li>

                </ul>
            </div>
            <div class="sidem_item sidem_item_6">
                <a href="javascript:;" class="sidem_b clear"><i></i><span>极速六合彩</span></a>
                <ul style="display:none">
<li><a data-key="FFLHC" data-val="极速六合彩" href="/credit/index.do?code=FFLHC"><span><i></i>极速六合彩</span></a></li><li><a data-key="F1LHC2" data-val="大发六合彩" href="/credit/index.do?code=F1LHC2"><span><i></i>大发六合彩</span></a></li><li><a data-key="F1LHC" data-val="澳洲六合彩" href="/credit/index.do?code=F1LHC"><span><i></i>澳洲六合彩</span></a></li><li><a data-key="WFLHC" data-val="5分六合彩" href="/credit/index.do?code=WFLHC"><span><i></i>5分六合彩</span></a></li><li><a data-key="SLHC" data-val="幸运六合彩" href="/credit/index.do?code=SLHC"><span><i></i>幸运六合彩</span></a></li>

                </ul>
            </div>
            <div class="sidem_item sidem_item_7">
                <a href="javascript:;" class="sidem_b clear"><i></i><span>PC蛋蛋</span></a>
                <ul style="display:none">
<li><a data-key="QW28" data-val="极速28" href="/credit/index.do?code=QW28"><span><i></i>极速28</span></a></li><li><a data-key="FF28" data-val="大发28" href="/credit/index.do?code=FF28"><span><i></i>大发28</span></a></li><li><a data-key="AZ28" data-val="幸运28" href="/credit/index.do?code=AZ28"><span><i></i>幸运28</span></a></li><li><a data-key="PCEGG" data-val="PC蛋蛋" href="/credit/index.do?code=PCEGG"><span><i></i>PC蛋蛋</span></a></li><li><a data-key="JND28" data-val="加拿大28" href="/credit/index.do?code=JND28"><span><i></i>加拿大28</span></a></li><li><a data-key="TWL28" data-val="台湾宾果28" href="/credit/index.do?code=TWL28"><span><i></i>台湾宾果28</span></a></li>

                </ul>
            </div>
            <div class="sidem_item sidem_item_8">
                <a href="javascript:;" class="sidem_b clear"><i></i><span>快三</span></a>
                <ul style="display:none">
<li><a data-key="QWK3" data-val="极速快三" href="/credit/index.do?code=QWK3"><span><i></i>极速快三</span></a></li><li><a data-key="FFK3" data-val="大发快三" href="/credit/index.do?code=FFK3"><span><i></i>大发快三</span></a></li><li><a data-key="SFK31" data-val="三分快三" href="/credit/index.do?code=SFK31"><span><i></i>三分快三</span></a></li><li><a data-key="JSK3" data-val="江苏快三" href="/credit/index.do?code=JSK3"><span><i></i>江苏快三</span></a></li><li><a data-key="HUBK3" data-val="湖北快三" href="/credit/index.do?code=HUBK3"><span><i></i>湖北快三</span></a></li><li><a data-key="HEBK3" data-val="河北快三" href="/credit/index.do?code=HEBK3"><span><i></i>河北快三</span></a></li>

                </ul>
            </div>
            <div class="sidem_item sidem_item_9">
                <a href="javascript:;" class="sidem_b clear"><i></i><span>11选5</span></a>
                <ul style="display:none">
<li><a data-key="QW11X5" data-val="极速11选5" href="/credit/index.do?code=QW11X5"><span><i></i>极速11选5</span></a></li><li><a data-key="GD11X5" data-val="广东11选5" href="/credit/index.do?code=GD11X5"><span><i></i>广东11选5</span></a></li><li><a data-key="SH11X5" data-val="上海11选5" href="/credit/index.do?code=SH11X5"><span><i></i>上海11选5</span></a></li>

                </ul>
            </div>
            <div class="sidem_item sidem_item_10">
                <a href="javascript:;" class="sidem_b clear"><i></i><span>3D彩</span></a>
                <ul style="display:none">
<li><a data-key="QW3D" data-val="极速3D" href="/credit/index.do?code=QW3D"><span><i></i>极速3D</span></a></li><li><a data-key="JS3D" data-val="大发3D" href="/credit/index.do?code=JS3D"><span><i></i>大发3D</span></a></li><li><a data-key="FC3D" data-val="福彩3D" href="/credit/index.do?code=FC3D"><span><i></i>福彩3D</span></a></li><li><a data-key="PL3" data-val="排列三" href="/credit/index.do?code=PL3"><span><i></i>排列三</span></a></li>

                </ul>
            </div>
        </div>
    </div>
</div>
	<div class="ui-main clear" id="UIMain">
<div class="ui-tab-ball-box change-play-nav clear">
    <ul class="fleft w100">
        <li class="fleft">
            <a href="javascript:;">选择彩种</a>
        </li>
        <li class="fleft on">
            <a href="javascript:;">赛车飞艇</a>
            <div class="change-play-menu">
            		<p>信用玩法</p>
                <div class="change-play-menu-xy">
                		<a href="/credit/index.do?code=FKSC"class="aon">极速赛车</a><a href="/credit/index.do?code=FKFT">极速飞艇</a><a href="/credit/index.do?code=FFSC">大发赛车</a><a href="/credit/index.do?code=AZFT">澳洲飞艇</a><a href="/credit/index.do?code=AZXYT168">澳洲幸运10</a><a href="/credit/index.do?code=BJSC">北京赛车</a><a href="/credit/index.do?code=XYFT">幸运飞艇</a><a href="/credit/index.do?code=XYFT2">168幸运飞艇</a><a href="/credit/index.do?code=JSSC168">168极速赛车</a><a href="/credit/index.do?code=SFSC">幸运赛车</a><a href="/credit/index.do?code=YLSM">香港跑马</a>
                </div>
                <p>官方玩法</p>
                <div class="change-play-menu-gf">
                		<a href="/offcial/index.do?code=FKSC">极速赛车</a><a href="/offcial/index.do?code=FKFT">极速飞艇</a><a href="/offcial/index.do?code=FFSC">大发赛车</a><a href="/offcial/index.do?code=AZFT">澳洲飞艇</a><a href="/offcial/index.do?code=AZXYT168">澳洲幸运10</a><a href="/offcial/index.do?code=BJSC">北京赛车</a><a href="/offcial/index.do?code=XYFT">幸运飞艇</a><a href="/offcial/index.do?code=XYFT2">168幸运飞艇</a><a href="/offcial/index.do?code=JSSC168">168极速赛车</a><a href="/offcial/index.do?code=SFSC">幸运赛车</a><a href="/offcial/index.do?code=YLSM">香港跑马</a>
                </div>
            </div>
        </li><li class="fleft">
            <a href="javascript:;">分分彩</a>
            <div class="change-play-menu">
            		<p>信用玩法</p>
                <div class="change-play-menu-xy">
                		<a href="/credit/index.do?code=AMFC">极速时时彩</a><a href="/credit/index.do?code=FFC">腾讯分分彩</a><a href="/credit/index.do?code=XGFC">大发时时彩</a><a href="/credit/index.do?code=FFC2">澳洲时时彩</a><a href="/credit/index.do?code=EFC">二分时时彩</a><a href="/credit/index.do?code=WFC">五分彩</a><a href="/credit/index.do?code=AZXYW168">澳洲幸运5</a><a href="/credit/index.do?code=SFC">幸运时时彩</a>
                </div>
                <p>官方玩法</p>
                <div class="change-play-menu-gf">
                		<a href="/offcial/index.do?code=AMFC">极速时时彩</a><a href="/offcial/index.do?code=FFC">腾讯分分彩</a><a href="/offcial/index.do?code=XGFC">大发时时彩</a><a href="/offcial/index.do?code=FFC2">澳洲时时彩</a><a href="/offcial/index.do?code=EFC">二分时时彩</a><a href="/offcial/index.do?code=WFC">五分彩</a><a href="/offcial/index.do?code=AZXYW168">澳洲幸运5</a><a href="/offcial/index.do?code=SFC">幸运时时彩</a>
                </div>
            </div>
        </li><li class="fleft">
            <a href="javascript:;">六合彩</a>
            <div class="change-play-menu">
            		<p>信用玩法</p>
                <div class="change-play-menu-xy">
                		<a href="/credit/index.do?code=LHC">香港六合彩</a><a href="/credit/index.do?code=AMLHC3">新澳门六合彩</a><a href="/credit/index.do?code=AMLHC2">澳门六合彩</a><a href="/credit/index.do?code=KLLHC">快乐8六合彩</a>
                </div>
                <p>官方玩法</p>
                <div class="change-play-menu-gf">
                		<a href="/offcial/index.do?code=LHC">香港六合彩</a><a href="/offcial/index.do?code=AMLHC3">新澳门六合彩</a><a href="/offcial/index.do?code=AMLHC2">澳门六合彩</a><a href="/offcial/index.do?code=KLLHC">快乐8六合彩</a>
                </div>
            </div>
        </li><li class="fleft">
            <a href="javascript:;">时时彩</a>
            <div class="change-play-menu">
            		<p>信用玩法</p>
                <div class="change-play-menu-xy">
                		<a href="/credit/index.do?code=XJSSC">新疆时时彩</a><a href="/credit/index.do?code=TJSSC">天津时时彩</a><a href="/credit/index.do?code=CQSSC">重庆时时彩</a>
                </div>
                <p>官方玩法</p>
                <div class="change-play-menu-gf">
                		<a href="/offcial/index.do?code=XJSSC">新疆时时彩</a><a href="/offcial/index.do?code=TJSSC">天津时时彩</a><a href="/offcial/index.do?code=CQSSC">重庆时时彩</a>
                </div>
            </div>
        </li><li class="fleft">
            <a href="javascript:;">快乐彩</a>
            <div class="change-play-menu">
            		<p>信用玩法</p>
                <div class="change-play-menu-xy">
                		<a href="/credit/index.do?code=HNKLSF">湖南快十</a><a href="/credit/index.do?code=GDKLSF">广东快十</a><a href="/credit/index.do?code=KLTF">快乐十分</a><a href="/credit/index.do?code=CQXYNC">幸运农场</a><a href="/credit/index.do?code=AZKL">澳洲幸运农场</a>
                </div>
                
            </div>
        </li><li class="fleft">
            <a href="javascript:;">极速六合彩</a>
            <div class="change-play-menu">
            		<p>信用玩法</p>
                <div class="change-play-menu-xy">
                		<a href="/credit/index.do?code=FFLHC">极速六合彩</a><a href="/credit/index.do?code=F1LHC2">大发六合彩</a><a href="/credit/index.do?code=F1LHC">澳洲六合彩</a><a href="/credit/index.do?code=WFLHC">5分六合彩</a><a href="/credit/index.do?code=SLHC">幸运六合彩</a>
                </div>
                <p>官方玩法</p>
                <div class="change-play-menu-gf">
                		<a href="/offcial/index.do?code=FFLHC">极速六合彩</a><a href="/offcial/index.do?code=F1LHC2">大发六合彩</a><a href="/offcial/index.do?code=F1LHC">澳洲六合彩</a><a href="/offcial/index.do?code=WFLHC">5分六合彩</a><a href="/offcial/index.do?code=SLHC">幸运六合彩</a>
                </div>
            </div>
        </li><li class="fleft">
            <a href="javascript:;">PC蛋蛋</a>
            <div class="change-play-menu">
            		<p>信用玩法</p>
                <div class="change-play-menu-xy">
                		<a href="/credit/index.do?code=QW28">极速28</a><a href="/credit/index.do?code=FF28">大发28</a><a href="/credit/index.do?code=AZ28">幸运28</a><a href="/credit/index.do?code=PCEGG">PC蛋蛋</a><a href="/credit/index.do?code=JND28">加拿大28</a><a href="/credit/index.do?code=TWL28">台湾宾果28</a>
                </div>
                <p>官方玩法</p>
                <div class="change-play-menu-gf">
                		<a href="/offcial/index.do?code=QW28">极速28</a><a href="/offcial/index.do?code=FF28">大发28</a><a href="/offcial/index.do?code=AZ28">幸运28</a><a href="/offcial/index.do?code=PCEGG">PC蛋蛋</a><a href="/offcial/index.do?code=JND28">加拿大28</a><a href="/offcial/index.do?code=TWL28">台湾宾果28</a>
                </div>
            </div>
        </li><li class="fleft">
            <a href="javascript:;">快三</a>
            <div class="change-play-menu">
            		<p>信用玩法</p>
                <div class="change-play-menu-xy">
                		<a href="/credit/index.do?code=QWK3">极速快三</a><a href="/credit/index.do?code=FFK3">大发快三</a><a href="/credit/index.do?code=SFK31">三分快三</a><a href="/credit/index.do?code=JSK3">江苏快三</a><a href="/credit/index.do?code=HUBK3">湖北快三</a><a href="/credit/index.do?code=HEBK3">河北快三</a>
                </div>
                <p>官方玩法</p>
                <div class="change-play-menu-gf">
                		<a href="/offcial/index.do?code=QWK3">极速快三</a><a href="/offcial/index.do?code=FFK3">大发快三</a><a href="/offcial/index.do?code=SFK31">三分快三</a><a href="/offcial/index.do?code=JSK3">江苏快三</a><a href="/offcial/index.do?code=HUBK3">湖北快三</a><a href="/offcial/index.do?code=HEBK3">河北快三</a>
                </div>
            </div>
        </li><li class="fleft">
            <a href="javascript:;">11选5</a>
            <div class="change-play-menu">
            		<p>信用玩法</p>
                <div class="change-play-menu-xy">
                		<a href="/credit/index.do?code=QW11X5">极速11选5</a><a href="/credit/index.do?code=GD11X5">广东11选5</a><a href="/credit/index.do?code=SH11X5">上海11选5</a>
                </div>
                <p>官方玩法</p>
                <div class="change-play-menu-gf">
                		<a href="/offcial/index.do?code=QW11X5">极速11选5</a><a href="/offcial/index.do?code=GD11X5">广东11选5</a><a href="/offcial/index.do?code=SH11X5">上海11选5</a>
                </div>
            </div>
        </li><li class="fleft">
            <a href="javascript:;">3D彩</a>
            <div class="change-play-menu">
            		<p>信用玩法</p>
                <div class="change-play-menu-xy">
                		<a href="/credit/index.do?code=QW3D">极速3D</a><a href="/credit/index.do?code=JS3D">大发3D</a><a href="/credit/index.do?code=FC3D">福彩3D</a><a href="/credit/index.do?code=PL3">排列三</a>
                </div>
                <p>官方玩法</p>
                <div class="change-play-menu-gf">
                		<a href="/offcial/index.do?code=QW3D">极速3D</a><a href="/offcial/index.do?code=JS3D">大发3D</a><a href="/offcial/index.do?code=FC3D">福彩3D</a><a href="/offcial/index.do?code=PL3">排列三</a>
                </div>
            </div>
        </li>
    </ul>
</div>
		<div class="ui-main-box fleft w100">


<div class="mainNav">
    <div class="mainNav-list">
        <!--ko foreach:navList-->
        <a data-bind="click:$parent.navClick,css:($parent.currentNavId()).code===code?'active':''">
            <i></i>
            <font data-bind="text:name"></font>
        </a>
        <!--/ko-->
    </div>
    <div class="gamePlay">
       	<a href="/offcial/index.do?code=FKSC" title="官方玩法"></a>
            <a href="/offcial/index.do?code=FKSC" title="官方玩法" class="page-ico"><img src="/common/lot/images/new-ico.gif" /></a>
    </div>
</div>
<div class="ui-left-box fleft" id="mainBox">
        <div class="ui-shortcut-box clear" style="overflow: visible;">
            <div class="fleft" style="height:35px;">
                                    <div class="buyBonus" id="SLIPPER" data-bind="visible: maxKickback() > 0">
                        <div class="BonusPercen" data-bind="text:utils.toFixed(kickback(),1)+'%'">8</div>
                        <a href="javascript:;" class="minus" data-bind="click:plus"></a>
                        <div class="ranger">
                            <div class="ui-widget">
                                <span class="ui-handle" style="left: 0;"></span>
                            </div>
                        </div>
                        <a href="javascript:;" class="plus" data-bind="click:minus"></a>
                    </div>
            </div>
            <div class="fright">
                <a href="javascript:;" title="一般" class="fleft ui-general-btn" id="generalClickBtnId" data-bind="click:$root.generalClick,css:{'yes':$root.selectType()===1,'dn':$root.zuxuan() === 1}">一般</a>
                <a href="javascript:;" title="快捷" class="fleft ui-short-btn"  id="fastQuickBtnId" data-bind="click:$root.fastQuick,css:{'yes':$root.selectType()===0,'dn':$root.zuxuan() === 1}">快捷</a>
                <a href="javascript:;" title="快选金额" class="fleft ui-fast-select-btn" data-bind="click:$root.customSelectAmountEvent">快选金额</a>
                
            </div>
        </div>
        <div id="game-box" class="clear ui-content-box pr game-pk10" data-bind="component:$root.fastRender"></div>
        
        <div class="waybill-wrap" data-bind="visible:!waybillHide()">
            <div class="clear waybill-tab waybill-tab-7">
                <dl data-bind="foreach:waybillSub2">
                    <dt data-bind="if:$index()===0,visible:$index()===0">
                        <select data-bind="options:$parent.waybillSub1,optionsText:'name',value:$parent.waybillSelect1"></select>
                    </dt>
                    <dd><a href="javascript:;" data-bind="text:name,click:$parent.waybillChecked.bind($data,$index()),css:$parent.waybillSelect2()===(type+$data.site)?'active':''">&nbsp;</a></dd>
                </dl>
            </div>
            <div class="waybill-main">
                <div class="w-item clear" id="WAYBILL-BODY"></div>
            </div>
        </div>
    </div><div class="ui-right-box fright pr">
    <div class="ui-shadow-box fleft">
        <a href="javascript:;" class="select out-btn" data-bind="click:omissionAndoutCode" data-id="out">出码排行</a>
        <a href="javascript:;" class="omi-btn" data-bind="click:omissionAndoutCode" data-id="omi">遗漏排行</a>
    </div>
    <div class="ui-out-omission-con-box">
        <div class="fl w100">
            <label class="db fl ui-min-tit-box tc" id="label_tab">次数：</label>
            <div class="db fl tc pr ui-omi-select-box">
                <label class="db ui-show-select-box" data-oid="showOmiOut" data-bind="click:selectOption,attr:{'data-id':$root.showOmiOut().partialName},text:$root.showOmiOut().name">--</label>
                <ul class="pa ui-slide-box" id="slide_con" style="display:none;" data-bind="foreach:omiAndOutList">
                    <li data-bind="click:$parent.dropDownLIClick.bind($parent,$data),attr:{'data-oid':partialName},text:name"></li>
                </ul>
            </div>
        </div>
        <ul class="ui-wer-box fl w100" style="color:red;" data-bind="css:(rankData().length === 0)?'':'dn'">
            <li class="tc" style="height:50px;line-height:50px;">暂无排名数据</li>
        </ul>
        <ul class="ui-wer-box fl w100" data-bind="foreach:rankData,css:(rankData().length === 0)?'dn':''">
            <li><label class="db ui-one-box" data-bind="html:name"></label><label class="db ui-two-box" data-bind="text:count+'期'"></label></li>
        </ul>
    </div>
    <div class="autoGen" data-bind="visible:$root.templateCode()=='gdjh' && $root.followList().length >0">
        <div class="title">自动跟单明细</div>
        <div class="list" data-bind="foreach:{data:$root.followList(),as:'fl'}">
            <div class="item">
                <div class="one" data-bind="click:$parent.append"><span data-bind="html:fl.predictorName"></span></div>
                <div class="two" data-bind="click:$parent.append"><span data-bind="html:fl.playName"></span><span data-bind="html:fl.ballNum"></span>码</div>
                <div class="three" data-bind="click:$parent.append"><span data-bind="html:fl.followNum"></span>期</div>
                <div class="four" data-bind="click:$parent.deleteOneFollow"><img src="/common/lot/images/del.png" alt=""></div>
            </div>
        </div>
        <div class="footer" data-bind="click:$root.deleteAllFollow">删除全部</div>
    </div>
</div>			<div id="stopSellingPop" class="stop_selling" style="display:none"><div class="stop_selling_title"></div></div>
	    </div>
	</div>
</div>
<script type="text/html" id="tpl_general">
    <form data-bind="submit: $root.buildOrders.bind($data,$root),selectAmount:$root.quickAmountArr">
        <div class="main_txt zm16_txt w900" style="max-height: 1000px;overflow: auto">
            <div class="txt_section clear_fix">
                <!--ko if:template.isTop-->
                <!--ko foreach:template.tmp-->
                <div data-bind="template:{name:$data,data:$parent.list()[$index()+1]}"></div>
                <!--/ko-->
                <!--/ko-->
                <div class="s1_nav clear_fix">
                    <!--ko if:list().length>0-->
                    <ul class="clear"
                        data-bind="foreach:{data:list()[0],as:'item'},css:$root.selectType()?'':'fast_slct'">
                        <li data-bind="css:$parent.template.comW">
                            <div class="zm16_nav" data-bind="css:item.title==''?'dn':'',text:item.title"></div>
                            <div class="clear ui-ball-span-box">
                                <div class="clear">
                                    <span class="border_001" data-bind="css:$parents[1].selectType()?'':'w49'">号码</span>
                                    <span class="border_002" data-bind="css:$parents[1].selectType()?'':'w49'">赔率</span>
                                    <span class="border_003"
                                          data-bind="css:$parents[1].selectType()?'':'dn'">金额</span><i></i>
                                </div>
                            </div>
                            <div data-bind="foreach:{data:item.list,as:'index'}">
                                <div class="bd clear" data-bind="click:$root.getFastItem">
                                    <span class="ball"
                                          data-bind="html:N, css:(/^[0-9]+$/.test(N))?'ball_com ball_'+N:(/[^\'\']/.test(N)?'':'dn'),style:{width:$root.selectType()?'':'50%'},attr:{'title':(/:+/.test($data.kname))?$data.kname:$data.kname+'：'+N}"></span>
                                    <span class="odds"
                                          data-bind="text:$root.status()?index['odd']():index['odd'](), css:/[^\'\']/.test(N) ? ($root.isFP(odd()) ? 'odds-close-text' : ($root.selectType() ? '' : 'w49')) : 'dn'"></span>
                                    <span class="amount_pane"
                                          data-bind="css:$root.selectType()?(/[^\'\']/.test(N)?'':'dn'):'dn'">
                                        <input type="text" class="amount"
                                               data-bind="textinput:amount, enable:$root.status, attr:{'oid':$root.status}"
                                               maxlength="9"/>
                                    </span>
                                </div>
                            </div>
                        </li>
                    </ul>
                    <!--/ko-->
                </div>
                <!--ko if:!template.isTop-->
                <!--ko foreach:template.tmp-->
                <div data-bind="template:{name:$data,data:$parent.list()[$index()+1]}"></div>
                <!--/ko-->
                <!--/ko-->
            </div>
            <div class="btn-wrap">
                <input type="text" class="amount"
                       data-bind="enable:$root.status,css:$root.zuxuan()===1?'':$root.selectType()?'dn':'',value:$root.fastAmount,textInput:$root.fastAmount"
                       maxlength="9">
                <input type="submit" class="btn btn-green" value="" title="确认"/>
                <input type="reset" class="btn" value="" title="取消" data-bind="click:$root.reset.bind($data,$root)">
            </div>
        </div>
    </form>
</script>

<script type="text/html" id="tpl_longhu">
    <form data-bind="submit: $root.buildOrders.bind($data,$root), selectAmount:$root.quickAmountArr">
        <!--ko if:list().length>0-->
        <div class="toradora toradora_bar clear"
             data-bind="foreach:{data:list()[0],as:'longhu'},attr:{'oid':list()[0].length},css:($root.selectType())?'':'fast_slct'">
            <ul class="clear_fix tc fl"
                data-bind="css:$data.offset+'-'+ $index(),foreach:{data:longhu.data,as:'items'}">
                <li data-bind="css:$parent.wid">
                    <div class="t-head"><b data-bind="text:$root.setPosName(items.N)"></b><label
                                data-bind="text:items.N"></label></div>
                    <div class="t-box" data-bind="foreach:{data:items.list,as:'v'}">
                        <div class="t-item" data-bind="click:$root.getFastItem,clickBubble:true,css:$parents[1].pre">
                            <div class="t-name" data-bind="css:(/[^\'\']/.test(N)?'':'dn'">
                                <span data-bind="text:v['N'],css:!$index()?'txt-red':($index()===Number(items.list.length-1))?'txt-blue':'txt-white',attr:{'style':($root.selectType())?'':'display:block;','oid':items.list.length}"></span>
                                <span class="t-odds"
                                      data-bind="text:$root.status()?v['odd']():v['odd'](),css:(($root.selectType())?'':'w100 db')+($root.status()?'':' odds-close-text')"></span>
                            </div>
                            <div class="t-input tc" data-bind="css:$root.selectType()?(/[^\'\']/.test(N)?'':'dn'):'dn'">
                                <input type="text" autocomplete="off" data-bind="enable:$root.status,textInput:amount">
                            </div>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
        <!--/ko-->
        <div class="toradora_btn w100">
            <input type="text" class="amount"
                   data-bind="enable:$root.status,css:$root.selectType()?'dn':'',textInput:$root.fastAmount"
                   maxlength="9">
            <input type="submit" class="btn btn-green" value="" title="确认">
            <input type="reset" class="btn" value="" title="取消" data-bind="click:$root.reset.bind($data,$root)">
        </div>
    </form>
</script>

<script type="text/html" id="tpl_tb">
    <form data-bind="submit: $root.buildOrders.bind($data,$root), selectAmount:$root.quickAmountArr">
        <div class="clear ui-ball-tb-box ui-ball-span-box" data-bind="foreach:[0,1,2,3,4]">
            <div class="clear">
                <span class="border_001">号码</span>
                <span class="border_002">赔率</span>
                <span class="border_003">金额</span><i></i>
            </div>
        </div>
        <div class="ui-tb-con-box w100 clear_fix">
            <!--ko if:list().length>0-->
            <ul class="clear" data-bind="foreach:{data:list()[0],as:'item'},css:$root.selectType()?'':'fast_slct'">
                <li>
                    <!--ko if:item.list.length>0-->
                    <div class="ui-tit-tr-box" data-bind="attr:{'oid':item.title},text:item.title"></div>
                    <!--/ko-->
                    <div class="fl w100" data-bind="foreach:{data:item.list,as:'index'}">
                        <!--ko if:index.N-->
                        <div class="ui-ttr-box">
                            <div class="bd clear fl" data-bind="click:$root.getFastItem">
                                <span class="ball tc" data-bind="style:{width:$root.selectType()?'':'47%'}">
                                    <span class="w100 db tb-style"
                                          data-bind="css:index.showImg?'dn':'',text:index.N, attr:{'title':index.kname+'：'+index.N}"></span>
                                    <label class="db w100"
                                           data-bind="css:index.showImg?'':'dn',attr:{'title':index.kname+'：'+index.N}">
                                        <img data-bind="attr:{src:/[1-6]/.test((index.N.split(''))[0])?'/common/lot/images/k3/'+ (index.N.split(''))[0] +'.png':''}"
                                             width="19" height="19"/>
                                        <img data-bind="css:index.oneBit?'dn':'',attr:{src:/[1-6]/.test((index.N.split(''))[1])?'/common/lot/images/k3/'+ (index.N.split(''))[1] +'.png':''}"
                                             width="19" height="19"/>
                                    </label>
                                </span>
                                <span class="odds"
                                      data-bind="text:$root.status()?index['odd']():index['odd'](),css:/[^\'\']/.test(N) ? ($root.isFP(odd()) ? 'odds-close-text' : ($root.selectType() ? '' : 'w49')) : 'dn'"></span>
                                <span class="amount_pane"
                                      data-bind="css:$root.selectType()?(/[^\'\']/.test(index.N)?'':'dn'):'dn',">
                                    <input type="text" class="amount" autocomplete="off" maxlength="9"
                                           data-bind="textInput:index.amount;,enable:$root.status"/>
                                </span>
                            </div>
                        </div>
                        <!--/ko-->
                    </div>
                </li>
            </ul>
            <!--/ko-->
        </div>
        <div class="btn-wrap">
            <input type="text" class="amount"
                   data-bind="enable:$root.status,css:$root.selectType()?'dn':'',textInput:$root.fastAmount"
                   maxlength="9">
            <input type="submit" class="btn btn-green" value="" title="确认">
            <input type="reset" class="btn" value="" title="取消" data-bind="click:$root.reset.bind($data,$root)">
        </div>
    </form>
</script>


<script type="text/html" id="cbc_tb">
    <form data-bind="submit: $root.buildOrders.bind($data,$root), selectAmount:$root.quickAmountArr">
        <div class="ui-tb-con-box w100 clear_fix">
            <!--ko if:list().length>0-->
            <ul class="clear" data-bind="foreach:{data:list()[0],as:'item'},css:$root.selectType()?'':'fast_slct'">
                <li>
                    <!--ko if:item.list.length>0-->
                    <div class="ui-tit-tr-box" data-bind="attr:{'oid':item.title},text:item.title"></div>
                    <div class="clear ui-ball-tb-box-new ui-ball-span-box" data-bind="foreach:[0,1,2]">
                        <div class="clear">
                            <span class="border_001">号码</span>
                            <span class="border_002">赔率</span>
                            <span class="border_003">金额</span><i></i>
                        </div>
                    </div>

                    <!--/ko-->
                    <div class="fl w100" data-bind="foreach:{data:item.list,as:'index'}">
                        <!--ko if:index.N-->
                        <div class="ui-ttr-box-new">
                            <div class="bd clear fl" data-bind="click:$root.getFastItem">
                                <span class="ball tc" data-bind="style:{width:$root.selectType()?'':'47%'}">
                                    <span class="w100 db tb-style"
                                          data-bind="css:index.showImg?'dn':'',text:index.N, attr:{'title':index.kname+'：'+index.N}"></span>
                                    <label class="db w100"
                                           data-bind="css:index.showImg?'':'dn',attr:{'title':index.kname+'：'+index.N}">
                                        <img data-bind="attr:{src:/[1-6]/.test((index.N.split(''))[0])?'/common/lot/images/k3/'+ (index.N.split(''))[0] +'.png':''}"
                                             width="19" height="19"/>
                                        <img data-bind="css:index.oneBit?'dn':'',attr:{src:/[1-6]/.test((index.N.split(''))[1])?'/common/lot/images/k3/'+ (index.N.split(''))[1] +'.png':''}"
                                             width="19" height="19"/>
                                        <img data-bind="css:index.twoBit?'dn':'',attr:{src:/[1-6]/.test((index.N.split(''))[2])?'/common/lot/images/k3/'+ (index.N.split(''))[2] +'.png':''}"
                                             width="19" height="19"/>
                                    </label>
                                </span>
                                <span class="odds" title="赔率"
                                      data-bind="text:$root.status()?index['odd']():index['odd'](),css:/[^\'\']/.test(N) ? ($root.isFP(odd()) ? 'odds-close-text' : ($root.selectType() ? '' : 'w49')) : 'dn'"></span>
                                <span class="amount_pane"
                                      data-bind="css:$root.selectType()?(/[^\'\']/.test(index.N)?'':'dn'):'dn',">
                                    <input type="text" class="amount" autocomplete="off" maxlength="9"
                                           data-bind="textInput:index.amount;,enable:$root.status"/>
                                </span>
                            </div>
                        </div>
                        <!--/ko-->
                    </div>
                </li>
            </ul>
            <!--/ko-->
        </div>
        <div class="btn-wrap">
            <input type="text" class="amount"
                   data-bind="enable:$root.status,css:$root.selectType()?'dn':'',textInput:$root.fastAmount"
                   maxlength="9">
            <input type="submit" class="btn btn-green" value="" title="确认">
            <input type="reset" class="btn" value="" title="取消" data-bind="click:$root.reset.bind($data,$root)">
        </div>
    </form>
</script>


<script type="text/html" id="qhzZhTemp">
    <div>
        <!--ko  foreach:$data-->
        <div class="zm16_nav nav-tab fl" data-oid="1"
             data-bind="text: title,css:$index()==0?'active':'',attr:{'data-oid':$index()},click:$root.qzhClick">前三
        </div>
        <!--/ko-->
    </div>
    <div id="qzhList">
        <!--ko  foreach:$data-->
        <div data-bind="attr:{'vid':$index},visible: $index() == 0">
            <ul data-bind="css:$root.selectType()?'':'fast_slct'">
                <!--ko foreach: list-->
                <li>
                    <div class="clear ui-ball-span-box">
                        <div class="clear">
                            <span class="border_001" data-bind="css:$root.selectType()?'':'w49'">号码</span>
                            <span class="border_002" data-bind="css:$root.selectType()?'':'w49'">赔率</span>
                            <span class="border_003" data-bind="css:$root.selectType()?'':'dn'">金额</span><i></i>
                        </div>
                    </div>
                    <div>
                        <div>
                            <div class="bd clear" data-bind="click:$root.getFastItem">
                                <span class="ball"
                                      data-bind="text:N,css:(/^\d|\d\d[^-]$/.test(N))?'ball_com ball_'+N:(/[^\'\']/.test(N)?'':'dn'),style:{width:$root.selectType()?'':'50%'},attr:{'title':$data.kname+'：'+N}"></span>
                                <span class="odds"
                                      data-bind="text:$root.status()?odd():odd(),css:/[^\'\']/.test(N) ? ($root.isFP(odd()) ? 'odds-close-text' : ($root.selectType() ? '' : 'w49')) : 'dn'"></span>
                                <span class="amount_pane"
                                      data-bind="css:$root.selectType()?(/[^\'\']/.test(N)?'':'dn'):'dn'">
                                    <input type="text" class="amount" maxlength="9"
                                           data-bind="enable:$root.status,textInput:amount"/>
                                </span>
                            </div>
                        </div>
                    </div>
                </li>
                <!--/ko-->
            </ul>
        </div>
        <!--/ko-->
    </div>
    <div style="clear:both;"></div>
</script>


<script type="text/html" id="dpcEzdwTemp">
    <div>
        <!--ko  foreach:$data-->
        <div class="zm16_nav nav-tab fl" data-oid="1"
             data-bind="text: title,css:$index()==0?'active':'',attr:{'data-oid':$index()},click:$root.qzhClick">前三
        </div>
        <!--/ko-->
    </div>
    <div id="qzhList">
        <!--ko  foreach:$data-->
        <div data-bind="attr:{'vid':$index},visible: $index() == 0">
            <ul data-bind="css:$root.selectType()?'':'fast_slct'">
                <!--ko foreach: list-->
                <li>
                    <!--ko if:$index() < 5 -->
                    <div class="clear ui-ball-span-box">
                        <div class="clear">
                            <span class="border_001" data-bind="css:$root.selectType()?'':'w49'">号码</span>
                            <span class="border_002" data-bind="css:$root.selectType()?'':'w49'">赔率</span>
                            <span class="border_003" data-bind="css:$root.selectType()?'':'dn'">金额</span><i></i>
                        </div>
                    </div>
                    <!--/ko-->
                    <div>
                        <div>
                            <div class="bd clear" data-bind="click:$root.getFastItem">
                                <span class="ball"
                                      data-bind="text:N,css:(/^\d|\d\d[^-]$/.test(N))?'ball_com ball_'+N:(/[^\'\']/.test(N)?'':'dn'),style:{width:$root.selectType()?'':'50%'},attr:{'title':$data.kname+'：'+N}"></span>
                                <span class="odds"
                                      data-bind="text:$root.status()?odd():odd(),css:/[^\'\']/.test(N) ? ($root.isFP(odd()) ? 'odds-close-text' : ($root.selectType() ? '' : 'w49')) : 'dn'"></span>
                                <span class="amount_pane"
                                      data-bind="css:$root.selectType()?(/[^\'\']/.test(N)?'':'dn'):'dn'">
                                    <input type="text" class="amount" maxlength="9"
                                           data-bind="enable:$root.status,textInput:amount"/>
                                </span>
                            </div>
                        </div>
                    </div>
                </li>
                <!--/ko-->
            </ul>
        </div>
        <!--/ko-->
    </div>
    <div style="clear:both;"></div>
</script>

<script type="text/html" id="dpcEzhsTemp">
    <div>
        <!--ko foreach:$data-->
        <!--ko if:navW != 'w399'-->
        <div class="zm16_nav nav-tab fl" data-oid="1"
             data-bind="text: title,css:$index()==0?'active ' + navW:''+navW,attr:{'data-oid':$index()},click:$root.qzhClick">
            前三
        </div>
        <!--/ko-->
        <!--/ko-->
    </div>
    <div id="qzhList">
        <!--ko  foreach:$data-->
        <div data-bind="attr:{'vid':$index},visible: $index() == 0">
            <ul data-bind="css:$root.selectType()?'':'fast_slct'" style="height: 173px;">
                <!--ko foreach: list-->
                <!--ko if:I.indexOf('ezhs') >=0 || I.indexOf('szhs') >= 0 -->
                <li>
                    <!--ko if:$index() < 5 -->
                    <div class="clear ui-ball-span-box">
                        <div class="clear">
                            <span class="border_001" data-bind="css:$root.selectType()?'':'w49'">号码</span>
                            <span class="border_002" data-bind="css:$root.selectType()?'':'w49'">赔率</span>
                            <span class="border_003" data-bind="css:$root.selectType()?'':'dn'">金额</span><i></i>
                        </div>
                    </div>
                    <!--/ko-->
                    <div>
                        <div>
                            <div class="bd clear" data-bind="click:$root.getFastItem">
                                <span class="ball"
                                      data-bind="text:N,style:{width:$root.selectType()?'':'50%'},attr:{'title':$data.kname+'：'+N}"></span>
                                <span class="odds"
                                      data-bind="text:$root.status()?odd():odd(),css:/[^\'\']/.test(N) ? ($root.isFP(odd()) ? 'odds-close-text' : ($root.selectType() ? '' : 'w49')) : 'dn'"></span>
                                <span class="amount_pane"
                                      data-bind="css:$root.selectType()?(/[^\'\']/.test(N)?'':'dn'):'dn'">
                                    <input type="text" class="amount" maxlength="9"
                                           data-bind="enable:$root.status,textInput:amount"/>
                                </span>
                            </div>
                        </div>
                    </div>
                </li>
                <!--/ko-->
                <!--/ko-->
            </ul>
            <ul data-bind="css:$root.selectType()?'':'fast_slct',visible: navW == 'w299'">
                    <li>
                        <div class="clear ui-ball-span-box">
                            <div class="clear">
                                <span class="border_001" data-bind="css:$root.selectType()?'':'w49'">号码</span>
                                <span class="border_002" data-bind="css:$root.selectType()?'':'w49'">赔率</span>
                                <span class="border_003" data-bind="css:$root.selectType()?'':'dn'">金额</span><i></i>
                            </div>
                        </div>
                    </li>
                    <li>
                        <div class="clear ui-ball-span-box">
                            <div class="clear">
                                <span class="border_001" data-bind="css:$root.selectType()?'':'w49'">号码</span>
                                <span class="border_002" data-bind="css:$root.selectType()?'':'w49'">赔率</span>
                                <span class="border_003" data-bind="css:$root.selectType()?'':'dn'">金额</span><i></i>
                            </div>
                        </div>
                    </li>
                    <li>
                        <div class="clear ui-ball-span-box">
                            <div class="clear">
                                <span class="border_001" data-bind="css:$root.selectType()?'':'w49'">号码</span>
                                <span class="border_002" data-bind="css:$root.selectType()?'':'w49'">赔率</span>
                                <span class="border_003" data-bind="css:$root.selectType()?'':'dn'">金额</span><i></i>
                            </div>
                        </div>
                    </li>
                    <li>
                        <div class="clear ui-ball-span-box">
                            <div class="clear">
                                <span class="border_001" data-bind="css:$root.selectType()?'':'w49'">号码</span>
                                <span class="border_002" data-bind="css:$root.selectType()?'':'w49'">赔率</span>
                                <span class="border_003" data-bind="css:$root.selectType()?'':'dn'">金额</span><i></i>
                            </div>
                        </div>
                    </li>
                    <li>
                        <div class="clear ui-ball-span-box">
                            <div class="clear">
                                <span class="border_001" data-bind="css:$root.selectType()?'':'w49'">号码</span>
                                <span class="border_002" data-bind="css:$root.selectType()?'':'w49'">赔率</span>
                                <span class="border_003" data-bind="css:$root.selectType()?'':'dn'">金额</span><i></i>
                            </div>
                        </div>
                    </li>
                <!--ko foreach: list-->
                <!--ko if:I.indexOf('ezhws') >=0 -->
                <li>
                    <div>
                        <div>
                            <div class="bd clear" data-bind="click:$root.getFastItem">
                                <span class="ball"
                                      data-bind="text:N,css:(/^\d|\d\d[^-]$/.test(N))?'ball_com ball_'+N:(/[^\'\']/.test(N)?'':'dn'),style:{width:$root.selectType()?'':'50%'},attr:{'title':$data.kname+'：'+N}"></span>
                                <span class="odds"
                                      data-bind="text:$root.status()?odd():odd(),css:/[^\'\']/.test(N) ? ($root.isFP(odd()) ? 'odds-close-text' : ($root.selectType() ? '' : 'w49')) : 'dn'"></span>
                                <span class="amount_pane"
                                      data-bind="css:$root.selectType()?(/[^\'\']/.test(N)?'':'dn'):'dn'">
                                    <input type="text" class="amount" maxlength="9"
                                           data-bind="enable:$root.status,textInput:amount"/>
                                </span>
                            </div>
                        </div>
                    </div>
                </li>
                <!--/ko-->
                <!--/ko-->
            </ul>

        </div>
        <!--/ko-->
    </div>
    <div style="clear:both;"></div>
</script>

<script type="text/html" id="dpcZuxuanTemp">
    <div class="navTabZx">
        <!--ko  foreach:$data-->
        <div class="a zm16_nav nav-tab fl" data-oid="1"
             data-bind="text: title,css:$index()==0?'active ' + navW:''+ navW,attr:{'data-oid':$index()},click:$root.qzhClick">
            前三
        </div>
        <!--/ko-->
    </div>
    <div id="qzhList">
        <!--ko  foreach:$data-->
        <div data-bind="attr:{'vid':$index},visible: $index() == 0">
            <ul>
                <!--ko foreach: list-->
                <li>
                    <!--ko if:$index() < 5 -->
                    <div class="clear ui-ball-span-box">
                        <div class="clear">
                            <span class="border_001">号码</span>
                            <span class="border_002">赔率</span>
                            <span class="border_003">勾选</span><i></i>
                        </div>
                    </div>
                    <!--/ko-->
                    <div>
                        <div>
                            <div class="bd clear" data-bind="click:$root.getZuxuanItem">
                                <span class="ball"
                                      data-bind="text:N,css:(/^\d|\d\d[^-]$/.test(N))?'ball_com ball_'+N:(/[^\'\']/.test(N)?'':'dn'),,attr:{'title':$data.kname+'：'+N}"></span>
                                <span class="odds"
                                      data-bind="text:$root.status()?odd():odd(),css:/[^\'\']/.test(N) ? ($root.isFP(odd()) ? 'odds-close-text' : 'dn'"></span>
                                <span class="amount_pane">
                                    <input type="checkbox" style="width:auto;margin-top:11px;"
                                           data-bind="click:$root.zuxuanCheckboxItem,clickBubble:false,value:$data,attr:{id:'ck-'+$index()}"/>
                                </span>
                            </div>
                        </div>
                    </div>
                </li>
                <!--/ko-->
            </ul>
        </div>
        <!--/ko-->
    </div>
    <div style="clear:both;"></div>
</script>

<script type="text/html" id="qhzTemp">
    <div data-bind="foreach:{data:$data,as:'item'}">
        <div>
            <div class="ui-tit-tr-box fl w100" data-bind="html:item.title,attr:{'oid':item.title}"></div>
            <div>
                <ul data-bind="foreach:list,attr:{'oid':list.length},css:$root.selectType()?'':'fast_slct'">
                    <li>
                        <div class="clear ui-ball-span-box">
                            <div class="clear">
                                <span class="border_001" data-bind="css:$root.selectType()?'':'w49'">号码</span>
                                <span class="border_002" data-bind="css:$root.selectType()?'':'w49'">赔率</span>
                                <span class="border_003" data-bind="css:$root.selectType()?'':'dn'">金额</span><i></i>
                            </div>
                        </div>
                        <div>
                            <div>
                                <div class="bd clear" data-bind="click:$root.getFastItem">
                                    <span class="ball"
                                          data-bind="text:N,css:(/^\d|\d\d[^-]$/.test(N))?'ball_com ball_'+N:(/[^\'\']/.test(N)?'':'dn'),style:{width:$root.selectType()?'':'50%'},attr:{'title':$data.kname+'：'+N}"></span>
                                    <span class="odds"
                                          data-bind="text:$root.status()?odd():odd(),css:/[^\'\']/.test(N) ? ($root.isFP(odd()) ? 'odds-close-text' : ($root.selectType() ? '' : 'w49')) : 'dn'"></span>
                                    <span class="amount_pane"
                                          data-bind="css:$parents[4].selectType()?(/[^\'\']/.test(N)?'':'dn'):'dn'">
                                        <input type="text" class="amount" maxlength="9"
                                               data-bind="enable:$root.status,textInput:amount"/>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </div>
    <div style="clear:both;"></div>
</script>


<script type="text/html" id="hozTemp">
    <div data-bind="foreach:$data">
        <div class="zm16_nav" data-bind="text:'冠，亚军和',visible:(list.length>0)"></div>
        <ul data-bind="foreach:list,attr:{'oid':list.length},css:$root.selectType()?'':'fast_slct'">
            <li class="w225px">
                <div class="clear ui-ball-span-box">
                    <div class="clear">
                        <span class="border_001" data-bind="css:$root.selectType()?'':'w49'">号码</span>
                        <span class="border_002" data-bind="css:$root.selectType()?'':'w49'">赔率</span>
                        <span class="border_003" data-bind="css:$root.selectType()?'':'dn'">金额</span><i></i>
                    </div>
                </div>
                <div>
                    <div>
                        <div class="bd clear" data-bind="click:$root.getFastItem">
                            <span class="ball"
                                  data-bind="text:N,css:(/^\d|\d\d[^-]$/.test(N))?'ball_com ball_'+N:(/[^\'\']/.test(N)?'':'dn'),style:{width:$root.selectType()?'':'50%'},attr:{'title':$data.kname+'：'+N}"></span>
                            <span class="odds"
                                  data-bind="text:$root.status()?odd():odd(),css:/[^\'\']/.test(N) ? ($root.isFP(odd()) ? 'odds-close-text' : ($root.selectType() ? '' : 'w49')) : 'dn'"></span>
                            <span class="amount_pane"
                                  data-bind="css:$root.selectType()?(/[^\'\']/.test(N)?'':'dn'):'dn'"><input type="text"
                                                                                                             class="amount"
                                                                                                             maxlength="9"
                                                                                                             data-bind="enable:$root.status,textInput:amount"/></span>
                        </div>
                    </div>
                </div>
            </li>
        </ul>
    </div>
</script>

<script type="text/html" id="dpcTopTemp">
    <div data-bind="foreach:$data">
        <div class="zm16_nav" data-bind="text:'三字和数',visible:(list.length>0)"></div>
        <ul data-bind="foreach:list,attr:{'oid':list.length},css:$root.selectType()?'':'fast_slct'">
            <li class="w225px">
                <!--ko if:$index() < 4 -->
                <div class="clear ui-ball-span-box">
                    <div class="clear">
                        <span class="border_001" data-bind="css:$root.selectType()?'':'w49'">号码</span>
                        <span class="border_002" data-bind="css:$root.selectType()?'':'w49'">赔率</span>
                        <span class="border_003" data-bind="css:$root.selectType()?'':'dn'">金额</span><i></i>
                    </div>
                </div>
                <!--/ko-->
                <div>
                    <div>
                        <div class="bd clear" data-bind="click:$root.getFastItem">
                            <span class="ball"
                                  data-bind="text:N,css:(/^\d|\d\d[^-]$/.test(N))?'ball_com ball_'+N:(/[^\'\']/.test(N)?'':'dn'),style:{width:$root.selectType()?'':'50%'},attr:{'title':$data.kname+'：'+N}"></span>
                            <span class="odds"
                                  data-bind="text:$root.status()?odd():odd(),css:/[^\'\']/.test(N) ? ($root.isFP(odd()) ? 'odds-close-text' : ($root.selectType() ? '' : 'w49')) : 'dn'"></span>
                            <span class="amount_pane"
                                  data-bind="css:$root.selectType()?(/[^\'\']/.test(N)?'':'dn'):'dn'"><input type="text"
                                                                                                             class="amount"
                                                                                                             maxlength="9"
                                                                                                             data-bind="enable:$root.status,textInput:amount"/></span>
                        </div>
                    </div>
                </div>
            </li>
        </ul>
    </div>
</script>


<script type="text/html" id="filterPanel">
    <div class="clear gxks-con" data-bind="css:$root.selectType()?'':'dn'">
        <div class="fl ui-kkk-btn" style="width:80%;">
            <!--ko foreach:[
                    {label:"波色：", data:[{'oid':'b1',name:'红'},{'oid':'b2',name:'蓝'},{'oid':'b0',name:'绿'}]},
                    {label:"两面：", data:[
                                            {'oid':'l1',name:'单'},{'oid':'l0',name:'双'},{'oid':'n0',name:'大'},
                                            {'oid':'n1',name:'小'},{'oid':'l3',name:'合单'},{'oid':'l2',name:'合双'}
                                        ]
                    },
                    {label:"头尾：", data:[
                                            {'oid':'t0',name:'0头'},{'oid':'t1',name:'1头'},{'oid':'t2',name:'2头'},
                                            {'oid':'v0',name:'0尾'},{'oid':'v1',name:'1尾'},{'oid':'v2',name:'2尾'},
                                            {'oid':'v3',name:'3尾'},{'oid':'v4',name:'4尾'},{'oid':'v5',name:'5尾'},
                                            {'oid':'v6',name:'6尾'},{'oid':'v7',name:'7尾'},{'oid':'v8',name:'8尾'},{'oid':'v9',name:'9尾'}
                                        ]
                    }
                ]-->
            <div>
                <label data-bind="text:label"></label>
                <label data-bind="foreach:data">
                    <a href="javasript:;"
                       data-bind="text:name, attr:{'data-oid':oid}, click:$root.filterButton.bind($data,$root)"></a>
                </label>
            </div>
            <!--/ko-->
        </div>
        <div class="fr" style="width:20%;">
            <label class="tc tt">金额</label>
            <label class="tc">
                <input type="text" class="amount" data-bind="textInput:$root.forwardAmount,enable:$root.status"
                       maxlength="9">
            </label>
            <label class="tc">
                <a href="javascript:;" class="btn btn-transfer"
                   data-bind="click:$root.forwardSubmit.bind($data,$root)"></a>
                <a href="javascript:;" class="btn" data-bind="click:$root.reset.bind($data,$root)"></a>
            </label>
        </div>
    </div>
    <div class="s1_nav clear_fix">
        <ul class="clear" data-bind="foreach:{data:$data,as:'item'},css:$root.selectType()?'':'fast_slct'">
            <li class="w150 w150px">
                <div class="zm16_nav" data-bind="css:item.title===''?'dn':'',text:item.title"></div>
                <div class="clear ui-ball-span-box">
                    <div>
                        <span class="border_001" data-bind="css:$root.selectType()?'':'w49'">号码</span>
                        <span class="border_002" data-bind="css:$root.selectType()?'':'w49'">赔率</span>
                        <span class="border_003" data-bind="css:$root.selectType()?'':'dn'">金额</span><i></i>
                    </div>
                </div>
                <div data-bind="foreach:{data:item.list,as:'u'}">
                    <div class="bd clear" data-bind="click:$root.getFastItem">
                        <span class="ball"
                              data-bind="text:u.N,css:(/^[0-9]+$/.test(u.N))?'ball_com ball_'+u.N:(/[^\'\']/.test(u.N)?'':'dn'),style:{width:$root.selectType()?'':'50%'},attr:{'title':(/:+/.test(u.kname))?u.kname:u.kname+'：'+u.N}"></span>
                        <span class="odds"
                              data-bind="text:$root.status()?odd():odd(),css:/[^\'\']/.test(N) ? ($root.isFP(odd()) ? 'odds-close-text' : ($root.selectType() ? '' : 'w49')) : 'dn'"></span>
                        <span class="amount_pane" data-bind="css:$root.selectType()?(/[^\'\']/.test(u.N)?'':'dn'):'dn'">
                            <input type="text" class="amount" data-bind="enable:$root.status,textInput:u.amount"
                                   maxlength="9"/>
                        </span>
                    </div>
                </div>
            </li>
        </ul>
    </div>
    <div style="clear:both;"></div>
</script>

<script type="text/html" id="pcddTemp">
    <form data-bind="submit: $root.buildOrders.bind($data,$root), selectAmount:$root.quickAmountArr">
        <div class="main_txt bet-wrap w900">
            <div class="txt_section clear_fix">
                <div class="s1_nav clear_fix">
                    <!--ko if:list().length>0-->
                    <ul class="clear"
                        data-bind="foreach:{data:list()[0],as:'item'},css:$root.selectType()?'':'fast_slct'">
                        <li data-bind="css:$parent.template.comW">
                            <!--ko if:item.list.length>0-->
                            <div class="zm16_nav" data-bind="css:item.title==''?'dn':'',text:item.title"></div>
                            <div class="clear ui-ball-span-box">
                                <div class="clear">
                                    <span class="border_001" data-bind="css:$parents[1].selectType()?'':'w49'">号码</span>
                                    <span class="border_002" data-bind="css:$parents[1].selectType()?'':'w49'">赔率</span>
                                    <span class="border_003"
                                          data-bind="css:$parents[1].selectType()?'':'dn'">金额</span><i></i>
                                </div>
                            </div>
                            <!--/ko-->
                            <div data-bind="foreach:{data:item.list,as:'index'}">
                                <div class="bd clear" data-bind="click:$root.getFastItem">
                                    <span class="ball"
                                          data-bind="html:N, css:(/^[0-9]+$/.test(N))?'ball_com ball_'+N:(/[^\'\']/.test(N)?'':'dn'),style:{width:$root.selectType()?'':'50%'},attr:{'title':(/:+/.test($data.kname))?$data.kname:$data.kname+'：'+N}"></span>
                                    <span class="odds"
                                          data-bind="text:$root.status()?index['odd']():index['odd'](), css:/[^\'\']/.test(N) ? ($root.isFP(odd()) ? 'odds-close-text' : ($root.selectType() ? '' : 'w49')) : 'dn'"></span>
                                    <span class="amount_pane"
                                          data-bind="css:$root.selectType()?(/[^\'\']/.test(N)?'':'dn'):'dn'">
                                        <input type="text" class="amount"
                                               data-bind="textinput:amount, enable:$root.status, attr:{'oid':$root.status}"
                                               maxlength="9"/>
                                    </span>
                                </div>
                            </div>
                        </li>
                    </ul>
                    <!--/ko-->
                </div>
            </div>
            <div class="bd txt_section5">
                <!--ko if:list().length>2-->
                <!--ko foreach:list()[2] -->
                <div class="dist-table" data-bind="css:$root.selectType()?(/[^\'\']/.test(N)?'':'dn'):'dn'">
                    <div style="float:left;" data-bind="click:$root.getFastItem">
                        <span class="ball_m" style="color:white" data-bind="css:title==''?'dn':'',text:title"></span>
                        <span class="odds"
                              data-bind="text:$root.status()?odd():odd(), css:/[^\'\']/.test(N) ? ($root.isFP(odd()) ? 'odds-close-text' : '') : 'dn'"></span>
                    </div>
                    <div class="dist-select">
                        <div class="gameSelect">
                            <span class="ball" data-bind="text:selectedNum0,css:'ball_'+selectedNum0()">12</span>

                            <select data-bind="value: selectedID0, enable:$root.status() && numbers.length > 3, foreach: numbers">
                                <option data-bind="text: N, attr: { disabled: !!optionDisable(), value: D }">text
                                </option>
                            </select>
                        </div>
                        <div class="gameSelect">
                            <span class="ball" data-bind="text:selectedNum1,css:'ball_'+selectedNum1()">12</span>


                            <select data-bind="value: selectedID1, enable:$root.status() && numbers.length > 3, foreach: numbers">
                                <option data-bind="text: N, attr: { disabled: !!optionDisable(), value: D }">text
                                </option>
                            </select>
                        </div>
                        <div class="gameSelect">
                            <span class="ball" data-bind="text:selectedNum2,css:'ball_'+selectedNum2()">12</span>
                            <select data-bind="value: selectedID2, enable:$root.status() && numbers.length > 3, foreach: numbers">
                                <option data-bind="text: N, attr: { disabled: !!optionDisable(), value: D }">text
                                </option>
                            </select>
                        </div>
                        <span class="amount_pane">
                            <input type="text" class="int-text"
                                   data-bind="textinput:amount, enable:$root.status() && numbers.length >= 3, attr:{'oid':$root.status()}"
                                   maxlength="9"/>
                        </span>
                    </div>
                </div>
                <!--/ko-->
                <!--/ko-->
                <div class="btn_ok">
                    <div class="clear">
                        <input type="text" class="amount"
                               data-bind="enable:$root.status,css:$root.selectType()?'dn':'',value:$root.fastAmount,textInput:$root.fastAmount"
                               maxlength="9">
                        <span>
                            <input type="submit" class="btn btn-green" value="" title="确定"/>
                            <input type="button" class="btn" data-bind="click:$root.reset.bind($data,$root)" value=""
                                   title="取消"/>
                            <input type="reset" style="display:none;"/>
                        </span>
                    </div>
                </div>
            </div>

            <div class="txt_section clear_fix">
                <div class="s1_nav clear_fix">
                    <!--ko if:list().length>1-->
                    <ul class="clear"
                        data-bind="foreach:{data:list()[1],as:'item'},css:$root.selectType()?'':'fast_slct'">
                        <li data-bind="css:$parent.template.comW">
                            <!--ko if:item.list.length>0-->
                            <div class="zm16_nav" data-bind="css:item.title==''?'dn':'',text:item.title"></div>
                            <div class="clear ui-ball-span-box">
                                <div class="clear">
                                    <span class="border_001" data-bind="css:$parents[1].selectType()?'':'w49'">号码</span>
                                    <span class="border_002" data-bind="css:$parents[1].selectType()?'':'w49'">赔率</span>
                                    <span class="border_003"
                                          data-bind="css:$parents[1].selectType()?'':'dn'">金额</span><i></i>
                                </div>
                            </div>
                            <!--/ko-->
                            <div data-bind="foreach:{data:item.list,as:'index'}">
                                <div class="bd clear" data-bind="click:$root.getFastItem">
                                    <span class="ball"
                                          data-bind="html:N,css:(/^[0-9]+$/.test(N))?'ball_com ball_'+N:(/[^\'\']/.test(N)?'':'dn'),style:{width:$root.selectType()?'':'50%'},attr:{'title':(/:+/.test($data.kname))?$data.kname:$data.kname+'：'+N}"></span>
                                    <span class="odds"
                                          data-bind="text:$root.status()? index['odd']() :index['odd'](), css:/[^\'\']/.test(N) ? ($root.isFP(odd()) ? 'odds-close-text' : ($root.selectType() ? '' : 'w49')) : 'dn'"></span>
                                    <span class="amount_pane"
                                          data-bind="css:$root.selectType()?(/[^\'\']/.test(N)?'':'dn'):'dn'">
                                        <input type="text" class="amount"
                                               data-bind="textinput:amount, enable:$root.status, attr:{'oid':$root.status}"
                                               maxlength="9"/>
                                    </span>
                                </div>
                            </div>
                        </li>
                    </ul>
                    <!--/ko-->
                </div>
            </div>
        </div>
    </form>
</script>

<script type="text/html" id="pk10PlanTemp">
    <form data-bind="submit: $root.buildOrders.bind($data,$root),selectAmount:$root.quickAmountArr">
        <div data-bind="foreach:{data:list()[0],as:'item'}" class="zm16_txt">
            <div class="zm16_nav nav-plan" data-oid="1"
                 data-bind="text: item.title,css:$index()==0?'active':'',attr:{'data-oid':item.code,'data-oname':item.title},click:$parent.planZhClick">
                前三
            </div>
        </div>
        <div id="pk10PlanList">
            <div class="fast_slct" data-bind="foreach:{data:planOddsList().list,as:'item'}">
                <div class="bd-plan clear"
                     data-bind="click:$root.getFastItem,css:'plan_ball_'+item.N,attr:{'data-bid':item.N}">
                    <span class="ball_plan"
                          data-bind="html:item.N, css:(/^[0-9]+$/.test(item.N))?'ball_com ball_'+item.N:(/[^\'\']/.test(item.N)?'':'dn')"></span>
                    <span class="odds"
                          data-bind="text:$root.status()?item['odd']():item['odd'](), css:/[^\'\']/.test(item.N) ? ($root.isFP(odd()) ? 'odds-close-text' : ($root.selectType() ? '' : 'w49')) : 'dn'"></span>
                </div>
            </div>
            <div class="btn-wrap">
                <input type="text" class="amount"
                       data-bind="enable:$root.status,value:$root.fastAmount,textInput:$root.fastAmount"
                       maxlength="9">
                <input type="submit" class="btn btn-green" value="" title="确认"/>
                <input type="reset" class="btn" value="" title="取消" data-bind="click:$root.reset.bind($data,$root)">
                <div style="float: right;font-size: 15px;margin-top: 18px;margin-right: 10px;" data-bind="text:$root.currentPlanNumInfo"></div>
            </div>
            <div class="plan_slct s1_nav clear_fix" style="margin-top: 60px">
                <div class="zm16_txt">
                    <div class="zm16_nav nav-tab active" data-oid="5" data-bind="click:$parent.planBallNumClick">五码计划
                    </div>
                    <div class="zm16_nav nav-tab" data-oid="6" data-bind="click:$parent.planBallNumClick">六码计划</div>
                    <div class="zm16_nav nav-tab" data-oid="7" data-bind="click:$parent.planBallNumClick">七码计划</div>
                </div>
                <div class="clear ui-ball-span-box" style="width: 100%;display: inline-block; overflow: hidden;">
                    <div class="clear">
                        <span style="width: 15%;border-right:1px solid #a4a4a4">专家</span>
                        <span style="width: 10%;border-right:1px solid #a4a4a4;">球号</span>
                        <span style="width: 27%;border-right:1px solid #a4a4a4;">预测号码</span>
                        <span style="width: 10%;border-right:1px solid #a4a4a4;">长龙</span>
                        <span style="width: 10%;border-right:1px solid #a4a4a4;">胜率</span>
                        <span style="width: 27%;border-right:0px solid #a4a4a4;"></span>
                    </div>
                </div>
                <div data-bind="foreach:{data:planList(),as:'pl'}">
                    <div class="clear">
                        <span style="width: 15%;border-right:1px solid #a4a4a4;border-bottom: 1px solid #c9c7c7;cursor: pointer;color: #0a94e3"
                              data-bind="html:pl.predictorName,click:$parent.appendHis">专家</span>
                        <span style="width: 10%;border-right:1px solid #a4a4a4;border-bottom: 1px solid #c9c7c7;" data-bind="html:pl.playName">冠军</span>
                        <span style="width: 27%;border-right:1px solid #a4a4a4;border-bottom: 1px solid #c9c7c7;font-size: 14px;font-weight: bold"
                              data-bind="html:pl.haoMa">预测号码</span>
                        <span style="width: 10%;border-right:1px solid #a4a4a4;border-bottom: 1px solid #c9c7c7;"
                              data-bind="html:pl.cl">长龙</span>
                        <span style="width: 10%;border-right:1px solid #a4a4a4;border-bottom: 1px solid #c9c7c7;"
                              data-bind="html:pl.winRate+'%'">胜率</span>
                        <span style="width: 27%;border-right:0px solid #a4a4a4;border-bottom: 1px solid #c9c7c7;">
                            <a data-bind="click:$parent.followPlanClick" class="plan-button">跟投</a>
                            <a data-bind="click:$parent.unFollowPlanClick" class="plan-button">反投</a>
                            <a data-bind="click:$parent.append" class="plan-button">自动跟投</a>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </form>

</script>
<div class="side_news">
    <div class="sn_in">
        <div class="sn_h black_bg">即时资讯</div>
        <div class="sn_c" style="color:#fff" id="NOTICE-BOX">

        </div>
    </div>
</div>


</div>
<div id="tpl_num_one" class="ui-quick-num-mod-box" style="display:none;">
    <ul data-bind="foreach:quickAmountArr">
        <li data-bind="click:$root.numClick"><a href="javascript:;" data-bind="click:$root.numClick,html:$data">100元</a></li>
    </ul>
</div>
<div class="ui-ww-box ui-ww-box1" style="position:absolute;right:0;top:80px"></div>
<script type="text/html" id="tpl_bet">
    <div class="betorder-box">
        <div class="bb-list">
            <table>
                <thead>
                    <tr>
                        <th>内容</th>
                        <th>赔率</th>
                        <th>下注金额</th>
                    </tr>
                </thead>
                <tbody data-bind="foreach:betList">
                    <tr>
                        <td data-bind="html:kname+'：'+N"></td>
                        <td data-bind="text:odd">0</td>
                        <td data-bind="text:amount()"></td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class="foot-total">共 <b class="redtext" data-bind="text:betList.length">X</b> 注，合计下注金额 <b class="redtext" data-bind="text:totalAmount">X</b></div>
    </div>
</script>

<script type="text/html" id="tpl-diysum">
    <div class="diy-slct-sum" style="top:115px;">
        <div class="dss-warn">注意：设置只保存在您的电脑，清空浏览器缓存或更换电脑会显示默认值。</div>
        <div class="dss-content clear">
            <ul data-bind="foreach:list">
                <li>
                    <i data-bind="click:$root.remove">删除</i>
                    <i data-bind="visible:$index()+1!==5&&$index()+1===$parent.list().length,click:$root.add" class="red_btn">增加</i>
                    <input type="text" data-bind="textInput:val" />
                </li>
            </ul>
        </div>
        <div class="dss-tip" data-bind="text:tip"></div>
        <div class="dss-btn">
            <a href="javascript:;" data-bind="click:changeStatus,text:status()?'禁用':'启用'"></a>
            <a href="javascript:;" class="btn btn-mini" data-bind="click:save">保存</a>
        </div>
    </div>
</script><script type="text/javascript">
	var top10orders = [];
	
    var PAGE_OPEN_TIME = new Date() - 0;
    //最新期数
    var LatestPriodData = '{"ServerTime":"2025/06/03 15:20:52","fclosetime":"2025/06/03 15:21:25","fid":70793526,"fisstopseles":"False","flottostarttime":"2025/06/03 15:21:30","fnextclosetime":1748935360000,"fnextperiod":"202506030738","fnextstarttime":"2025/06/03 15:21:30","fnumberofperiod":"202506030737","fpreviousperiod":"202506030736","fpreviousresult":"09,05,02,04,10,03,06,07,08,01","fsettlefid":70793525,"fsettlenumber":"202506030736","fsettletime":"2025/06/03 15:18:55","fstarttime":"2025/06/03 15:20:15","fstatus":1,"nextPeriodEndTimeYear":2025,"periodEndTimeYear":2025,"prePeriodEndTimeYear":2025}';
    //历史开奖结果
    var ResultList = '{"total":10,"list":[{"date":"2025-06-03 15:20","period":"202506030736","result":"09,05,02,04,10,03,06,07,08,01"},{"date":"2025-06-03 15:19","period":"202506030735","result":"07,05,03,08,01,06,04,09,02,10"},{"date":"2025-06-03 15:17","period":"202506030734","result":"04,02,01,05,09,07,06,03,08,10"},{"date":"2025-06-03 15:16","period":"202506030733","result":"01,06,02,04,09,10,03,07,08,05"},{"date":"2025-06-03 15:15","period":"202506030732","result":"05,09,10,02,06,01,03,04,08,07"},{"date":"2025-06-03 15:14","period":"202506030731","result":"06,03,01,07,08,10,09,04,02,05"},{"date":"2025-06-03 15:12","period":"202506030730","result":"01,09,05,10,03,04,02,06,07,08"},{"date":"2025-06-03 15:11","period":"202506030729","result":"10,09,05,01,07,02,03,04,08,06"},{"date":"2025-06-03 15:10","period":"202506030728","result":"02,10,09,03,07,04,06,01,05,08"},{"date":"2025-06-03 15:09","period":"202506030727","result":"10,08,02,01,03,09,05,07,06,04"}]}';
    //玩法、玩法项树状结构树
    var navListJson = '[{"C":[{"C":[{"B":"0","D":"gyjhda","I":"gyjh","M":"2.19","N":"大","O":"2.19","oddShowName":"大"},{"B":"0","D":"gyjhx","I":"gyjh","M":"1.79","N":"小","O":"1.79","oddShowName":"小"},{"B":"0","D":"gyjhd","I":"gyjh","M":"1.79","N":"单","O":"1.79","oddShowName":"单"},{"B":"0","D":"gyjhs","I":"gyjh","M":"2.19","N":"双","O":"2.19","oddShowName":"双"}],"I":"gyjh","N":"冠，亚和"},{"C":[{"B":"0","D":"gjda","I":"gj","M":"1.99","N":"大","O":"1.99","oddShowName":"大"},{"B":"0","D":"gjx","I":"gj","M":"1.99","N":"小","O":"1.99","oddShowName":"小"},{"B":"0","D":"gjd","I":"gj","M":"1.99","N":"单","O":"1.99","oddShowName":"单"},{"B":"0","D":"gjs","I":"gj","M":"1.99","N":"双","O":"1.99","oddShowName":"双"},{"B":"0","D":"gjl","I":"gj","M":"1.99","N":"1V10龙","O":"1.99","oddShowName":"1V10龙"},{"B":"0","D":"gjh","I":"gj","M":"1.99","N":"1V10虎","O":"1.99","oddShowName":"1V10虎"}],"I":"gj","N":"冠军"},{"C":[{"B":"0","D":"yjda","I":"yj","M":"1.99","N":"大","O":"1.99","oddShowName":"大"},{"B":"0","D":"yjx","I":"yj","M":"1.99","N":"小","O":"1.99","oddShowName":"小"},{"B":"0","D":"yjd","I":"yj","M":"1.99","N":"单","O":"1.99","oddShowName":"单"},{"B":"0","D":"yjs","I":"yj","M":"1.99","N":"双","O":"1.99","oddShowName":"双"},{"B":"0","D":"yjl","I":"yj","M":"1.99","N":"2V9龙","O":"1.99","oddShowName":"2V9龙"},{"B":"0","D":"yjh","I":"yj","M":"1.99","N":"2V9虎","O":"1.99","oddShowName":"2V9虎"}],"I":"yj","N":"亚军"},{"C":[{"B":"0","D":"dsmda","I":"dsm","M":"1.99","N":"大","O":"1.99","oddShowName":"大"},{"B":"0","D":"dsmx","I":"dsm","M":"1.99","N":"小","O":"1.99","oddShowName":"小"},{"B":"0","D":"dsmd","I":"dsm","M":"1.99","N":"单","O":"1.99","oddShowName":"单"},{"B":"0","D":"dsms","I":"dsm","M":"1.99","N":"双","O":"1.99","oddShowName":"双"},{"B":"0","D":"dsml","I":"dsm","M":"1.99","N":"3V8龙","O":"1.99","oddShowName":"3V8龙"},{"B":"0","D":"dsmh","I":"dsm","M":"1.99","N":"3V8虎","O":"1.99","oddShowName":"3V8虎"}],"I":"dsm","N":"第三名"},{"C":[{"B":"0","D":"dsimda","I":"dsim","M":"1.99","N":"大","O":"1.99","oddShowName":"大"},{"B":"0","D":"dsimx","I":"dsim","M":"1.99","N":"小","O":"1.99","oddShowName":"小"},{"B":"0","D":"dsimd","I":"dsim","M":"1.99","N":"单","O":"1.99","oddShowName":"单"},{"B":"0","D":"dsims","I":"dsim","M":"1.99","N":"双","O":"1.99","oddShowName":"双"},{"B":"0","D":"dsiml","I":"dsim","M":"1.99","N":"4V7龙","O":"1.99","oddShowName":"4V7龙"},{"B":"0","D":"dsimh","I":"dsim","M":"1.99","N":"4V7虎","O":"1.99","oddShowName":"4V7虎"}],"I":"dsim","N":"第四名"},{"C":[{"B":"0","D":"dwmda","I":"dwm","M":"1.99","N":"大","O":"1.99","oddShowName":"大"},{"B":"0","D":"dwmx","I":"dwm","M":"1.99","N":"小","O":"1.99","oddShowName":"小"},{"B":"0","D":"dwmd","I":"dwm","M":"1.99","N":"单","O":"1.99","oddShowName":"单"},{"B":"0","D":"dwms","I":"dwm","M":"1.99","N":"双","O":"1.99","oddShowName":"双"},{"B":"0","D":"dwml","I":"dwm","M":"1.99","N":"5V6龙","O":"1.99","oddShowName":"5V6龙"},{"B":"0","D":"dwmh","I":"dwm","M":"1.99","N":"5V6虎","O":"1.99","oddShowName":"5V6虎"}],"I":"dwm","N":"第五名"},{"C":[{"B":"0","D":"dlmda","I":"dlm","M":"1.99","N":"大","O":"1.99","oddShowName":"大"},{"B":"0","D":"dlmx","I":"dlm","M":"1.99","N":"小","O":"1.99","oddShowName":"小"},{"B":"0","D":"dlmd","I":"dlm","M":"1.99","N":"单","O":"1.99","oddShowName":"单"},{"B":"0","D":"dlms","I":"dlm","M":"1.99","N":"双","O":"1.99","oddShowName":"双"}],"I":"dlm","N":"第六名"},{"C":[{"B":"0","D":"dqmda","I":"dqm","M":"1.99","N":"大","O":"1.99","oddShowName":"大"},{"B":"0","D":"dqmx","I":"dqm","M":"1.99","N":"小","O":"1.99","oddShowName":"小"},{"B":"0","D":"dqmd","I":"dqm","M":"1.99","N":"单","O":"1.99","oddShowName":"单"},{"B":"0","D":"dqms","I":"dqm","M":"1.99","N":"双","O":"1.99","oddShowName":"双"}],"I":"dqm","N":"第七名"},{"C":[{"B":"0","D":"dbmda","I":"dbm","M":"1.99","N":"大","O":"1.99","oddShowName":"大"},{"B":"0","D":"dbmx","I":"dbm","M":"1.99","N":"小","O":"1.99","oddShowName":"小"},{"B":"0","D":"dbmd","I":"dbm","M":"1.99","N":"单","O":"1.99","oddShowName":"单"},{"B":"0","D":"dbms","I":"dbm","M":"1.99","N":"双","O":"1.99","oddShowName":"双"}],"I":"dbm","N":"第八名"},{"C":[{"B":"0","D":"djmda","I":"djm","M":"1.99","N":"大","O":"1.99","oddShowName":"大"},{"B":"0","D":"djmx","I":"djm","M":"1.99","N":"小","O":"1.99","oddShowName":"小"},{"B":"0","D":"djmd","I":"djm","M":"1.99","N":"单","O":"1.99","oddShowName":"单"},{"B":"0","D":"djms","I":"djm","M":"1.99","N":"双","O":"1.99","oddShowName":"双"}],"I":"djm","N":"第九名"},{"C":[{"B":"0","D":"dshmda","I":"dshm","M":"1.99","N":"大","O":"1.99","oddShowName":"大"},{"B":"0","D":"dshmx","I":"dshm","M":"1.99","N":"小","O":"1.99","oddShowName":"小"},{"B":"0","D":"dshmd","I":"dshm","M":"1.99","N":"单","O":"1.99","oddShowName":"单"},{"B":"0","D":"dshms","I":"dshm","M":"1.99","N":"双","O":"1.99","oddShowName":"双"}],"I":"dshm","N":"第十名"}],"I":"zh","N":"两面"},{"C":[{"C":[{"B":"0","D":"dgj1","I":"dgj","M":"9.95","N":"1","O":"9.95","oddShowName":"1"},{"B":"0","D":"dgj2","I":"dgj","M":"9.95","N":"2","O":"9.95","oddShowName":"2"},{"B":"0","D":"dgj3","I":"dgj","M":"9.95","N":"3","O":"9.95","oddShowName":"3"},{"B":"0","D":"dgj4","I":"dgj","M":"9.95","N":"4","O":"9.95","oddShowName":"4"},{"B":"0","D":"dgj5","I":"dgj","M":"9.95","N":"5","O":"9.95","oddShowName":"5"},{"B":"0","D":"dgj6","I":"dgj","M":"9.95","N":"6","O":"9.95","oddShowName":"6"},{"B":"0","D":"dgj7","I":"dgj","M":"9.95","N":"7","O":"9.95","oddShowName":"7"},{"B":"0","D":"dgj8","I":"dgj","M":"9.95","N":"8","O":"9.95","oddShowName":"8"},{"B":"0","D":"dgj9","I":"dgj","M":"9.95","N":"9","O":"9.95","oddShowName":"9"},{"B":"0","D":"dgj10","I":"dgj","M":"9.95","N":"10","O":"9.95","oddShowName":"10"}],"I":"dgj","N":"冠军"},{"C":[{"B":"0","D":"dyj1","I":"dyj","M":"9.95","N":"1","O":"9.95","oddShowName":"1"},{"B":"0","D":"dyj2","I":"dyj","M":"9.95","N":"2","O":"9.95","oddShowName":"2"},{"B":"0","D":"dyj3","I":"dyj","M":"9.95","N":"3","O":"9.95","oddShowName":"3"},{"B":"0","D":"dyj4","I":"dyj","M":"9.95","N":"4","O":"9.95","oddShowName":"4"},{"B":"0","D":"dyj5","I":"dyj","M":"9.95","N":"5","O":"9.95","oddShowName":"5"},{"B":"0","D":"dyj6","I":"dyj","M":"9.95","N":"6","O":"9.95","oddShowName":"6"},{"B":"0","D":"dyj7","I":"dyj","M":"9.95","N":"7","O":"9.95","oddShowName":"7"},{"B":"0","D":"dyj8","I":"dyj","M":"9.95","N":"8","O":"9.95","oddShowName":"8"},{"B":"0","D":"dyj9","I":"dyj","M":"9.95","N":"9","O":"9.95","oddShowName":"9"},{"B":"0","D":"dyj10","I":"dyj","M":"9.95","N":"10","O":"9.95","oddShowName":"10"}],"I":"dyj","N":"亚军"},{"C":[{"B":"0","D":"ddsm1","I":"ddsm","M":"9.95","N":"1","O":"9.95","oddShowName":"1"},{"B":"0","D":"ddsm2","I":"ddsm","M":"9.95","N":"2","O":"9.95","oddShowName":"2"},{"B":"0","D":"ddsm3","I":"ddsm","M":"9.95","N":"3","O":"9.95","oddShowName":"3"},{"B":"0","D":"ddsm4","I":"ddsm","M":"9.95","N":"4","O":"9.95","oddShowName":"4"},{"B":"0","D":"ddsm5","I":"ddsm","M":"9.95","N":"5","O":"9.95","oddShowName":"5"},{"B":"0","D":"ddsm6","I":"ddsm","M":"9.95","N":"6","O":"9.95","oddShowName":"6"},{"B":"0","D":"ddsm7","I":"ddsm","M":"9.95","N":"7","O":"9.95","oddShowName":"7"},{"B":"0","D":"ddsm8","I":"ddsm","M":"9.95","N":"8","O":"9.95","oddShowName":"8"},{"B":"0","D":"ddsm9","I":"ddsm","M":"9.95","N":"9","O":"9.95","oddShowName":"9"},{"B":"0","D":"ddsm10","I":"ddsm","M":"9.95","N":"10","O":"9.95","oddShowName":"10"}],"I":"ddsm","N":"第三名"},{"C":[{"B":"0","D":"ddsim1","I":"ddsim","M":"9.95","N":"1","O":"9.95","oddShowName":"1"},{"B":"0","D":"ddsim2","I":"ddsim","M":"9.95","N":"2","O":"9.95","oddShowName":"2"},{"B":"0","D":"ddsim3","I":"ddsim","M":"9.95","N":"3","O":"9.95","oddShowName":"3"},{"B":"0","D":"ddsim4","I":"ddsim","M":"9.95","N":"4","O":"9.95","oddShowName":"4"},{"B":"0","D":"ddsim5","I":"ddsim","M":"9.95","N":"5","O":"9.95","oddShowName":"5"},{"B":"0","D":"ddsim6","I":"ddsim","M":"9.95","N":"6","O":"9.95","oddShowName":"6"},{"B":"0","D":"ddsim7","I":"ddsim","M":"9.95","N":"7","O":"9.95","oddShowName":"7"},{"B":"0","D":"ddsim8","I":"ddsim","M":"9.95","N":"8","O":"9.95","oddShowName":"8"},{"B":"0","D":"ddsim9","I":"ddsim","M":"9.95","N":"9","O":"9.95","oddShowName":"9"},{"B":"0","D":"ddsim10","I":"ddsim","M":"9.95","N":"10","O":"9.95","oddShowName":"10"}],"I":"ddsim","N":"第四名"},{"C":[{"B":"0","D":"ddwm1","I":"ddwm","M":"9.95","N":"1","O":"9.95","oddShowName":"1"},{"B":"0","D":"ddwm2","I":"ddwm","M":"9.95","N":"2","O":"9.95","oddShowName":"2"},{"B":"0","D":"ddwm3","I":"ddwm","M":"9.95","N":"3","O":"9.95","oddShowName":"3"},{"B":"0","D":"ddwm4","I":"ddwm","M":"9.95","N":"4","O":"9.95","oddShowName":"4"},{"B":"0","D":"ddwm5","I":"ddwm","M":"9.95","N":"5","O":"9.95","oddShowName":"5"},{"B":"0","D":"ddwm6","I":"ddwm","M":"9.95","N":"6","O":"9.95","oddShowName":"6"},{"B":"0","D":"ddwm7","I":"ddwm","M":"9.95","N":"7","O":"9.95","oddShowName":"7"},{"B":"0","D":"ddwm8","I":"ddwm","M":"9.95","N":"8","O":"9.95","oddShowName":"8"},{"B":"0","D":"ddwm9","I":"ddwm","M":"9.95","N":"9","O":"9.95","oddShowName":"9"},{"B":"0","D":"ddwm10","I":"ddwm","M":"9.95","N":"10","O":"9.95","oddShowName":"10"}],"I":"ddwm","N":"第五名"},{"C":[{"B":"0","D":"ddlm1","I":"ddlm","M":"9.95","N":"1","O":"9.95","oddShowName":"1"},{"B":"0","D":"ddlm2","I":"ddlm","M":"9.95","N":"2","O":"9.95","oddShowName":"2"},{"B":"0","D":"ddlm3","I":"ddlm","M":"9.95","N":"3","O":"9.95","oddShowName":"3"},{"B":"0","D":"ddlm4","I":"ddlm","M":"9.95","N":"4","O":"9.95","oddShowName":"4"},{"B":"0","D":"ddlm5","I":"ddlm","M":"9.95","N":"5","O":"9.95","oddShowName":"5"},{"B":"0","D":"ddlm6","I":"ddlm","M":"9.95","N":"6","O":"9.95","oddShowName":"6"},{"B":"0","D":"ddlm7","I":"ddlm","M":"9.95","N":"7","O":"9.95","oddShowName":"7"},{"B":"0","D":"ddlm8","I":"ddlm","M":"9.95","N":"8","O":"9.95","oddShowName":"8"},{"B":"0","D":"ddlm9","I":"ddlm","M":"9.95","N":"9","O":"9.95","oddShowName":"9"},{"B":"0","D":"ddlm10","I":"ddlm","M":"9.95","N":"10","O":"9.95","oddShowName":"10"}],"I":"ddlm","N":"第六名"},{"C":[{"B":"0","D":"ddqm1","I":"ddqm","M":"9.95","N":"1","O":"9.95","oddShowName":"1"},{"B":"0","D":"ddqm2","I":"ddqm","M":"9.95","N":"2","O":"9.95","oddShowName":"2"},{"B":"0","D":"ddqm3","I":"ddqm","M":"9.95","N":"3","O":"9.95","oddShowName":"3"},{"B":"0","D":"ddqm4","I":"ddqm","M":"9.95","N":"4","O":"9.95","oddShowName":"4"},{"B":"0","D":"ddqm5","I":"ddqm","M":"9.95","N":"5","O":"9.95","oddShowName":"5"},{"B":"0","D":"ddqm6","I":"ddqm","M":"9.95","N":"6","O":"9.95","oddShowName":"6"},{"B":"0","D":"ddqm7","I":"ddqm","M":"9.95","N":"7","O":"9.95","oddShowName":"7"},{"B":"0","D":"ddqm8","I":"ddqm","M":"9.95","N":"8","O":"9.95","oddShowName":"8"},{"B":"0","D":"ddqm9","I":"ddqm","M":"9.95","N":"9","O":"9.95","oddShowName":"9"},{"B":"0","D":"ddqm10","I":"ddqm","M":"9.95","N":"10","O":"9.95","oddShowName":"10"}],"I":"ddqm","N":"第七名"},{"C":[{"B":"0","D":"ddbm1","I":"ddbm","M":"9.95","N":"1","O":"9.95","oddShowName":"1"},{"B":"0","D":"ddbm2","I":"ddbm","M":"9.95","N":"2","O":"9.95","oddShowName":"2"},{"B":"0","D":"ddbm3","I":"ddbm","M":"9.95","N":"3","O":"9.95","oddShowName":"3"},{"B":"0","D":"ddbm4","I":"ddbm","M":"9.95","N":"4","O":"9.95","oddShowName":"4"},{"B":"0","D":"ddbm5","I":"ddbm","M":"9.95","N":"5","O":"9.95","oddShowName":"5"},{"B":"0","D":"ddbm6","I":"ddbm","M":"9.95","N":"6","O":"9.95","oddShowName":"6"},{"B":"0","D":"ddbm7","I":"ddbm","M":"9.95","N":"7","O":"9.95","oddShowName":"7"},{"B":"0","D":"ddbm8","I":"ddbm","M":"9.95","N":"8","O":"9.95","oddShowName":"8"},{"B":"0","D":"ddbm9","I":"ddbm","M":"9.95","N":"9","O":"9.95","oddShowName":"9"},{"B":"0","D":"ddbm10","I":"ddbm","M":"9.95","N":"10","O":"9.95","oddShowName":"10"}],"I":"ddbm","N":"第八名"},{"C":[{"B":"0","D":"ddjm1","I":"ddjm","M":"9.95","N":"1","O":"9.95","oddShowName":"1"},{"B":"0","D":"ddjm2","I":"ddjm","M":"9.95","N":"2","O":"9.95","oddShowName":"2"},{"B":"0","D":"ddjm3","I":"ddjm","M":"9.95","N":"3","O":"9.95","oddShowName":"3"},{"B":"0","D":"ddjm4","I":"ddjm","M":"9.95","N":"4","O":"9.95","oddShowName":"4"},{"B":"0","D":"ddjm5","I":"ddjm","M":"9.95","N":"5","O":"9.95","oddShowName":"5"},{"B":"0","D":"ddjm6","I":"ddjm","M":"9.95","N":"6","O":"9.95","oddShowName":"6"},{"B":"0","D":"ddjm7","I":"ddjm","M":"9.95","N":"7","O":"9.95","oddShowName":"7"},{"B":"0","D":"ddjm8","I":"ddjm","M":"9.95","N":"8","O":"9.95","oddShowName":"8"},{"B":"0","D":"ddjm9","I":"ddjm","M":"9.95","N":"9","O":"9.95","oddShowName":"9"},{"B":"0","D":"ddjm10","I":"ddjm","M":"9.95","N":"10","O":"9.95","oddShowName":"10"}],"I":"ddjm","N":"第九名"},{"C":[{"B":"0","D":"ddshm1","I":"ddshm","M":"9.95","N":"1","O":"9.95","oddShowName":"1"},{"B":"0","D":"ddshm2","I":"ddshm","M":"9.95","N":"2","O":"9.95","oddShowName":"2"},{"B":"0","D":"ddshm3","I":"ddshm","M":"9.95","N":"3","O":"9.95","oddShowName":"3"},{"B":"0","D":"ddshm4","I":"ddshm","M":"9.95","N":"4","O":"9.95","oddShowName":"4"},{"B":"0","D":"ddshm5","I":"ddshm","M":"9.95","N":"5","O":"9.95","oddShowName":"5"},{"B":"0","D":"ddshm6","I":"ddshm","M":"9.95","N":"6","O":"9.95","oddShowName":"6"},{"B":"0","D":"ddshm7","I":"ddshm","M":"9.95","N":"7","O":"9.95","oddShowName":"7"},{"B":"0","D":"ddshm8","I":"ddshm","M":"9.95","N":"8","O":"9.95","oddShowName":"8"},{"B":"0","D":"ddshm9","I":"ddshm","M":"9.95","N":"9","O":"9.95","oddShowName":"9"},{"B":"0","D":"ddshm10","I":"ddshm","M":"9.95","N":"10","O":"9.95","oddShowName":"10"}],"I":"ddshm","N":"第十名"}],"I":"dnm","N":"第1-10名"},{"C":[{"C":[{"B":"0","D":"gyh3","I":"gyh","M":"44","N":"3","O":"44","oddShowName":"3"},{"B":"0","D":"gyh4","I":"gyh","M":"44","N":"4","O":"44","oddShowName":"4"},{"B":"0","D":"gyh5","I":"gyh","M":"22","N":"5","O":"22","oddShowName":"5"},{"B":"0","D":"gyh6","I":"gyh","M":"22","N":"6","O":"22","oddShowName":"6"},{"B":"0","D":"gyh7","I":"gyh","M":"14","N":"7","O":"14","oddShowName":"7"},{"B":"0","D":"gyh8","I":"gyh","M":"14","N":"8","O":"14","oddShowName":"8"},{"B":"0","D":"gyh9","I":"gyh","M":"11","N":"9","O":"11","oddShowName":"9"},{"B":"0","D":"gyh10","I":"gyh","M":"11","N":"10","O":"11","oddShowName":"10"},{"B":"0","D":"gyh11","I":"gyh","M":"8.8","N":"11","O":"8.8","oddShowName":"11"},{"B":"0","D":"gyh12","I":"gyh","M":"11","N":"12","O":"11","oddShowName":"12"},{"B":"0","D":"gyh13","I":"gyh","M":"11","N":"13","O":"11","oddShowName":"13"},{"B":"0","D":"gyh14","I":"gyh","M":"14","N":"14","O":"14","oddShowName":"14"},{"B":"0","D":"gyh15","I":"gyh","M":"14","N":"15","O":"14","oddShowName":"15"},{"B":"0","D":"gyh16","I":"gyh","M":"22","N":"16","O":"22","oddShowName":"16"},{"B":"0","D":"gyh17","I":"gyh","M":"22","N":"17","O":"22","oddShowName":"17"},{"B":"0","D":"gyh18","I":"gyh","M":"44","N":"18","O":"44","oddShowName":"18"},{"B":"0","D":"gyh19","I":"gyh","M":"44","N":"19","O":"44","oddShowName":"19"},{"B":"0","D":"gyhd","I":"gyh","M":"2.19","N":"大","O":"2.19","oddShowName":"大"},{"B":"0","D":"gyhx","I":"gyh","M":"1.79","N":"小","O":"1.79","oddShowName":"小"},{"B":"0","D":"gyho","I":"gyh","M":"1.79","N":"单","O":"1.79","oddShowName":"单"},{"B":"0","D":"gyhs","I":"gyh","M":"2.19","N":"双","O":"2.19","oddShowName":"双"}],"I":"gyh","N":"冠亚和值"}],"I":"gyh1","N":"冠亚和值"},{"C":[{"C":[{"B":"0","D":"gyz1_2","I":"gyz","M":"42.5","N":"1_2","O":"42.5","oddShowName":"1_2"},{"B":"0","D":"gyz1_3","I":"gyz","M":"42.5","N":"1_3","O":"42.5","oddShowName":"1_3"},{"B":"0","D":"gyz1_4","I":"gyz","M":"42.5","N":"1_4","O":"42.5","oddShowName":"1_4"},{"B":"0","D":"gyz1_5","I":"gyz","M":"42.5","N":"1_5","O":"42.5","oddShowName":"1_5"},{"B":"0","D":"gyz1_6","I":"gyz","M":"42.5","N":"1_6","O":"42.5","oddShowName":"1_6"},{"B":"0","D":"gyz1_7","I":"gyz","M":"42.5","N":"1_7","O":"42.5","oddShowName":"1_7"},{"B":"0","D":"gyz1_8","I":"gyz","M":"42.5","N":"1_8","O":"42.5","oddShowName":"1_8"},{"B":"0","D":"gyz1_9","I":"gyz","M":"42.5","N":"1_9","O":"42.5","oddShowName":"1_9"},{"B":"0","D":"gyz1_10","I":"gyz","M":"42.5","N":"1_10","O":"42.5","oddShowName":"1_10"},{"B":"0","D":"gyz2_3","I":"gyz","M":"42.5","N":"2_3","O":"42.5","oddShowName":"2_3"},{"B":"0","D":"gyz2_4","I":"gyz","M":"42.5","N":"2_4","O":"42.5","oddShowName":"2_4"},{"B":"0","D":"gyz2_5","I":"gyz","M":"42.5","N":"2_5","O":"42.5","oddShowName":"2_5"},{"B":"0","D":"gyz2_6","I":"gyz","M":"42.5","N":"2_6","O":"42.5","oddShowName":"2_6"},{"B":"0","D":"gyz2_7","I":"gyz","M":"42.5","N":"2_7","O":"42.5","oddShowName":"2_7"},{"B":"0","D":"gyz2_8","I":"gyz","M":"42.5","N":"2_8","O":"42.5","oddShowName":"2_8"},{"B":"0","D":"gyz2_9","I":"gyz","M":"42.5","N":"2_9","O":"42.5","oddShowName":"2_9"},{"B":"0","D":"gyz2_10","I":"gyz","M":"42.5","N":"2_10","O":"42.5","oddShowName":"2_10"},{"B":"0","D":"gyz3_4","I":"gyz","M":"42.5","N":"3_4","O":"42.5","oddShowName":"3_4"},{"B":"0","D":"gyz3_5","I":"gyz","M":"42.5","N":"3_5","O":"42.5","oddShowName":"3_5"},{"B":"0","D":"gyz3_6","I":"gyz","M":"42.5","N":"3_6","O":"42.5","oddShowName":"3_6"},{"B":"0","D":"gyz3_7","I":"gyz","M":"42.5","N":"3_7","O":"42.5","oddShowName":"3_7"},{"B":"0","D":"gyz3_8","I":"gyz","M":"42.5","N":"3_8","O":"42.5","oddShowName":"3_8"},{"B":"0","D":"gyz3_9","I":"gyz","M":"42.5","N":"3_9","O":"42.5","oddShowName":"3_9"},{"B":"0","D":"gyz3_10","I":"gyz","M":"42.5","N":"3_10","O":"42.5","oddShowName":"3_10"},{"B":"0","D":"gyz4_5","I":"gyz","M":"42.5","N":"4_5","O":"42.5","oddShowName":"4_5"},{"B":"0","D":"gyz4_6","I":"gyz","M":"42.5","N":"4_6","O":"42.5","oddShowName":"4_6"},{"B":"0","D":"gyz4_7","I":"gyz","M":"42.5","N":"4_7","O":"42.5","oddShowName":"4_7"},{"B":"0","D":"gyz4_8","I":"gyz","M":"42.5","N":"4_8","O":"42.5","oddShowName":"4_8"},{"B":"0","D":"gyz4_9","I":"gyz","M":"42.5","N":"4_9","O":"42.5","oddShowName":"4_9"},{"B":"0","D":"gyz4_10","I":"gyz","M":"42.5","N":"4_10","O":"42.5","oddShowName":"4_10"},{"B":"0","D":"gyz5_6","I":"gyz","M":"42.5","N":"5_6","O":"42.5","oddShowName":"5_6"},{"B":"0","D":"gyz5_7","I":"gyz","M":"42.5","N":"5_7","O":"42.5","oddShowName":"5_7"},{"B":"0","D":"gyz5_8","I":"gyz","M":"42.5","N":"5_8","O":"42.5","oddShowName":"5_8"},{"B":"0","D":"gyz5_9","I":"gyz","M":"42.5","N":"5_9","O":"42.5","oddShowName":"5_9"},{"B":"0","D":"gyz5_10","I":"gyz","M":"42.5","N":"5_10","O":"42.5","oddShowName":"5_10"},{"B":"0","D":"gyz6_7","I":"gyz","M":"42.5","N":"6_7","O":"42.5","oddShowName":"6_7"},{"B":"0","D":"gyz6_8","I":"gyz","M":"42.5","N":"6_8","O":"42.5","oddShowName":"6_8"},{"B":"0","D":"gyz6_9","I":"gyz","M":"42.5","N":"6_9","O":"42.5","oddShowName":"6_9"},{"B":"0","D":"gyz6_10","I":"gyz","M":"42.5","N":"6_10","O":"42.5","oddShowName":"6_10"},{"B":"0","D":"gyz7_8","I":"gyz","M":"42.5","N":"7_8","O":"42.5","oddShowName":"7_8"},{"B":"0","D":"gyz7_9","I":"gyz","M":"42.5","N":"7_9","O":"42.5","oddShowName":"7_9"},{"B":"0","D":"gyz7_10","I":"gyz","M":"42.5","N":"7_10","O":"42.5","oddShowName":"7_10"},{"B":"0","D":"gyz8_9","I":"gyz","M":"42.5","N":"8_9","O":"42.5","oddShowName":"8_9"},{"B":"0","D":"gyz8_10","I":"gyz","M":"42.5","N":"8_10","O":"42.5","oddShowName":"8_10"},{"B":"0","D":"gyz9_10","I":"gyz","M":"42.5","N":"9_10","O":"42.5","oddShowName":"9_10"}],"I":"gyz","N":"冠亚组合"}],"I":"gyz1","N":"冠亚组合"}]';
	var CP={base:"",gameName:"极速赛车",gameCode:"FKSC",groupCode:"pk10",closeNotice:"true"};
    //今日未封盘期数
    var TodyUnClosePeriods= [{"CloseTime":"2025-06-03 15:21:25","NumberOfPeriod":"202506030737","PeriodId":70793526,"StartTime":"2025-06-03 15:20:15","Status":1},{"CloseTime":"2025-06-03 15:22:40","NumberOfPeriod":"202506030738","PeriodId":70793527,"StartTime":"2025-06-03 15:21:30","Status":0},{"CloseTime":"2025-06-03 15:23:55","NumberOfPeriod":"202506030739","PeriodId":70793528,"StartTime":"2025-06-03 15:22:45","Status":0},{"CloseTime":"2025-06-03 15:25:10","NumberOfPeriod":"202506030740","PeriodId":70793529,"StartTime":"2025-06-03 15:24:00","Status":0},{"CloseTime":"2025-06-03 15:26:25","NumberOfPeriod":"202506030741","PeriodId":70793530,"StartTime":"2025-06-03 15:25:15","Status":0},{"CloseTime":"2025-06-03 15:27:40","NumberOfPeriod":"202506030742","PeriodId":70793531,"StartTime":"2025-06-03 15:26:30","Status":0},{"CloseTime":"2025-06-03 15:28:55","NumberOfPeriod":"202506030743","PeriodId":70793532,"StartTime":"2025-06-03 15:27:45","Status":0},{"CloseTime":"2025-06-03 15:30:10","NumberOfPeriod":"202506030744","PeriodId":70793533,"StartTime":"2025-06-03 15:29:00","Status":0},{"CloseTime":"2025-06-03 15:31:25","NumberOfPeriod":"202506030745","PeriodId":70793534,"StartTime":"2025-06-03 15:30:15","Status":0},{"CloseTime":"2025-06-03 15:32:40","NumberOfPeriod":"202506030746","PeriodId":70793535,"StartTime":"2025-06-03 15:31:30","Status":0},{"CloseTime":"2025-06-03 15:33:55","NumberOfPeriod":"202506030747","PeriodId":70793536,"StartTime":"2025-06-03 15:32:45","Status":0},{"CloseTime":"2025-06-03 15:35:10","NumberOfPeriod":"202506030748","PeriodId":70793537,"StartTime":"2025-06-03 15:34:00","Status":0},{"CloseTime":"2025-06-03 15:36:25","NumberOfPeriod":"202506030749","PeriodId":70793538,"StartTime":"2025-06-03 15:35:15","Status":0},{"CloseTime":"2025-06-03 15:37:40","NumberOfPeriod":"202506030750","PeriodId":70793539,"StartTime":"2025-06-03 15:36:30","Status":0},{"CloseTime":"2025-06-03 15:38:55","NumberOfPeriod":"202506030751","PeriodId":70793540,"StartTime":"2025-06-03 15:37:45","Status":0},{"CloseTime":"2025-06-03 15:40:10","NumberOfPeriod":"202506030752","PeriodId":70793541,"StartTime":"2025-06-03 15:39:00","Status":0},{"CloseTime":"2025-06-03 15:41:25","NumberOfPeriod":"202506030753","PeriodId":70793542,"StartTime":"2025-06-03 15:40:15","Status":0},{"CloseTime":"2025-06-03 15:42:40","NumberOfPeriod":"202506030754","PeriodId":70793543,"StartTime":"2025-06-03 15:41:30","Status":0},{"CloseTime":"2025-06-03 15:43:55","NumberOfPeriod":"202506030755","PeriodId":70793544,"StartTime":"2025-06-03 15:42:45","Status":0},{"CloseTime":"2025-06-03 15:45:10","NumberOfPeriod":"202506030756","PeriodId":70793545,"StartTime":"2025-06-03 15:44:00","Status":0},{"CloseTime":"2025-06-03 15:46:25","NumberOfPeriod":"202506030757","PeriodId":70793546,"StartTime":"2025-06-03 15:45:15","Status":0},{"CloseTime":"2025-06-03 15:47:40","NumberOfPeriod":"202506030758","PeriodId":70793547,"StartTime":"2025-06-03 15:46:30","Status":0},{"CloseTime":"2025-06-03 15:48:55","NumberOfPeriod":"202506030759","PeriodId":70793548,"StartTime":"2025-06-03 15:47:45","Status":0},{"CloseTime":"2025-06-03 15:50:10","NumberOfPeriod":"202506030760","PeriodId":70793549,"StartTime":"2025-06-03 15:49:00","Status":0},{"CloseTime":"2025-06-03 15:51:25","NumberOfPeriod":"202506030761","PeriodId":70793550,"StartTime":"2025-06-03 15:50:15","Status":0},{"CloseTime":"2025-06-03 15:52:40","NumberOfPeriod":"202506030762","PeriodId":70793551,"StartTime":"2025-06-03 15:51:30","Status":0},{"CloseTime":"2025-06-03 15:53:55","NumberOfPeriod":"202506030763","PeriodId":70793552,"StartTime":"2025-06-03 15:52:45","Status":0},{"CloseTime":"2025-06-03 15:55:10","NumberOfPeriod":"202506030764","PeriodId":70793553,"StartTime":"2025-06-03 15:54:00","Status":0},{"CloseTime":"2025-06-03 15:56:25","NumberOfPeriod":"202506030765","PeriodId":70793554,"StartTime":"2025-06-03 15:55:15","Status":0},{"CloseTime":"2025-06-03 15:57:40","NumberOfPeriod":"202506030766","PeriodId":70793555,"StartTime":"2025-06-03 15:56:30","Status":0},{"CloseTime":"2025-06-03 15:58:55","NumberOfPeriod":"202506030767","PeriodId":70793556,"StartTime":"2025-06-03 15:57:45","Status":0},{"CloseTime":"2025-06-03 16:00:10","NumberOfPeriod":"202506030768","PeriodId":70793557,"StartTime":"2025-06-03 15:59:00","Status":0},{"CloseTime":"2025-06-03 16:01:25","NumberOfPeriod":"202506030769","PeriodId":70793558,"StartTime":"2025-06-03 16:00:15","Status":0},{"CloseTime":"2025-06-03 16:02:40","NumberOfPeriod":"202506030770","PeriodId":70793559,"StartTime":"2025-06-03 16:01:30","Status":0},{"CloseTime":"2025-06-03 16:03:55","NumberOfPeriod":"202506030771","PeriodId":70793560,"StartTime":"2025-06-03 16:02:45","Status":0},{"CloseTime":"2025-06-03 16:05:10","NumberOfPeriod":"202506030772","PeriodId":70793561,"StartTime":"2025-06-03 16:04:00","Status":0},{"CloseTime":"2025-06-03 16:06:25","NumberOfPeriod":"202506030773","PeriodId":70793562,"StartTime":"2025-06-03 16:05:15","Status":0},{"CloseTime":"2025-06-03 16:07:40","NumberOfPeriod":"202506030774","PeriodId":70793563,"StartTime":"2025-06-03 16:06:30","Status":0},{"CloseTime":"2025-06-03 16:08:55","NumberOfPeriod":"202506030775","PeriodId":70793564,"StartTime":"2025-06-03 16:07:45","Status":0},{"CloseTime":"2025-06-03 16:10:10","NumberOfPeriod":"202506030776","PeriodId":70793565,"StartTime":"2025-06-03 16:09:00","Status":0},{"CloseTime":"2025-06-03 16:11:25","NumberOfPeriod":"202506030777","PeriodId":70793566,"StartTime":"2025-06-03 16:10:15","Status":0},{"CloseTime":"2025-06-03 16:12:40","NumberOfPeriod":"202506030778","PeriodId":70793567,"StartTime":"2025-06-03 16:11:30","Status":0},{"CloseTime":"2025-06-03 16:13:55","NumberOfPeriod":"202506030779","PeriodId":70793568,"StartTime":"2025-06-03 16:12:45","Status":0},{"CloseTime":"2025-06-03 16:15:10","NumberOfPeriod":"202506030780","PeriodId":70793569,"StartTime":"2025-06-03 16:14:00","Status":0},{"CloseTime":"2025-06-03 16:16:25","NumberOfPeriod":"202506030781","PeriodId":70793570,"StartTime":"2025-06-03 16:15:15","Status":0},{"CloseTime":"2025-06-03 16:17:40","NumberOfPeriod":"202506030782","PeriodId":70793571,"StartTime":"2025-06-03 16:16:30","Status":0},{"CloseTime":"2025-06-03 16:18:55","NumberOfPeriod":"202506030783","PeriodId":70793572,"StartTime":"2025-06-03 16:17:45","Status":0},{"CloseTime":"2025-06-03 16:20:10","NumberOfPeriod":"202506030784","PeriodId":70793573,"StartTime":"2025-06-03 16:19:00","Status":0},{"CloseTime":"2025-06-03 16:21:25","NumberOfPeriod":"202506030785","PeriodId":70793574,"StartTime":"2025-06-03 16:20:15","Status":0},{"CloseTime":"2025-06-03 16:22:40","NumberOfPeriod":"202506030786","PeriodId":70793575,"StartTime":"2025-06-03 16:21:30","Status":0},{"CloseTime":"2025-06-03 16:23:55","NumberOfPeriod":"202506030787","PeriodId":70793576,"StartTime":"2025-06-03 16:22:45","Status":0},{"CloseTime":"2025-06-03 16:25:10","NumberOfPeriod":"202506030788","PeriodId":70793577,"StartTime":"2025-06-03 16:24:00","Status":0},{"CloseTime":"2025-06-03 16:26:25","NumberOfPeriod":"202506030789","PeriodId":70793578,"StartTime":"2025-06-03 16:25:15","Status":0},{"CloseTime":"2025-06-03 16:27:40","NumberOfPeriod":"202506030790","PeriodId":70793579,"StartTime":"2025-06-03 16:26:30","Status":0},{"CloseTime":"2025-06-03 16:28:55","NumberOfPeriod":"202506030791","PeriodId":70793580,"StartTime":"2025-06-03 16:27:45","Status":0},{"CloseTime":"2025-06-03 16:30:10","NumberOfPeriod":"202506030792","PeriodId":70793581,"StartTime":"2025-06-03 16:29:00","Status":0},{"CloseTime":"2025-06-03 16:31:25","NumberOfPeriod":"202506030793","PeriodId":70793582,"StartTime":"2025-06-03 16:30:15","Status":0},{"CloseTime":"2025-06-03 16:32:40","NumberOfPeriod":"202506030794","PeriodId":70793583,"StartTime":"2025-06-03 16:31:30","Status":0},{"CloseTime":"2025-06-03 16:33:55","NumberOfPeriod":"202506030795","PeriodId":70793584,"StartTime":"2025-06-03 16:32:45","Status":0},{"CloseTime":"2025-06-03 16:35:10","NumberOfPeriod":"202506030796","PeriodId":70793585,"StartTime":"2025-06-03 16:34:00","Status":0},{"CloseTime":"2025-06-03 16:36:25","NumberOfPeriod":"202506030797","PeriodId":70793586,"StartTime":"2025-06-03 16:35:15","Status":0},{"CloseTime":"2025-06-03 16:37:40","NumberOfPeriod":"202506030798","PeriodId":70793587,"StartTime":"2025-06-03 16:36:30","Status":0},{"CloseTime":"2025-06-03 16:38:55","NumberOfPeriod":"202506030799","PeriodId":70793588,"StartTime":"2025-06-03 16:37:45","Status":0},{"CloseTime":"2025-06-03 16:40:10","NumberOfPeriod":"202506030800","PeriodId":70793589,"StartTime":"2025-06-03 16:39:00","Status":0},{"CloseTime":"2025-06-03 16:41:25","NumberOfPeriod":"202506030801","PeriodId":70793590,"StartTime":"2025-06-03 16:40:15","Status":0},{"CloseTime":"2025-06-03 16:42:40","NumberOfPeriod":"202506030802","PeriodId":70793591,"StartTime":"2025-06-03 16:41:30","Status":0},{"CloseTime":"2025-06-03 16:43:55","NumberOfPeriod":"202506030803","PeriodId":70793592,"StartTime":"2025-06-03 16:42:45","Status":0},{"CloseTime":"2025-06-03 16:45:10","NumberOfPeriod":"202506030804","PeriodId":70793593,"StartTime":"2025-06-03 16:44:00","Status":0},{"CloseTime":"2025-06-03 16:46:25","NumberOfPeriod":"202506030805","PeriodId":70793594,"StartTime":"2025-06-03 16:45:15","Status":0},{"CloseTime":"2025-06-03 16:47:40","NumberOfPeriod":"202506030806","PeriodId":70793595,"StartTime":"2025-06-03 16:46:30","Status":0},{"CloseTime":"2025-06-03 16:48:55","NumberOfPeriod":"202506030807","PeriodId":70793596,"StartTime":"2025-06-03 16:47:45","Status":0},{"CloseTime":"2025-06-03 16:50:10","NumberOfPeriod":"202506030808","PeriodId":70793597,"StartTime":"2025-06-03 16:49:00","Status":0},{"CloseTime":"2025-06-03 16:51:25","NumberOfPeriod":"202506030809","PeriodId":70793598,"StartTime":"2025-06-03 16:50:15","Status":0},{"CloseTime":"2025-06-03 16:52:40","NumberOfPeriod":"202506030810","PeriodId":70793599,"StartTime":"2025-06-03 16:51:30","Status":0},{"CloseTime":"2025-06-03 16:53:55","NumberOfPeriod":"202506030811","PeriodId":70793600,"StartTime":"2025-06-03 16:52:45","Status":0},{"CloseTime":"2025-06-03 16:55:10","NumberOfPeriod":"202506030812","PeriodId":70793601,"StartTime":"2025-06-03 16:54:00","Status":0},{"CloseTime":"2025-06-03 16:56:25","NumberOfPeriod":"202506030813","PeriodId":70793602,"StartTime":"2025-06-03 16:55:15","Status":0},{"CloseTime":"2025-06-03 16:57:40","NumberOfPeriod":"202506030814","PeriodId":70793603,"StartTime":"2025-06-03 16:56:30","Status":0},{"CloseTime":"2025-06-03 16:58:55","NumberOfPeriod":"202506030815","PeriodId":70793604,"StartTime":"2025-06-03 16:57:45","Status":0},{"CloseTime":"2025-06-03 17:00:10","NumberOfPeriod":"202506030816","PeriodId":70793605,"StartTime":"2025-06-03 16:59:00","Status":0},{"CloseTime":"2025-06-03 17:01:25","NumberOfPeriod":"202506030817","PeriodId":70793606,"StartTime":"2025-06-03 17:00:15","Status":0},{"CloseTime":"2025-06-03 17:02:40","NumberOfPeriod":"202506030818","PeriodId":70793607,"StartTime":"2025-06-03 17:01:30","Status":0},{"CloseTime":"2025-06-03 17:03:55","NumberOfPeriod":"202506030819","PeriodId":70793608,"StartTime":"2025-06-03 17:02:45","Status":0},{"CloseTime":"2025-06-03 17:05:10","NumberOfPeriod":"202506030820","PeriodId":70793609,"StartTime":"2025-06-03 17:04:00","Status":0},{"CloseTime":"2025-06-03 17:06:25","NumberOfPeriod":"202506030821","PeriodId":70793610,"StartTime":"2025-06-03 17:05:15","Status":0},{"CloseTime":"2025-06-03 17:07:40","NumberOfPeriod":"202506030822","PeriodId":70793611,"StartTime":"2025-06-03 17:06:30","Status":0},{"CloseTime":"2025-06-03 17:08:55","NumberOfPeriod":"202506030823","PeriodId":70793612,"StartTime":"2025-06-03 17:07:45","Status":0},{"CloseTime":"2025-06-03 17:10:10","NumberOfPeriod":"202506030824","PeriodId":70793613,"StartTime":"2025-06-03 17:09:00","Status":0},{"CloseTime":"2025-06-03 17:11:25","NumberOfPeriod":"202506030825","PeriodId":70793614,"StartTime":"2025-06-03 17:10:15","Status":0},{"CloseTime":"2025-06-03 17:12:40","NumberOfPeriod":"202506030826","PeriodId":70793615,"StartTime":"2025-06-03 17:11:30","Status":0},{"CloseTime":"2025-06-03 17:13:55","NumberOfPeriod":"202506030827","PeriodId":70793616,"StartTime":"2025-06-03 17:12:45","Status":0},{"CloseTime":"2025-06-03 17:15:10","NumberOfPeriod":"202506030828","PeriodId":70793617,"StartTime":"2025-06-03 17:14:00","Status":0},{"CloseTime":"2025-06-03 17:16:25","NumberOfPeriod":"202506030829","PeriodId":70793618,"StartTime":"2025-06-03 17:15:15","Status":0},{"CloseTime":"2025-06-03 17:17:40","NumberOfPeriod":"202506030830","PeriodId":70793619,"StartTime":"2025-06-03 17:16:30","Status":0},{"CloseTime":"2025-06-03 17:18:55","NumberOfPeriod":"202506030831","PeriodId":70793620,"StartTime":"2025-06-03 17:17:45","Status":0},{"CloseTime":"2025-06-03 17:20:10","NumberOfPeriod":"202506030832","PeriodId":70793621,"StartTime":"2025-06-03 17:19:00","Status":0},{"CloseTime":"2025-06-03 17:21:25","NumberOfPeriod":"202506030833","PeriodId":70793622,"StartTime":"2025-06-03 17:20:15","Status":0},{"CloseTime":"2025-06-03 17:22:40","NumberOfPeriod":"202506030834","PeriodId":70793623,"StartTime":"2025-06-03 17:21:30","Status":0},{"CloseTime":"2025-06-03 17:23:55","NumberOfPeriod":"202506030835","PeriodId":70793624,"StartTime":"2025-06-03 17:22:45","Status":0},{"CloseTime":"2025-06-03 17:25:10","NumberOfPeriod":"202506030836","PeriodId":70793625,"StartTime":"2025-06-03 17:24:00","Status":0},{"CloseTime":"2025-06-03 17:26:25","NumberOfPeriod":"202506030837","PeriodId":70793626,"StartTime":"2025-06-03 17:25:15","Status":0},{"CloseTime":"2025-06-03 17:27:40","NumberOfPeriod":"202506030838","PeriodId":70793627,"StartTime":"2025-06-03 17:26:30","Status":0},{"CloseTime":"2025-06-03 17:28:55","NumberOfPeriod":"202506030839","PeriodId":70793628,"StartTime":"2025-06-03 17:27:45","Status":0},{"CloseTime":"2025-06-03 17:30:10","NumberOfPeriod":"202506030840","PeriodId":70793629,"StartTime":"2025-06-03 17:29:00","Status":0},{"CloseTime":"2025-06-03 17:31:25","NumberOfPeriod":"202506030841","PeriodId":70793630,"StartTime":"2025-06-03 17:30:15","Status":0},{"CloseTime":"2025-06-03 17:32:40","NumberOfPeriod":"202506030842","PeriodId":70793631,"StartTime":"2025-06-03 17:31:30","Status":0},{"CloseTime":"2025-06-03 17:33:55","NumberOfPeriod":"202506030843","PeriodId":70793632,"StartTime":"2025-06-03 17:32:45","Status":0},{"CloseTime":"2025-06-03 17:35:10","NumberOfPeriod":"202506030844","PeriodId":70793633,"StartTime":"2025-06-03 17:34:00","Status":0},{"CloseTime":"2025-06-03 17:36:25","NumberOfPeriod":"202506030845","PeriodId":70793634,"StartTime":"2025-06-03 17:35:15","Status":0},{"CloseTime":"2025-06-03 17:37:40","NumberOfPeriod":"202506030846","PeriodId":70793635,"StartTime":"2025-06-03 17:36:30","Status":0}];
</script>
<script src="/common/js/jquery-1.12.4.min.js"></script>
<script src="/common/js/jquery.md5.js"></script>
<script src="/common/js/knockout-3.4.2.js"></script>
<script src="/common/js/artDialog/dialog-plus.js"></script>
<script src="/common/lot/js/credit/utils.js?v1"></script>
<script src="/common/js/flipclock/flipclock.min.js"></script>
<script src="/common/lot/js/credit/jquery.waybill.js?v=1"></script>
<script src="/common/lot/js/credit/game.common.js?v=3"></script>

<script src="/common/lot/js/credit/pk10.js?2.21"></script>
<script src="/common/lot/js/credit/core.js?d1w18"></script>
<script src="/common/lot/js/notice.js?5"></script>
 <script type="application/javascript">
     //noticeFn.message(false,true,3);
     $("#menuSelect .game-tab ul li").click(function () {
         $(this).addClass("active").siblings().removeClass("active");
         var _index = $(this).index();
         $("#menuSelect .tab-main ul li").eq(_index).addClass("active").siblings().removeClass("active");
     }) ;
 </script>
    </body>
</html>
<link href="/common/member/floatFrame/css/floatFrame.css?v=1.0.3" rel="stylesheet">
<div id="main_float_frame_id">
</div>
<script src="/common/member/floatFrame/js/jquery.SuperSlide.2.1.1.js"></script>
<script type="text/javascript">
$(function(){
	jQuery("#main_float_frame_id .floatFreameSlideBox").slide({mainCell:".fdbd.lunbo ul",interTime:"6000",autoPlay:true});
})
function clearSlideBox(fid){
	$('#main_float_frame_id .float_frame_close_id_'+fid).fadeOut();
}

function closeFloatFrame(fid){
	$('#main_float_frame_id .float_frame_close_id_'+fid).hide();
}
$('#main_float_frame_id .show-hide-over-img').hover(function(){
	$('.over-img-id-'+$(this).data('id')).show();
},function(){
	$('.over-img-id-'+$(this).data('id')).hide();
})
</script><script type="text/html" id="APPEND-TPL">
    <div class="popup">
        <div class="popup-body">
            <div class="bet-info">
                <div class="popup-form clear">
                    <div class="fleft">
                        自动跟单计划：
                        <span>正投金额<input type="text" data-bind="textinput:followMoney"/></span>
                        <span>反投金额<input type="text" data-bind="textinput:unFollowMoney"/></span>
                        <span>跟单期数<input type="text" data-bind="textinput:followNum"/></span>
                    </div>
                    <div class="fright" style="padding-left:0">
                    </div>
                </div>
                <div class="popup-form clear">
                    <div class="fleft">
                        <span>跟单总期数:<label class="fc_yellow" data-bind="text:totalFollowNum()">&nbsp;</label>期</span>
                        <span>跟单总金额:<label class="fc_yellow" data-bind="text:totalFollowMoney()">&nbsp;</label>元</span>
                    </div>
                    <div class="fright"><a href="#" class="btn generate" data-bind="click:createAppendNumberPlan"
                                           title="生成跟单计划"></a></div>
                </div>
                <div class="table-box" style="height:auto;width:auto;">
                    <table>
                        <thead>
                        <tr>
                            <th>期号</th>
                            <th width="200">正投</th>
                            <th width="200">反投</th>
                            <th width="160">截止时间</th>
                        </tr>
                        </thead>
                    </table>
                </div>
                <div class="table-box" style="overflow:auto;width:auto">
                    <table>
                        <tbody data-bind="foreach:planGenDanList">
                        <tr>
                            <td><span class="checkbox"
                                      data-bind="text:NumberOfPeriod,css:followMoney()>0||unFollowMoney()>0?'checkbox-checked':''">&nbsp;</span>
                            </td>
                            <td width="200">&yen;<input type="text" data-bind="textinput:followMoney"/></td>
                            <td width="200">&yen;<input type="text" data-bind="textinput:unFollowMoney"/></td>
                            <td width="160" data-bind="text:CloseTime">&nbsp;</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="popup-foot" style="padding-top:10px">
            <div class="clear">
                <a href="#" class="btn Empty" data-bind="click:empty" title="清空追号"></a>
                <a href="#" class="btn bet" data-bind="click:subAppendAction"></a>
                <a href="#" class="btn Cancel" data-bind="click:cancel" title="取消"></a>
            </div>
        </div>
    </div>
</script>

<script type="text/html" id="APPEND-PRE-DETAIL">
    <div class="popup" id="append_zhuihao">
        <div class="popup-body">
            <div class="bet-info">
                <div class="table-box" style="height:auto">
                    <div>
                        <table>
                            <thead>
                            <tr>
                                <th width="200">期号</th>
                                <th width="120">球号</th>
                                <th width="200">预测号码</th>
                                <th width="100">中奖号码</th>
                                <th width="120">结果</th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                    <div style="height:154px;overflow:auto">
                        <table>
                            <tbody data-bind="foreach:preHisList">
                            <tr>
                                <td width="200">
                                    <span data-bind="text:qiHao">&nbsp;</span>
                                </td>
                                <td width="120"><span data-bind="text:playName"></span></td>
                                <td width="200"><span data-bind="text:haoMa"></span></td>
                                <td width="100"><span data-bind="text:status==1?'-':winHaoMa"></span></td>
                                <td width="120" data-bind="text:['未开奖','已中奖','未中奖'][status-1],css:['color-doing','color-complete','color-cancel'][status-1]"></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="popup-foot" style="padding-top:10px;">
        <div>
            <a href="javascript:;" class="btn deter" style="float: none" title="确定" data-bind="click:cancel"></a>
        </div>
    </div>
</script><link href="/common/lot/waybill/css/skin1/waybill.css?v=1.1" rel="stylesheet">

<div class="waybill-btn">

    <!-- <p><img src="/common/lot/waybill/image/luzhi_un.png" alt=""></p> -->

    <div class="pop-box" id="waybill-dialog-id">

        <div class="codePopUp">

            <div class="waybillShowBtn">彩票路单</div>

            <div class="popInnderBox">

                    <div class="selectVel">

                        <select id="waybill-ball-num">

                                <option value="0" selected>冠军</option>

                                <option value="1">亚军</option>

                                <option value="2">第三名</option>

                                <option value="3">第四名</option>

                                <option value="4">第五名</option>

                                <option value="5">第六名</option>

                                <option value="6">第七名</option>

                                <option value="7">第八名</option>

                                <option value="8">第九名</option>

                                <option value="9">第十名</option>

                                <option value="longhudou">龙虎斗</option>

                        </select>

                    </div>



                <div class="tabBox" id="waybill-site">

                            <div class="tab-btn on" data-type="haoma">球号</div>

                            <div class="tab-btn" data-type="danshuang">单双</div>

                            <div class="tab-btn" data-type="daxiao">大小</div>

                        <div class="clearPop"></div>

                    </div>

                    <div class="tabBox" id="waybill-lhd-site" style="display: none">

                            <div class="tab-btn-lh on" data-type="0V9">1V10</div>

                            <div class="tab-btn-lh" data-type="1V8">2V9</div>

                            <div class="tab-btn-lh" data-type="2V7">3V8</div>

                            <div class="tab-btn-lh" data-type="3V6">4V7</div>

                            <div class="tab-btn-lh" data-type="4V5">5V6</div>

                        <div class="clearPop"></div>

                    </div>

                    <div class="popNumber">

                        <div class="popGridBox free dragscroll">

                            <div class="gridBanner" id="waybill-tb-id">



                            </div>

                        </div>

                    </div>

                </div>

            </div>

        </div>

    </div>

</div>

<script src="/common/lot/waybill/js/dragscroll.js"></script>

<script src="/common/lot/waybill/js/waybill.js?v=3"></script>



<link rel="stylesheet" href="/common/lot/clBet/css/skin1/cl.css?v=1.3">

<div class="loogCont" id="CLBet">

    <div class="lengthLoog">

        <div class="showLoogBtn"><span>长龙投注</span></div>

        <script>

            $('.showLoogBtn').click(function () {

                $('.lengthLoog').toggleClass('showLength')

            })

        </script>

        <div class="head">

            <div class="clpx" data-bind="css:{'active':!isBet()},click:change" style="border-right:1px solid #b9c2cb;">长龙排行榜</div>

            <div class="active cltz" data-bind="css:{'active':isBet()},click:change" >长龙投注</div>

        </div>

        <div class="content">

            <div class="ranking" data-bind="css:{'active':!isBet()}">

                <table data-bind="foreach:clHisList()">

                    <tr>

                        <th data-bind="text:$data.playName + ' - ' +$data.numName"> - 冠亚小</th>

                        <td data-bind="text:$data.cl +'期'">7期</td>

                    </tr>

                </table>

            </div>

            <div class="cl_bet_ta active" data-bind="css:{'active':isBet()}">

                <div class="cl_bet">

                    <div class="cl_stage">

                        <span>长龙连开期数：</span>

                        <select data-bind="value:clNum,event:{change:changClNum}">

                            <option value="4">4</option>

                            <option value="6">6</option>

                            <option value="8">8</option>

                            <option value="10">10</option>

                            <option value="12">12</option>

                        </select>

                    </div>

                    <div class="cl_list">

                        <div class="more_box" data-bind="visible:clList().length>0,foreach:clList()">

                            <div class="cl_item" data-bind="css:$data.lotCode+$data.playCode+$data.period.fnumberofperiod">

                                <div class="def">

                                    <div class="name">

                                        <div data-bind="text:$data.lotName">广东11选5</div>

                                        <div>

                                            第<span class="cl_period" data-bind="text:$data.period.fnumberofperiod">20092334</span>期

                                        </div>

                                    </div>

                                    <div class="plays bet_row">

                                        <div>

                                            <div data-bind="text:$data.playName"> 冠、亚军和</div>

                                            <div class="count_down time" data-bind="html:$parent.lastTimeHandle($data.period,$data.playCode,$data.lotCode,event)">

                                                17:36

                                            </div>

                                        </div>

                                        <div class="flex" data-bind="foreach:{data:$data.oddsArr,as:'odd'}">

                                            <div class="play bet_item no_select" data-bind="css:{'c_red':$index()==0,'c_black':$index()==1,'c_green':$index()==2},click:$root.chooseItem

                                            ,attr:{'data-code':$data.d,'data-odd':$data.odd,'data-numname':$data.N,'data-playcode':$parent.playCode,'data-playname':$parent.playName

                                            ,'data-period':$parent.period.fnumberofperiod ,'data-lotname':$parent.lotName,'data-lotcode':$parent.lotCode}">

                                                <div data-bind="text:$data.N">总和单</div>

                                                <div data-bind="text:$data.odd">1.900</div>

                                            </div>

                                        </div>

                                    </div>

                                    <div class="step">

                                        <div class="active" data-bind="style:{width:$parent.randomStep()}" >

                                            <span>PK</span>

                                        </div>

                                    </div>

                                    <div class="other">

                                        <div class="fl">

                                            <span class="play" data-bind="text:$data.numName">大</span>连开<span class="num" data-bind="text:$data.cl">8</span>期

                                        </div>

                                    </div>

                                </div>

                            </div>

                        </div>

                        <div class="more_box" data-bind="visible:clList().length==0">

                            <!-- 没有数据时 -->

                            <div class="cl_item">

                                <div class="def clearFlex">

                                    <div>当前没有符合长龙投注的游戏</div>

                                </div>

                            </div>

                        </div>

                    </div>

                    <div class="bet_box">

                        <div class="btn-wrap">

                            <input type="text" class="amount" data-bind="value:amount" maxlength="9">

                            <input type="submit" class="btn btn-green" value="" title="确认" data-bind="click:bet"/>

                            <input type="reset" class="btn" value="" title="取消"/>

                        </div>

                    </div>

                </div>

            </div>

        </div>

    </div>

</div>

<script src="/common/lot/clBet/js/cl.js?v=2.3"></script><!--公告-->
<script src="/common/member/notice/js/jquery.cookie.js"></script>
    <script type="application/javascript">
    var popShowTime = '45';
        noticeFn.message(true, true, 3);
    </script>
<script type="text/html" id="tpl-message">
    <div>
        <div>
            <div class="side_left" data-bind="foreach:dialogNotice">
                <div class="side_item">
                    <a href="javascript:;"
                       data-bind="click:$parent.active.bind($data,$index()),css:$parent.activeIndex() == $index() ? 'active' : ''">
                        <span data-bind="text:title"></span>
                    </a>
                </div>
            </div>
            <div class="notice_main" data-bind="with:activeMessage">
                <div class="notice_title"><h1 data-bind="text:title"></h1></div>
                <div class="notice_text">
                    <div class="fleft notice_item" style="text-align:left;word-break:break-all;"
                         data-bind="html:content"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</script>
<script>

   function openChatWindow(url,name,iWidth,iHeight){

 	  var url,name,iWidth,iHeight;

  	  var iTop = (window.screen.availHeight-30-iHeight)/2;

  	  var iLeft = (window.screen.availWidth-10-iWidth)/2;

  	  window.open(url,name,'height='+iHeight+',,innerHeight='+iHeight+',width='+iWidth+',innerWidth='+iWidth+',top='+iTop+',left='+iLeft+',toolbar=no,menubar=no,scrollbars=auto,resizeable=no,location=no,status=no');

   }

</script>

	<a href="/goChat.do?isPc=true" target="_blank" class="open-chat close-chat"></a>

	<style>

		#chatroomIframe{

			display: none;

			position: fixed;

			right: 0;

			top: 0;

			max-width:100% !important;

			max-height:100% !important;

			height: 100% !important;

			z-index: 100;

			min-width:320px;

			box-shadow: 0 0 18px 1px #c5c5c5;

		}

		.close-chat{

			position: fixed;

			right: 0;

			top: 0;

			width: 50px;

			height: 50px;

			display: block;

			z-index: 101;

		}

		.open-chat{

			width: 50px;

			height: 150px;

			background: url("/common/lot/images/chat_float_red.png") no-repeat;

			background-size: contain;

			position: fixed;

			right: 10px;

			top: 63%;

			margin-top: -75px;

			z-index: 10;

			display: block;

		}

	</style>

	<script type="text/javascript" src="/common/js/iframeResizer/iframeResizer.min.js"></script>

	<script>

		if (!String.prototype.startsWith) {

		  String.prototype.startsWith = function(searchString, position) {

		    position = position || 0;

		    return this.indexOf(searchString, position) === position;

		  };

		}

		

		var iFrameResizer = iFrameResize(

			{

				log:false, 

				widthCalculationMethod:'rightMostElement', 

				heightCalculationMethod:'lowestElement',

				autoResize:false,

				messageCallback:function(event) {

					if(event.message == 'close') {

						//聊天室关闭event

						$('.showChatroomButton').show();

					}else if(event.message.startsWith('changeSize=')) {

						//修改大小

						var params = JSON.parse(event.message.substring(11));

						$('#chatroomIframe').css('width', params.width)

							.css('height', params.height);

					}

				}

			}, 

			'#chatroomIframe')

	</script>

</body>
</html>
