#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import urllib3
import json
import time
from typing import Dict, List, Tuple, Optional, Any

from utils.logger import Logger
from utils.helpers import retry_operation, safe_json_loads

# 禁用未验证HTTPS请求的警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class ApiClient:
    """API客户端类，统一管理所有API请求"""
    
    def __init__(self, session, base_url):
        """
        初始化API客户端
        
        Args:
            session: 请求会话对象
            base_url: API基础URL
        """
        self.session = session
        self.base_url = base_url
        self.default_headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36",
            "Referer": f"{base_url}/"
        }
        self.timeout = 10  # 默认超时时间
        self.logger = Logger("ApiClient")  # 创建日志记录器
    
    def _make_request(self, method, endpoint, params=None, data=None, json_data=None, headers=None, timeout=None, verify=False):
        """
        发送HTTP请求的内部方法
        
        Args:
            method: 请求方法 (GET, POST, etc.)
            endpoint: API端点
            params: URL参数
            data: 表单数据
            json_data: JSON数据
            headers: 请求头
            timeout: 超时时间
            verify: 是否验证SSL证书
            
        Returns:
            请求响应对象
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        request_headers = self.default_headers.copy()
        if headers:
            request_headers.update(headers)
        
        self.logger.debug(f"发送 {method} 请求到: {url}")
        
        try:
            response = self.session.request(
                method=method,
                url=url,
                params=params,
                data=data,
                json=json_data,
                headers=request_headers,
                timeout=timeout or self.timeout,
                verify=verify
            )
            
            self.logger.debug(f"请求成功，状态码: {response.status_code}")
            return response
        except Exception as e:
            self.logger.error(f"请求失败: {str(e)}")
            raise
    
    def get(self, endpoint, params=None, headers=None, timeout=None, verify=False):
        """
        发送GET请求
        
        Args:
            endpoint: API端点
            params: URL参数
            headers: 请求头
            timeout: 超时时间
            verify: 是否验证SSL证书
            
        Returns:
            请求响应对象
        """
        return self._make_request("GET", endpoint, params=params, headers=headers, timeout=timeout, verify=verify)
    
    def post(self, endpoint, data=None, json_data=None, headers=None, timeout=None, verify=False):
        """
        发送POST请求
        
        Args:
            endpoint: API端点
            data: 表单数据
            json_data: JSON数据
            headers: 请求头
            timeout: 超时时间
            verify: 是否验证SSL证书
            
        Returns:
            请求响应对象
        """
        return self._make_request("POST", endpoint, data=data, json_data=json_data, headers=headers, timeout=timeout, verify=verify)
    
    def retry_get(self, endpoint, params=None, headers=None, max_retries=3, delay=1, backoff=2):
        """
        带重试机制的GET请求
        
        Args:
            endpoint: API端点
            params: URL参数
            headers: 请求头
            max_retries: 最大重试次数
            delay: 初始延迟时间（秒）
            backoff: 退避因子
            
        Returns:
            请求响应对象或None（如果所有重试都失败）
        """
        operation = lambda: self.get(endpoint, params=params, headers=headers)
        return retry_operation(operation, max_retries=max_retries, delay=delay, backoff=backoff, exceptions=(requests.RequestException,))
    
    def retry_post(self, endpoint, data=None, json_data=None, headers=None, max_retries=3, delay=1, backoff=2):
        """
        带重试机制的POST请求
        
        Args:
            endpoint: API端点
            data: 表单数据
            json_data: JSON数据
            headers: 请求头
            max_retries: 最大重试次数
            delay: 初始延迟时间（秒）
            backoff: 退避因子
            
        Returns:
            请求响应对象或None（如果所有重试都失败）
        """
        operation = lambda: self.post(endpoint, data=data, json_data=json_data, headers=headers)
        return retry_operation(operation, max_retries=max_retries, delay=delay, backoff=backoff, exceptions=(requests.RequestException,))
    
    def login(self, username, password):
        """
        用户登录
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            (成功标志, 响应数据或错误消息)
        """
        try:
            login_url = "login.do"
            data = {
                "username": username,
                "password": password
            }
            
            response = self.post(login_url, data=data)
            
            if response.status_code == 200:
                # 检查登录是否成功
                if "登录成功" in response.text or "会员中心" in response.text:
                    self.logger.info(f"用户 {username} 登录成功")
                    return True, "登录成功"
                else:
                    error_msg = "登录失败，请检查用户名和密码"
                    self.logger.info(error_msg)
                    return False, error_msg
            else:
                error_msg = f"登录请求失败，状态码: {response.status_code}"
                self.logger.info(error_msg)
                return False, error_msg
        except Exception as e:
            error_msg = f"登录过程中出错: {str(e)}"
            self.logger.info(error_msg)
            return False, error_msg
    
    def get_user_info(self):
        """
        获取用户信息
        
        Returns:
            (成功标志, 用户信息或错误消息)
        """
        try:
            user_info_url = "userInfo.do"
            response = self.get(user_info_url)
            
            if response.status_code == 200:
                # 解析用户信息
                try:
                    user_info = json.loads(response.text)
                    self.logger.info("成功获取用户信息")
                    return True, user_info
                except json.JSONDecodeError:
                    error_msg = "解析用户信息失败，响应不是有效的JSON"
                    self.logger.info(error_msg)
                    return False, error_msg
            else:
                error_msg = f"获取用户信息失败，状态码: {response.status_code}"
                self.logger.info(error_msg)
                return False, error_msg
        except Exception as e:
            error_msg = f"获取用户信息过程中出错: {str(e)}"
            self.logger.info(error_msg)
            return False, error_msg
    
    def get_game_info(self, game_id):
        """
        获取游戏信息
        
        Args:
            game_id: 游戏ID
            
        Returns:
            (成功标志, 游戏信息或错误消息)
        """
        try:
            game_info_url = f"game/info/{game_id}.do"
            response = self.get(game_info_url)
            
            if response.status_code == 200:
                # 解析游戏信息
                try:
                    game_info = json.loads(response.text)
                    self.logger.info(f"成功获取游戏 {game_id} 的信息")
                    return True, game_info
                except json.JSONDecodeError:
                    error_msg = "解析游戏信息失败，响应不是有效的JSON"
                    self.logger.info(error_msg)
                    return False, error_msg
            else:
                error_msg = f"获取游戏信息失败，状态码: {response.status_code}"
                self.logger.error(error_msg)
                return False, error_msg
        except Exception as e:
            error_msg = f"获取游戏信息过程中出错: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg
    
    def get_trial_page(self):
        """
        获取试玩页面
        
        Returns:
            (成功标志, 页面内容或错误消息)
        """
        try:
            trial_url = "trial.do"
            response = self.get(trial_url)
            
            if response.status_code == 200:
                self.logger.info("成功获取试玩页面")
                return True, response.text
            else:
                error_msg = f"获取试玩页面失败，状态码: {response.status_code}"
                self.logger.error(error_msg)
                return False, error_msg
        except Exception as e:
            error_msg = f"获取试玩页面过程中出错: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg
    
    def get_trial_account(self):
        """
        获取试玩账号信息

        Returns:
            (成功标志, 试玩账号信息或错误消息)
        """
        try:
            trial_url = "normalTrialPlay.do"
            response = self.get(trial_url)

            if response.status_code == 200:
                # 模拟解析试玩账号信息
                # 在实际应用中，这里应该解析HTML或JSON响应
                trial_info = {
                    "account": f"trial_{int(time.time()) % 10000}",
                    "balance": "10000.00"
                }
                self.logger.info("成功获取试玩账号信息")
                return True, trial_info
            else:
                error_msg = f"获取试玩账号失败，状态码: {response.status_code}"
                self.logger.error(error_msg)
                return False, error_msg
        except Exception as e:
            error_msg = f"获取试玩账号过程中出错: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg

    def get_game_page(self):
        """
        获取游戏页面

        Returns:
            (成功标志, 页面内容或错误消息)
        """
        try:
            game_url = "offcial/index.do?code=JSSC168#5.0.0"
            response = self.get(game_url)

            if response.status_code == 200:
                self.logger.info("成功获取游戏页面")
                return True, response.text
            else:
                error_msg = f"获取游戏页面失败，状态码: {response.status_code}"
                self.logger.error(error_msg)
                return False, error_msg
        except Exception as e:
            error_msg = f"获取游戏页面过程中出错: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg

    def get_game_info(self):
        """
        获取游戏信息

        Returns:
            (成功标志, 游戏信息或错误消息)
        """
        try:
            # 生成时间戳
            timestamp = int(time.time() * 1000)
            game_info_url = f"lotData/getNewPeriod.do?code=JSSC168&_={timestamp}"
            response = self.get(game_info_url)

            if response.status_code == 200:
                try:
                    game_info = json.loads(response.text)
                    self.logger.info("成功获取游戏信息")
                    return True, game_info
                except json.JSONDecodeError:
                    error_msg = "解析游戏信息失败，响应不是有效的JSON"
                    self.logger.error(error_msg)
                    return False, error_msg
            else:
                error_msg = f"获取游戏信息失败，状态码: {response.status_code}"
                self.logger.error(error_msg)
                return False, error_msg
        except Exception as e:
            error_msg = f"获取游戏信息过程中出错: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg

    def get_bet_history(self):
        """
        获取投注记录

        Returns:
            (成功标志, 投注记录列表或错误消息)
        """
        try:
            # 模拟投注记录数据
            history_data = [
                {
                    "period": f"20250603-{i:03d}",
                    "amount": f"{(i+1)*10}.00",
                    "status": "已中奖" if i % 3 == 0 else "未中奖",
                    "time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time() - i * 300))
                }
                for i in range(5)
            ]

            self.logger.info(f"成功获取投注记录，共 {len(history_data)} 条")
            return True, history_data
        except Exception as e:
            error_msg = f"获取投注记录过程中出错: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg

    def place_bet(self, period, bet_data, multiple, unit):
        """
        下注

        Args:
            period: 期号
            bet_data: 投注数据
            multiple: 倍数
            unit: 单位

        Returns:
            (成功标志, 下注结果或错误消息)
        """
        try:
            bet_url = "bet.do"
            data = {
                "period": period,
                "bet_data": json.dumps(bet_data),
                "multiple": multiple,
                "unit": unit
            }

            response = self.post(bet_url, data=data)

            if response.status_code == 200:
                # 模拟投注成功
                bet_result = {
                    "success": True,
                    "message": "投注成功",
                    "order_id": f"ORDER_{int(time.time())}"
                }
                self.logger.info(f"投注成功: 期号 {period}")
                return True, bet_result
            else:
                error_msg = f"投注请求失败，状态码: {response.status_code}"
                self.logger.error(error_msg)
                return False, {"message": error_msg}
        except Exception as e:
            error_msg = f"投注过程中出错: {str(e)}"
            self.logger.error(error_msg)
            return False, {"message": error_msg}
