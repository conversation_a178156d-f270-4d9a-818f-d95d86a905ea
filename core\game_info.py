#!/usr/bin/env python
# -*- coding: utf-8 -*-

from bs4 import BeautifulSoup
from urllib.parse import urljoin
import re
import threading
import time
from utils.logger import Logger

class GameInfo:
    """游戏信息类，负责管理游戏数据和状态"""
    
    def __init__(self, api_client):
        """
        初始化游戏信息类
        
        Args:
            api_client: API客户端对象
        """
        self.api_client = api_client
        self.logger = Logger("GameInfo")
        self.game_code = "JSSC168"  # 默认游戏代码
        self.game_name = "168极速赛车"  # 默认游戏名称
        self.game_icon = "/common/lot/images/gameIcon/JSSC168.png"  # 默认图标路径
        self.official_site = "https://jk7859.mcu25805gtwqekeyw.com:59789/index.do"
        
        # 期号信息
        self.current_period = ""
        self.period_status = "等待下期开盘"
        
        # 倒计时信息
        self.remaining_time = 0  # 剩余时间（秒）
        self.remaining_time_str = "00:00:00"  # 格式化的剩余时间
        
        # 更新线程
        self.update_thread = None
        self.is_updating = False
        self.update_interval = 5  # 更新间隔（秒）
        
        # 回调函数
        self.on_update_callbacks = []
    
    def fetch_game_info(self):
        """
        从服务器获取游戏信息
        
        Returns:
            成功获取信息返回True，否则返回False
        """
        try:
            # 构建游戏页面URL
            game_url = f"offcial/index.do?code={self.game_code}#5.0.0"
            
            # 使用API客户端发送请求
            success, response_text = self.api_client.get_trial_page()
            
            if not success:
                self.logger.error("获取游戏页面失败")
                return False
            
            # 解析HTML
            soup = BeautifulSoup(response_text, 'html.parser')
            
            # 尝试直接使用您提供的HTML代码
            game_info_div = soup.select_one('div.game_info')
            if not game_info_div:
                self.logger.warning("使用 div.game_info 选择器未找到游戏信息区域")
                
                # 尝试使用更宽松的选择器
                game_info_div = soup.find('div', class_='game_info')
                if not game_info_div:
                    self.logger.warning("使用 find('div', class_='game_info') 仍然未找到游戏信息区域")
                    
                    # 尝试使用部分匹配
                    game_info_div = soup.find('div', class_=lambda c: c and 'game_info' in c)
                    if not game_info_div:
                        self.logger.warning("使用部分匹配仍然未找到游戏信息区域")
                        
                        # 使用 '--' 作为占位符
                        self.game_name = "--"
                        self.game_icon = "/common/lot/images/gameIcon/JSSC168.png"  # 保留图标路径以显示默认图标
                        self.official_site = "--"
                        self.current_period = "--"
                        self.period_status = "--"
                        self.remaining_time = 0
                        self.remaining_time_str = "--:--:--"
                        
                        self.logger.info("使用硬编码的游戏信息")
                        return True
            
            self.logger.info("成功找到游戏信息区域")
            
            # 提取游戏图标
            game_ico_div = game_info_div.select_one('.game_ico img')
            if game_ico_div and game_ico_div.get('src'):
                self.game_icon = game_ico_div['src']
                self.logger.debug(f"游戏图标: {self.game_icon}")
            
            # 提取游戏名称（修正：从 .game_mane 下的 span 获取）
            game_mane_div = game_info_div.select_one('div.game_mane')
            if game_mane_div:
                game_name_span = game_mane_div.find('span')
                if game_name_span:
                    self.game_name = game_name_span.text.strip()
                    self.logger.debug(f"游戏名称: {self.game_name}")
                else:
                    self.logger.warning("未找到游戏名称 span 标签")
                    # 尝试直接获取文本
                    game_name_text = game_mane_div.get_text(strip=True)
                    if game_name_text:
                        self.game_name = game_name_text
                        self.logger.debug(f"尝试直接获取文本: {self.game_name}")
            
            # 提取官方网站链接
            official_site_a = game_info_div.select_one('div.official_site a')
            if official_site_a and official_site_a.get('href'):
                self.official_site = official_site_a['href']
                self.logger.debug(f"官方网站: {self.official_site}")
            
            # 提取期号信息
            period_div = game_info_div.select_one('div.period')
            if period_div:
                period_text = period_div.get_text(strip=True)
                period_match = re.search(r'第(\d+)期', period_text)
                if period_match:
                    self.current_period = period_match.group(1)
                    self.logger.debug(f"期号: {self.current_period}")
            
            # 提取期号状态
            status_div = game_info_div.select_one('div.status')
            if status_div:
                self.period_status = status_div.get_text(strip=True)
                self.logger.debug(f"期号状态: {self.period_status}")
            
            # 提取倒计时信息
            resttime_span = soup.select_one('span.resttime')
            if resttime_span:
                # 尝试从JavaScript中提取倒计时
                script_tags = soup.find_all('script')
                for script in script_tags:
                    script_text = script.string
                    if script_text and 'var countDownTime' in script_text:
                        time_match = re.search(r'var countDownTime\s*=\s*(\d+)', script_text)
                        if time_match:
                            self.remaining_time = int(time_match.group(1))
                            self.remaining_time_str = self.format_remaining_time(self.remaining_time)
                            self.logger.debug(f"从JavaScript中提取剩余时间: {self.remaining_time_str}")
                            break
                
                # 如果从JavaScript中提取失败，尝试从HTML中提取
                if self.remaining_time == 0:
                    seconds_match = re.findall(r'<div class="inn">(\d+)</div>', str(resttime_span))
                    if len(seconds_match) >= 6:  # 希望至少有 6 个数字（小时、分钟、秒数）
                        hours = int(seconds_match[0] + seconds_match[1])
                        minutes = int(seconds_match[2] + seconds_match[3])
                        seconds = int(seconds_match[4] + seconds_match[5])
                        self.remaining_time = hours * 3600 + minutes * 60 + seconds
                        self.remaining_time_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
                        self.logger.debug(f"从显示的倒计时中提取剩余时间: {self.remaining_time_str}")
            
            self.logger.info(f"成功获取游戏信息: {self.game_name}, 期号: {self.current_period}, 状态: {self.period_status}")
            return True
            
        except Exception as e:
            self.logger.error(f"获取游戏信息失败: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return False
    
    def format_remaining_time(self, seconds):
        """
        将秒数格式化为时:分:秒格式
        
        Args:
            seconds: 剩余秒数
            
        Returns:
            格式化后的时间字符串
        """
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    
    def start_auto_update(self):
        """开始自动更新游戏信息"""
        if self.is_updating:
            return
        
        self.is_updating = True
        self.update_thread = threading.Thread(target=self._update_task)
        self.update_thread.daemon = True
        self.update_thread.start()
    
    def stop_auto_update(self):
        """停止自动更新"""
        self.is_updating = False
        if self.update_thread:
            self.update_thread.join(timeout=1.0)
            self.update_thread = None
    
    def _update_task(self):
        """更新任务，定期获取游戏信息并通知回调函数"""
        while self.is_updating:
            success = self.fetch_game_info()
            if success:
                # 通知所有回调
                for callback in self.on_update_callbacks:
                    try:
                        callback(self)
                    except Exception as e:
                        self.logger.error(f"执行回调时出错: {str(e)}")
            
            # 倒计时更新
            local_remaining = self.remaining_time
            for _ in range(self.update_interval):
                if not self.is_updating:
                    break
                if local_remaining > 0:
                    local_remaining -= 1
                    self.remaining_time = local_remaining
                    self.remaining_time_str = self.format_remaining_time(local_remaining)
                    # 通知倒计时更新
                    for callback in self.on_update_callbacks:
                        try:
                            callback(self, countdown_only=True)
                        except Exception as e:
                            self.logger.error(f"执行倒计时回调时出错: {str(e)}")
                time.sleep(1)
    
    def register_update_callback(self, callback):
        """
        注册更新回调函数
        
        Args:
            callback: 回调函数，接收游戏信息对象作为参数
        """
        if callback not in self.on_update_callbacks:
            self.on_update_callbacks.append(callback)
    
    def unregister_update_callback(self, callback):
        """
        取消注册更新回调函数
        
        Args:
            callback: 之前注册的回调函数
        """
        if callback in self.on_update_callbacks:
            self.on_update_callbacks.remove(callback)
    
    def get_game_info_dict(self):
        """
        获取游戏信息字典，方便其他模块使用
        
        Returns:
            包含游戏信息的字典
        """
        return {
            'game_code': self.game_code,
            'game_name': self.game_name,
            'game_icon': self.game_icon,
            'official_site': self.official_site,
            'current_period': self.current_period,
            'period_status': self.period_status,
            'remaining_time': self.remaining_time,
            'remaining_time_str': self.remaining_time_str
        }
