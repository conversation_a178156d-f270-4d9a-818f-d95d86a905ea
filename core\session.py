#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import datetime
import pickle
import json
import os
import re
from requests.cookies import RequestsCookieJar
from utils.logger import Logger

# 全局配置
class SessionConfig:
    """会话配置类"""
    BASE_DOMAIN = "jvvpbv2580513aknu.com"
    BASE_URL = f"https://jk7859.{BASE_DOMAIN}:59789"
    SESSION_FILE = "session.pkl"
    USER_INFO_FILE = "user_info.json"

class SessionManager:
    """会话管理类，负责处理用户会话的创建、保存、加载和验证"""
    
    def __init__(self):
        """初始化会话管理器"""
        self.config = SessionConfig()
        self.session = self.get_new_session()
        self.username = ""
        self.balance = "0"
        self.user_links = []
        self.is_logged_in = False
        self.logger = Logger("SessionManager")
    
    def create_session(self):
        """创建新的会话"""
        session = requests.Session()
        return session
        
    def get_new_session(self):
        """创建新的会话（兼容旧代码）"""
        return self.create_session()
    
    def save_session(self):
        """保存会话到文件"""
        try:
            # 保存会话到文件
            with open(self.config.SESSION_FILE, "wb") as f:
                pickle.dump(self.session, f)
            self.logger.info(f"会话信息已保存到 {self.config.SESSION_FILE}")
            
            # 保存用户信息到文件
            user_info = {
                "username": self.username,
                "balance": self.balance,
                "user_links": self.user_links,
                "is_logged_in": self.is_logged_in
            }
            with open(self.config.USER_INFO_FILE, "w", encoding="utf-8") as f:
                json.dump(user_info, f, ensure_ascii=False)
            self.logger.info(f"用户信息已保存到 {self.config.USER_INFO_FILE}")
            return True
        except Exception as e:
            self.logger.error(f"保存会话信息时出错: {str(e)}")
            return False
    
    def load_session(self):
        """加载会话从文件"""
        try:
            # 检查会话文件是否存在
            if not os.path.exists(self.config.SESSION_FILE) or not os.path.exists(self.config.USER_INFO_FILE):
                self.logger.info("没有找到会话文件，需要重新登录")
                return False
            
            # 加载会话
            with open(self.config.SESSION_FILE, "rb") as f:
                session_data = pickle.load(f)
            
            # 确保加载的是Session对象而不是RequestsCookieJar
            if not isinstance(session_data, requests.Session):
                self.logger.warning(f"加载的会话对象类型不正确: {type(session_data)}, 创建新会话")
                self.session = requests.Session()
                # 如果有必要，可以从旧会话中复制cookies
                if hasattr(session_data, 'cookies'):
                    self.session.cookies.update(session_data.cookies)
            else:
                self.session = session_data
                
            self.logger.info(f"从 {self.config.SESSION_FILE} 加载了会话信息")
            
            # 加载用户信息
            with open(self.config.USER_INFO_FILE, "r", encoding="utf-8") as f:
                user_info = json.load(f)
        
            # 检查user_info是否是字典类型
            if not isinstance(user_info, dict):
                self.logger.warning(f"加载的用户信息类型不正确: {type(user_info)}, 创建新用户信息")
                user_info = {"username": "", "balance": "0", "user_links": [], "is_logged_in": False}
        
            self.username = user_info.get("username", "")
            self.balance = user_info.get("balance", "0")
            self.user_links = user_info.get("user_links", [])
            self.is_logged_in = user_info.get("is_logged_in", False)
            
            self.logger.info(f"从 {self.config.USER_INFO_FILE} 加载了用户信息: {self.username}")
            
            return True
        except Exception as e:
            self.logger.error(f"加载会话信息时出错: {str(e)}")
            return False
    
    def verify_session(self):
        """验证会话是否有效"""
        try:
            if not self.is_logged_in:
                return False
            
            # 访问首页检查登录状态
            index_url = f"{self.config.BASE_URL}/index.do"
            self.logger.debug(f"验证会话，发送请求到: {index_url}")
            
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36",
                "Referer": f"{self.config.BASE_URL}/"
            }
            
            response = self.session.get(index_url, headers=headers, verify=False, timeout=10)
            
            # 检查是否包含登录成功的标志
            patterns = [
                r'账\s*号',
                r'系统余额',
                r'退出登录',
                r'会员中心'
            ]
            
            for pattern in patterns:
                if re.search(pattern, response.text):
                    self.logger.info(f"会话验证成功，找到登录标志: {pattern}")
                    return True
            
            self.logger.warning("会话验证失败，未找到登录标志")
            return False
        except Exception as e:
            self.logger.error(f"验证会话时出错: {str(e)}")
            return False
    
    def load_session_and_validate(self):
        """加载会话并验证"""
        try:
            # 加载会话
            if self.load_session():
                # 验证会话是否有效
                if self.verify_session():
                    # 会话有效
                    self.logger.info("会话有效，自动登录成功")
                    self.is_logged_in = True
                else:
                    # 会话无效，需要重新登录
                    self.logger.info("会话无效，需要重新登录")
                    self.is_logged_in = False
                    # 创建新会话
                    self.session = self.get_new_session()
            else:
                self.logger.info("没有找到会话文件，需要重新登录")
                self.is_logged_in = False
        except Exception as e:
            self.logger.error(f"加载会话时出错: {str(e)}")
            self.is_logged_in = False
            self.session = self.get_new_session()
        
        return self.is_logged_in
    
    def clear_session(self):
        """清除会话"""
        try:
            if os.path.exists(self.config.SESSION_FILE):
                os.remove(self.config.SESSION_FILE)
            if os.path.exists(self.config.USER_INFO_FILE):
                os.remove(self.config.USER_INFO_FILE)
            self.logger.info("已删除会话文件")
            
            # 重置会话状态
            self.session = self.get_new_session()
            self.username = ""
            self.balance = "0"
            self.user_links = []
            self.is_logged_in = False
            
            return True
        except Exception as e:
            self.logger.error(f"删除会话文件时出错: {str(e)}")
            return False
