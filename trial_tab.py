#!/usr/bin/env python
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin
from utils import debug_log, BASE_URL, register_status_callback, unregister_status_callback
import datetime
import threading
import time
import json
import re
from PIL import Image, ImageTk
import io
from game_info import GameInfo

class TrialTab:
    def __init__(self, parent, notebook, app, root=None):
        """初始化试玩选项卡"""
        self.parent = parent
        self.notebook = notebook
        self.app = app
        self.session = app.session
        self.root = root if root is not None else parent  # 兼容旧代码，优先用root参数
        
        # 注册状态更新回调
        register_status_callback(self.update_status_from_log)
        
        # 创建试玩选项卡
        self.tab = ttk.Frame(notebook)
        notebook.add(self.tab, text="试玩")
        
        # 账号信息
        self.account = ""
        self.balance = "0"
        self.has_loaded_trial = False  # 标记是否已加载试玩账号
        
        # 投注信息
        self.positions = ["冠军", "亚军", "季军", "第四", "第五", "第六", "第七", "第八", "第九", "第十"]
        
        # 默认选中的位置
        self.selected_position = "冠军"  # 默认选中冠军
        self.selected_numbers = []  # 兼容旧代码
        
        # 每个位置的选中号码和投注金额
        self.position_data = {}
        for position in self.positions:
            self.position_data[position] = {
                "selected_numbers": [],
                "amount": tk.StringVar(value="1")
            }
        
        # 总投注金额
        self.total_amount = tk.StringVar(value="0")
        
        # 设置默认投注模式为元
        self.bet_unit = tk.StringVar(value="1")
        self.bet_unit_text = tk.StringVar(value="元")
        
        # 设置默认倍数为1
        self.multiple = tk.StringVar(value="1")
        self.multiple_options = ["1", "5", "10", "15", "20", "50", "100", "200", "500", "1000", "2000"]
        
        # 自动刷新相关
        self.auto_refresh = False
        self.refresh_interval = 30  # 秒
        self.refresh_thread = None
        
        # 游戏信息相关
        self.game_info = GameInfo(self.session)
        self.game_icon_image = None  # 游戏图标图片对象
        self.game_icon_photo = None  # Tkinter PhotoImage对象
        
        # 初始化选项卡内容
        self.init_tab_content()
        
        # 不自动加载试玩账号信息，改为按钮触发
        # 不自动加载投注记录，在加载试玩账号后才加载
        
        # 监听选项卡切换
        self.notebook.bind("<<NotebookTabChanged>>", self.on_tab_changed)

    def init_tab_content(self):
        """初始化选项卡内容"""
        # 创建主要容器
        self.main_paned = ttk.PanedWindow(self.tab, orient=tk.HORIZONTAL)
        self.main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 左侧面板 - 账号信息和投注界面
        self.left_frame = ttk.Frame(self.main_paned)
        self.main_paned.add(self.left_frame, weight=5)  # 进一步增加左侧面板的权重
        
        # 右侧面板 - 状态信息
        self.right_frame = ttk.Frame(self.main_paned)
        self.main_paned.add(self.right_frame, weight=1)  # 保持右侧面板的权重不变
        
        # 初始化左侧面板
        self.init_left_panel()
        
        # 初始化右侧面板
        self.init_right_panel()
        
        # 在初始化完成后设置默认选中的位置
        self.select_position(self.selected_position)

    def init_left_panel(self):
        """初始化左侧面板"""
        # 游戏信息框架
        self.game_info_frame = ttk.LabelFrame(self.left_frame, text="游戏信息", padding=10)
        self.game_info_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 游戏信息布局
        game_info_container = ttk.Frame(self.game_info_frame)
        game_info_container.pack(fill=tk.X, expand=True)
        
        # 游戏图标区域
        self.game_icon_label = ttk.Label(game_info_container)
        self.game_icon_label.grid(row=0, column=0, rowspan=2, padx=5, pady=5)
        
        # 游戏名称区域
        self.game_name_label = ttk.Label(game_info_container, text="168极速赛车", font=("Arial", 12, "bold"))
        self.game_name_label.grid(row=0, column=1, sticky=tk.W, padx=5)
        
        # 官方开奖网链接
        self.official_site_link = ttk.Label(game_info_container, text="官方开奖网", foreground="blue", cursor="hand2")
        self.official_site_link.grid(row=0, column=2, sticky=tk.W, padx=5)
        self.official_site_link.bind("<Button-1>", self.open_official_site)
        
        # 期号信息区域
        period_frame = ttk.Frame(game_info_container)
        period_frame.grid(row=1, column=1, columnspan=2, sticky=tk.W, padx=5)
        
        self.period_label = ttk.Label(period_frame, text="第 0000000 期")
        self.period_label.pack(side=tk.LEFT, padx=5)
        
        self.period_status_label = ttk.Label(period_frame, text="等待下期开盘")
        self.period_status_label.pack(side=tk.LEFT, padx=5)
        
        # 上一期信息区域
        previous_period_frame = ttk.Frame(game_info_container)
        previous_period_frame.grid(row=2, column=0, columnspan=5, sticky=tk.W, padx=5, pady=(5, 0))
        
        ttk.Label(previous_period_frame, text="上期期号:").pack(side=tk.LEFT, padx=(0, 5))
        self.previous_period_label = ttk.Label(previous_period_frame, text="--")
        self.previous_period_label.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Label(previous_period_frame, text="开奖号码:").pack(side=tk.LEFT, padx=(0, 5))
        self.previous_result_label = ttk.Label(previous_period_frame, text="--")
        self.previous_result_label.pack(side=tk.LEFT)
        
        # 倒计时区域
        countdown_frame = ttk.Frame(game_info_container)
        countdown_frame.grid(row=0, column=3, rowspan=2, padx=10)
        
        self.countdown_label = ttk.Label(countdown_frame, text="00:00:00", font=("Arial", 14, "bold"))
        self.countdown_label.pack()
        
        ttk.Label(countdown_frame, text="剩余投注时间").pack()
        
        # 刷新游戏信息按钮
        refresh_game_info_btn = ttk.Button(
            game_info_container,
            text="刷新游戏信息",
            command=self.refresh_game_info
        )
        refresh_game_info_btn.grid(row=0, column=4, rowspan=2, padx=10)
        
        # 账号信息框架
        self.info_frame = ttk.LabelFrame(self.left_frame, text="试玩账号信息", padding=10)
        self.info_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 显示账号信息的标签
        ttk.Label(self.info_frame, text="账号:").grid(row=0, column=0, sticky=tk.W)
        self.account_label = ttk.Label(self.info_frame, text="未获取")
        self.account_label.grid(row=0, column=1, sticky=tk.W)
        
        ttk.Label(self.info_frame, text="余额:").grid(row=1, column=0, sticky=tk.W)
        self.balance_label = ttk.Label(self.info_frame, text="未获取")
        self.balance_label.grid(row=1, column=1, sticky=tk.W)
        
        # 获取试玩按钮
        get_trial_btn = ttk.Button(
            self.info_frame, 
            text="获取试玩",
            command=self.confirm_get_trial
        )
        get_trial_btn.grid(row=0, column=2, rowspan=2, padx=10)
        
        # 刷新余额按钮
        refresh_balance_btn = ttk.Button(
            self.info_frame, 
            text="刷新余额",
            command=self.refresh_balance
        )
        refresh_balance_btn.grid(row=0, column=3, rowspan=2, padx=10)
        
        # 刷新全部按钮
        refresh_btn = ttk.Button(
            self.info_frame, 
            text="刷新全部",
            command=self.refresh_all
        )
        refresh_btn.grid(row=0, column=4, rowspan=2, padx=10)
        
        # 进入游戏按钮
        game_btn = ttk.Button(
            self.info_frame,
            text="进入游戏",
            command=self.go_to_game
        )
        game_btn.grid(row=0, column=5, rowspan=2, padx=10)
        
        # 自动刷新开关
        self.auto_refresh_var = tk.BooleanVar(value=False)
        auto_refresh_check = ttk.Checkbutton(
            self.info_frame,
            text="自动刷新",
            variable=self.auto_refresh_var,
            command=self.toggle_auto_refresh
        )
        auto_refresh_check.grid(row=0, column=6, rowspan=2, padx=10)
        
        # 投注界面框架
        self.bet_frame = ttk.LabelFrame(self.left_frame, text="投注界面", padding=10)
        self.bet_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)  # 允许自动扩展
        
        # 创建投注界面的滚动区域，以支持更多内容
        self.bet_canvas = tk.Canvas(self.bet_frame)
        self.bet_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)  # 允许自动扩展
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(self.bet_frame, orient=tk.VERTICAL, command=self.bet_canvas.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.bet_canvas.configure(yscrollcommand=scrollbar.set)
        
        # 创建内部框架来存放所有投注元素
        self.bet_inner_frame = ttk.Frame(self.bet_canvas)
        self.bet_canvas.create_window((0, 0), window=self.bet_inner_frame, anchor=tk.NW)
        
        # 创建金额选择框架
        self.amount_frame = ttk.LabelFrame(self.bet_inner_frame, text="投注金额设置", padding=5)
        self.amount_frame.grid(row=4, column=0, columnspan=5, sticky=tk.EW, padx=5, pady=10)
        
        # 创建金额选择内容
        amount_container = ttk.Frame(self.amount_frame)
        amount_container.pack(fill=tk.X, expand=True, padx=5, pady=5)
        
        # 左侧倍数选择区域
        multiple_frame = ttk.Frame(amount_container)
        multiple_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        # 倍数选择标签
        ttk.Label(multiple_frame, text="倍数:").pack(side=tk.LEFT, padx=5)
        
        # 倍数减少按钮
        minus_btn = ttk.Button(multiple_frame, text="-", width=2, command=lambda: self.adjust_multiple(-1))
        minus_btn.pack(side=tk.LEFT, padx=2)
        
        # 倍数输入框
        self.multiple_entry = ttk.Entry(multiple_frame, textvariable=self.multiple, width=6)
        self.multiple_entry.pack(side=tk.LEFT, padx=2)
        
        # 倍数增加按钮
        plus_btn = ttk.Button(multiple_frame, text="+", width=2, command=lambda: self.adjust_multiple(1))
        plus_btn.pack(side=tk.LEFT, padx=2)
        
        # 倍数选择下拉菜单
        self.multiple_menu = tk.Menu(self.tab, tearoff=0)
        for option in self.multiple_options:
            self.multiple_menu.add_command(label=option, command=lambda opt=option: self.set_multiple(opt))
        
        # 绑定右键菜单到倍数输入框
        self.multiple_entry.bind("<Button-3>", self.show_multiple_menu)
        
        # 倍数标签
        ttk.Label(multiple_frame, text="倍").pack(side=tk.LEFT, padx=5)
        
        # 右侧模式选择区域
        unit_frame = ttk.Frame(amount_container)
        unit_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        # 模式选择标签
        ttk.Label(unit_frame, text="模式:").pack(side=tk.LEFT, padx=5)
        
        # 元按钮
        yuan_btn = ttk.Button(unit_frame, text="元", width=4, command=lambda: self.set_bet_unit("1", "元"))
        yuan_btn.pack(side=tk.LEFT, padx=2)
        
        # 角按钮
        jiao_btn = ttk.Button(unit_frame, text="角", width=4, command=lambda: self.set_bet_unit("0.1", "角"))
        jiao_btn.pack(side=tk.LEFT, padx=2)
        
        # 分按钮
        fen_btn = ttk.Button(unit_frame, text="分", width=4, command=lambda: self.set_bet_unit("0.01", "分"))
        fen_btn.pack(side=tk.LEFT, padx=2)
        
        # 当前模式显示
        self.unit_label = ttk.Label(unit_frame, textvariable=self.bet_unit_text, width=4)
        self.unit_label.pack(side=tk.LEFT, padx=5)
        
        # 右侧总计信息
        total_frame = ttk.Frame(amount_container)
        total_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        # 总投注金额信息
        self.total_info_label = ttk.Label(total_frame, text="共选 0 注，共投 0 元，最大盈利 0 元")
        self.total_info_label.pack(side=tk.LEFT, padx=5)
        
        # 投注按钮
        bet_buttons_frame = ttk.Frame(amount_container)
        bet_buttons_frame.pack(side=tk.RIGHT, padx=5)
        
        # 确认选号按钮
        self.confirm_btn = ttk.Button(bet_buttons_frame, text="确认选号", width=10, command=self.confirm_selection)
        self.confirm_btn.pack(side=tk.LEFT, padx=5)
        
        # 立即下注按钮
        self.place_bet_btn = ttk.Button(bet_buttons_frame, text="立即下注", width=10, command=self.place_bet)
        self.place_bet_btn.pack(side=tk.LEFT, padx=5)
        
        # 创建位置选择框架
        self.position_frames = {}
        self.number_buttons = {}
        
        # 显示所有位置，分两行显示
        # 第一行显示前5个位置
        first_row_positions = self.positions[:5]
        # 第二行显示后5个位置
        second_row_positions = self.positions[5:]
        
        # 创建第一行位置的投注界面
        for i, position in enumerate(first_row_positions):
            # 每个位置的框架
            position_frame = ttk.LabelFrame(self.bet_inner_frame, text=position, padding=5)
            position_frame.grid(row=0, column=i, padx=5, pady=5, sticky=tk.NSEW)
            self.position_frames[position] = position_frame
            
            # 创建号码选择按钮
            numbers_frame = ttk.Frame(position_frame)
            numbers_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)
            
            # 创建号码按钮 (1-10)
            self.number_buttons[position] = {}
            for num in range(1, 11):
                num_str = f"{num:02d}"  # 格式化为两位数字，如 01, 02, ...
                btn = ttk.Button(
                    numbers_frame,
                    text=num_str,
                    width=3,
                    command=lambda p=position, n=num_str: self.toggle_number(p, n)
                )
                row = (num - 1) // 5
                col = (num - 1) % 5
                btn.grid(row=row, column=col, padx=2, pady=2)
                self.number_buttons[position][num_str] = btn
            
            # 快捷按钮框架
            quick_frame = ttk.Frame(position_frame)
            quick_frame.pack(fill=tk.X, padx=2, pady=2)
            
            # 全选按钮
            select_all_btn = ttk.Button(
                quick_frame,
                text="全选",
                width=6,
                command=lambda p=position: self.select_all_numbers(p)
            )
            select_all_btn.pack(side=tk.LEFT, padx=2)
            
            # 清除按钮
            clear_btn = ttk.Button(
                quick_frame,
                text="清除",
                width=6,
                command=lambda p=position: self.clear_numbers(p)
            )
            clear_btn.pack(side=tk.LEFT, padx=2)
            
            # 投注金额框架
            amount_frame = ttk.Frame(position_frame)
            amount_frame.pack(fill=tk.X, padx=2, pady=2)
            
            ttk.Label(amount_frame, text="金额:").pack(side=tk.LEFT)
            amount_entry = ttk.Entry(
                amount_frame, 
                width=6, 
                textvariable=self.position_data[position]["amount"]
            )
            amount_entry.pack(side=tk.LEFT, padx=2)
            
            # 快速金额按钮
            for amount in ["10", "50", "100"]:
                btn = ttk.Button(
                    amount_frame,
                    text=amount,
                    width=3,
                    command=lambda p=position, a=amount: self.set_position_amount(p, a)
                )
                btn.pack(side=tk.LEFT, padx=1)
        
        # 创建第二行位置的投注界面
        for i, position in enumerate(second_row_positions):
            # 每个位置的框架
            position_frame = ttk.LabelFrame(self.bet_inner_frame, text=position, padding=5)
            position_frame.grid(row=1, column=i, padx=5, pady=5, sticky=tk.NSEW)
            self.position_frames[position] = position_frame
            
            # 创建号码选择按钮
            numbers_frame = ttk.Frame(position_frame)
            numbers_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)
            
            # 创建号码按钮 (1-10)
            self.number_buttons[position] = {}
            for num in range(1, 11):
                num_str = f"{num:02d}"  # 格式化为两位数字，如 01, 02, ...
                btn = ttk.Button(
                    numbers_frame,
                    text=num_str,
                    width=3,
                    command=lambda p=position, n=num_str: self.toggle_number(p, n)
                )
                row = (num - 1) // 5
                col = (num - 1) % 5
                btn.grid(row=row, column=col, padx=2, pady=2)
                self.number_buttons[position][num_str] = btn
            
            # 快捷按钮框架
            quick_frame = ttk.Frame(position_frame)
            quick_frame.pack(fill=tk.X, padx=2, pady=2)
            
            # 全选按钮
            select_all_btn = ttk.Button(
                quick_frame,
                text="全选",
                width=6,
                command=lambda p=position: self.select_all_numbers(p)
            )
            select_all_btn.pack(side=tk.LEFT, padx=2)
            
            # 清除按钮
            clear_btn = ttk.Button(
                quick_frame,
                text="清除",
                width=6,
                command=lambda p=position: self.clear_numbers(p)
            )
            clear_btn.pack(side=tk.LEFT, padx=2)
            
            # 投注金额框架
            amount_frame = ttk.Frame(position_frame)
            amount_frame.pack(fill=tk.X, padx=2, pady=2)
            
            ttk.Label(amount_frame, text="金额:").pack(side=tk.LEFT)
            amount_entry = ttk.Entry(
                amount_frame, 
                width=6, 
                textvariable=self.position_data[position]["amount"]
            )
            amount_entry.pack(side=tk.LEFT, padx=2)
            
            # 快速金额按钮
            for amount in ["10", "50", "100"]:
                btn = ttk.Button(
                    amount_frame,
                    text=amount,
                    width=3,
                    command=lambda p=position, a=amount: self.set_position_amount(p, a)
                )
                btn.pack(side=tk.LEFT, padx=1)
        
        # 总金额和投注按钮框架
        total_frame = ttk.Frame(self.bet_inner_frame)
        total_frame.grid(row=2, column=0, columnspan=5, sticky=tk.EW, padx=5, pady=10)
        
        ttk.Label(total_frame, text="总投注金额:").pack(side=tk.LEFT, padx=5)
        total_amount_label = ttk.Label(total_frame, textvariable=self.total_amount)
        total_amount_label.pack(side=tk.LEFT, padx=5)
        
        # 投注按钮
        bet_btn = ttk.Button(
            total_frame,
            text="确认投注",
            command=self.place_bet
        )
        bet_btn.pack(side=tk.RIGHT, padx=10)
        
        # 更新滚动区域
        self.bet_inner_frame.update_idletasks()
        self.bet_canvas.config(scrollregion=self.bet_canvas.bbox(tk.ALL))
        
        # 投注状态框
        self.status_frame = ttk.LabelFrame(self.left_frame, text="投注状态", padding=10)
        self.status_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.status_text = scrolledtext.ScrolledText(self.status_frame, height=5, wrap=tk.WORD)
        self.status_text.pack(fill=tk.BOTH, expand=True)
        self.status_text.config(state=tk.DISABLED)

    def init_right_panel(self):
        """初始化右侧面板"""
        # 状态框架，设置指定大小
        self.status_frame = ttk.LabelFrame(self.right_frame, text="状态信息", padding=5)  # 减少内边距
        self.status_frame.pack(fill=tk.X, expand=False, padx=3, pady=3)  # 只填充水平方向，不扩展，减少外边距
        
        # 设置状态信息长666，宽222
        self.status_text = scrolledtext.ScrolledText(self.status_frame, width=50, height=100, wrap=tk.WORD)
        self.status_text.pack(fill=tk.BOTH, expand=True)
        self.status_text.config(state=tk.DISABLED)
        
        # 投注记录框架移到左侧面板，限制高度
        self.order_frame = ttk.LabelFrame(self.left_frame, text="投注记录", padding=10)
        self.order_frame.pack(fill=tk.X, expand=False, padx=5, pady=5)  # 只填充水平方向，不扩展
        
        # 创建投注记录表格
        self.create_order_history_table()
        
        # 刷新投注记录按钮
        refresh_btn = ttk.Button(
            self.order_frame,
            text="刷新记录",
            command=self.load_order_history
        )
        refresh_btn.grid(row=2, column=0, columnspan=2, pady=5)

    def create_order_history_table(self):
        """创建投注记录表格"""
        # 创建树形视图
        columns = ("order_id", "game_type", "issue", "bet_time", "bet_content", "odds", "amount", "bonus", "status")
        self.tree = ttk.Treeview(
            self.order_frame,
            columns=columns,
            show="headings",
            selectmode="browse"
        )
        
        # 定义列标题
        self.tree.heading("order_id", text="注单编号")
        self.tree.heading("game_type", text="玩法")
        self.tree.heading("issue", text="期号")
        self.tree.heading("bet_time", text="投注时间")
        self.tree.heading("bet_content", text="投注内容")
        self.tree.heading("odds", text="倍模")
        self.tree.heading("amount", text="总金额")
        self.tree.heading("bonus", text="奖金")
        self.tree.heading("status", text="状态")
        
        # 设置列宽度
        self.tree.column("order_id", width=100)
        self.tree.column("game_type", width=80)
        self.tree.column("issue", width=80)
        self.tree.column("bet_time", width=120)
        self.tree.column("bet_content", width=150)
        self.tree.column("odds", width=60)
        self.tree.column("amount", width=80)
        self.tree.column("bonus", width=80)
        self.tree.column("status", width=60)
        
        # 添加滚动条
        vsb = ttk.Scrollbar(self.order_frame, orient="vertical", command=self.tree.yview)
        hsb = ttk.Scrollbar(self.order_frame, orient="horizontal", command=self.tree.xview)
        self.tree.configure(yscrollcommand=vsb.set, xscrollcommand=hsb.set)
        
        # 网格布局
        self.tree.grid(row=0, column=0, sticky="nsew")
        vsb.grid(row=0, column=1, sticky="ns")
        hsb.grid(row=1, column=0, sticky="ew")
        
        # 配置网格权重
        self.order_frame.grid_rowconfigure(0, weight=1)
        self.order_frame.grid_columnconfigure(0, weight=1)
        
        # 添加双击事件
        self.tree.bind("<Double-1>", self.show_bet_details)
        
        # 注意：状态框架已移到右侧面板，投注记录框架已移到左侧面板

    def create_order_history_table(self):
        """创建投注记录表格"""
        # 创建树形视图
        columns = ("order_id", "game_type", "issue", "bet_time", "bet_content", "odds", "amount", "bonus", "status")
        self.tree = ttk.Treeview(
            self.order_frame,
            columns=columns,
            show="headings",
            selectmode="browse"
        )

        # 定义列标题
        self.tree.heading("order_id", text="注单编号")
        self.tree.heading("game_type", text="玩法")
        self.tree.heading("issue", text="期号")
        self.tree.heading("bet_time", text="投注时间")
        self.tree.heading("bet_content", text="投注内容")
        self.tree.heading("odds", text="倍模")
        self.tree.heading("amount", text="总金额")
        self.tree.heading("bonus", text="奖金")
        self.tree.heading("status", text="状态")

        # 设置列宽度
        self.tree.column("order_id", width=100)
        self.tree.column("game_type", width=80)
        self.tree.column("issue", width=80)
        self.tree.column("bet_time", width=120)
        self.tree.column("bet_content", width=150)
        self.tree.column("odds", width=60)
        self.tree.column("amount", width=80)
        self.tree.column("bonus", width=80)
        self.tree.column("status", width=60)
        
        # 添加滚动条
        vsb = ttk.Scrollbar(self.order_frame, orient="vertical", command=self.tree.yview)
        hsb = ttk.Scrollbar(self.order_frame, orient="horizontal", command=self.tree.xview)
        self.tree.configure(yscrollcommand=vsb.set, xscrollcommand=hsb.set)

        # 网格布局
        self.tree.grid(row=0, column=0, sticky="nsew")
        vsb.grid(row=0, column=1, sticky="ns")
        hsb.grid(row=1, column=0, sticky="ew")

        # 配置网格权重
        self.order_frame.grid_rowconfigure(0, weight=1)
        self.order_frame.grid_columnconfigure(0, weight=1)

        # 添加双击事件
        self.tree.bind("<Double-1>", self.show_bet_details)

    def create_number_buttons(self):
        """创建号码按钮 - 已由新的投注界面替代"""
        pass  # 这个方法不再需要，因为我们在init_left_panel中为每个位置创建了号码按钮
    
    def toggle_number(self, position, number):
        """切换号码选中状态"""
        # 获取当前位置的选中号码列表
        selected_numbers = self.position_data[position]["selected_numbers"]
        
        if number in selected_numbers:
            # 如果已经选中，则取消选中
            selected_numbers.remove(number)
            self.number_buttons[position][number].configure(style="TButton")
        else:
            # 如果未选中，则选中
            selected_numbers.append(number)
            # 创建一个选中状态的样式，数字文字为红色
            style = ttk.Style()
            style.configure("Selected.TButton", background="white", foreground="#FF0000")
            self.number_buttons[position][number].configure(style="Selected.TButton")
        
        # 更新总金额
        self.update_total_amount()
            
        # 更新状态显示
        self.update_status(f"当前选择: {position} - {', '.join(selected_numbers)}")
    
    def select_all_numbers(self, position):
        """全选号码"""
        # 先清除当前选中的号码
        self.clear_numbers(position)
        
        # 选中所有号码
        selected_numbers = self.position_data[position]["selected_numbers"]
        for i in range(1, 11):
            num_str = f"{i:02d}"
            selected_numbers.append(num_str)
            
            # 设置按钮样式，数字文字为红色
            style = ttk.Style()
            style.configure("Selected.TButton", background="white", foreground="#FF0000")
            self.number_buttons[position][num_str].configure(style="Selected.TButton")
        
        # 更新总金额
        self.update_total_amount()
        
        # 更新状态显示
        self.update_status(f"当前选择: {position} - 全选")
    
    # 注意: 这个方法已经被替换为上面的 clear_numbers(self, position=None) 方法
    def set_position_amount(self, position, amount):
        """设置指定位置的投注金额"""
        self.position_data[position]["amount"].set(amount)
        self.update_total_amount()
        self.update_status(f"设置 {position} 的投注金额为 {amount}")
    
    def update_total_amount(self):
        """更新总投注金额"""
        total = 0
        total_count = 0
        for position in self.positions:
            # 计算每个位置的投注金额
            try:
                # 每注基础金额是2元
                base_amount = 2.0
                num_selected = len(self.position_data[position]["selected_numbers"])
                multiple_value = float(self.multiple.get())
                unit_value = float(self.bet_unit.get())
                
                # 计算该位置的总金额 = 基础金额(2元) * 倍数 * 单位(元/角/分) * 选中号码数
                # 元=1.0, 角=0.1, 分=0.01
                position_total = base_amount * multiple_value * unit_value * num_selected
                total += position_total
                total_count += num_selected
            except ValueError:
                # 如果金额不是有效的数字，忽略
                pass
        
        # 更新总金额显示
        self.total_amount.set(f"{total:.2f}")
        
        # 计算最大盈利，假设赢率为19.8
        max_profit = total * 19.8 - total if total > 0 else 0
        
        # 更新总投注信息显示
        unit_text = self.bet_unit_text.get()
        self.total_info_label.config(text=f"共选 {total_count} 注，共投 {total:.2f} 元，最大盈利 {max_profit:.2f} 元")
        
    # 注意: 这个方法已经被替换为下面的 select_all_numbers(self, position) 方法
        
    def clear_numbers(self, position=None):
        """清除选中的号码
        
        Args:
            position: 指定要清除的位置，如果为None则清除当前选中的位置
        """
        if position is None:
            # 兼容旧版本，清除当前选中位置的号码
            self.selected_numbers = []
            # 添加检查确保 number_buttons 属性存在
            if hasattr(self, 'number_buttons') and self.number_buttons:
                for btn in self.number_buttons.values():
                    btn.configure(style="TButton")
            self.update_status("已清除选择")
        else:
            # 清除指定位置的号码
            if position in self.position_data:
                self.position_data[position]["selected_numbers"] = []
                # 重置该位置的按钮样式
                if position in self.number_buttons:
                    for num_str, btn in self.number_buttons[position].items():
                        btn.configure(style="TButton")
                self.update_status(f"已清除 {position} 的选择")
                
            # 更新总金额
            self.update_total_amount()
        
    def select_position(self, position):
        """选择位置"""
        # 在多位置投注模式下，这个方法不再需要重置按钮样式
        # 因为我们现在允许同时选择多个位置
        
        # 更新选中位置
        self.selected_position = position
        
        # 更新状态显示
        self.update_status(f"当前选择位置: {self.selected_position}")
        
    def set_amount(self, amount):
        """设置当前选中位置的投注金额"""
        if self.selected_position in self.position_data:
            self.position_data[self.selected_position]["amount"].set(amount)
            self.update_total_amount()
            self.update_status(f"设置 {self.selected_position} 的投注金额为 {amount}")
        
    def update_status(self, message):
        """更新状态显示"""
        self.status_text.config(state=tk.NORMAL)
        self.status_text.insert(tk.END, f"{datetime.datetime.now().strftime('%H:%M:%S')} - {message}\n")
        self.status_text.see(tk.END)
        self.status_text.config(state=tk.DISABLED)
    
    def update_status_from_log(self, message):
        """从日志更新状态显示，用于回调函数"""
        # 确保在主线程中更新UI
        if hasattr(self, 'status_text') and self.status_text:
            try:
                self.parent.after(0, lambda: self.update_status(message))
            except Exception as e:
                print(f"更新状态显示时出错: {str(e)}")
        
    def show_bet_details(self, event):
        """显示投注详情"""
        selected_item = self.tree.selection()
        if not selected_item:
            return
            
        item = self.tree.item(selected_item[0])
        values = item['values']
        
        if not values or len(values) < 9:
            return
            
        # 创建详情窗口
        details_window = tk.Toplevel(self.tab)
        details_window.title("投注详情")
        details_window.geometry("500x400")
        
        # 添加详情内容
        frame = ttk.Frame(details_window, padding=20)
        frame.pack(fill=tk.BOTH, expand=True)
        
        # 注单信息
        ttk.Label(frame, text="注单编号:", font=("Arial", 10, "bold")).grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Label(frame, text=values[0]).grid(row=0, column=1, sticky=tk.W, pady=5)
        
        ttk.Label(frame, text="玩法:", font=("Arial", 10, "bold")).grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Label(frame, text=values[1]).grid(row=1, column=1, sticky=tk.W, pady=5)
        
        ttk.Label(frame, text="期号:", font=("Arial", 10, "bold")).grid(row=2, column=0, sticky=tk.W, pady=5)
        ttk.Label(frame, text=values[2]).grid(row=2, column=1, sticky=tk.W, pady=5)
        
        ttk.Label(frame, text="投注时间:", font=("Arial", 10, "bold")).grid(row=3, column=0, sticky=tk.W, pady=5)
        ttk.Label(frame, text=values[3]).grid(row=3, column=1, sticky=tk.W, pady=5)
        
        ttk.Label(frame, text="投注内容:", font=("Arial", 10, "bold")).grid(row=4, column=0, sticky=tk.W, pady=5)
        content_text = scrolledtext.ScrolledText(frame, height=5, width=40, wrap=tk.WORD)
        content_text.grid(row=4, column=1, sticky=tk.W, pady=5)
        content_text.insert(tk.END, values[4])
        content_text.config(state=tk.DISABLED)
        
        ttk.Label(frame, text="倍模:", font=("Arial", 10, "bold")).grid(row=5, column=0, sticky=tk.W, pady=5)
        ttk.Label(frame, text=values[5]).grid(row=5, column=1, sticky=tk.W, pady=5)
        
        ttk.Label(frame, text="总金额:", font=("Arial", 10, "bold")).grid(row=6, column=0, sticky=tk.W, pady=5)
        ttk.Label(frame, text=values[6]).grid(row=6, column=1, sticky=tk.W, pady=5)
        
        ttk.Label(frame, text="奖金:", font=("Arial", 10, "bold")).grid(row=7, column=0, sticky=tk.W, pady=5)
        ttk.Label(frame, text=values[7], foreground="red").grid(row=7, column=1, sticky=tk.W, pady=5)
        
        ttk.Label(frame, text="状态:", font=("Arial", 10, "bold")).grid(row=8, column=0, sticky=tk.W, pady=5)
        status_label = ttk.Label(frame, text=values[8])
        if values[8] == "中奖":
            status_label.configure(foreground="green")
        elif values[8] == "未中奖":
            status_label.configure(foreground="red")
        status_label.grid(row=8, column=1, sticky=tk.W, pady=5)
        
        # 关闭按钮
        close_btn = ttk.Button(frame, text="关闭", command=details_window.destroy)
        close_btn.grid(row=9, column=0, columnspan=2, pady=20)

    def refresh_all(self):
        """刷新账号信息和投注记录"""
        self.load_trial_account()
        self.load_order_history()
        
    def refresh_balance(self):
        """只刷新余额"""
        try:
            # 访问试玩页面
            trial_url = urljoin(BASE_URL, "normalTrialPlay.do")
            self.logger.info(f"正在刷新余额...")
            response = self.session.get(trial_url, verify=False)
            response.raise_for_status()
            
            # 解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找余额元素
            balance_elem = soup.select_one('#AvailableBalance')
            
            if balance_elem:
                balance = balance_elem.get_text(strip=True)
                self.balance = balance
                self.balance_label.config(text=balance)
                self.logger.info(f"刷新余额成功: {balance}")
                self.update_status(f"余额已更新: {balance}")
                return True
            else:
                self.logger.info("未找到余额元素")
                self.update_status("刷新余额失败")
                return False
                
        except Exception as e:
            self.logger.info(f"刷新余额失败: {str(e)}")
            self.update_status("刷新余额失败")
            return False
            
    def toggle_auto_refresh(self):
        """切换自动刷新状态"""
        self.auto_refresh = self.auto_refresh_var.get()
        
        if self.auto_refresh:
            self.update_status(f"已开启自动刷新 (每{self.refresh_interval}秒)")
            self.start_auto_refresh()
        else:
            self.update_status("已关闭自动刷新")
            if self.refresh_thread and self.refresh_thread.is_alive():
                self.refresh_thread = None
                
    def start_auto_refresh(self):
        """启动自动刷新线程"""
        if not self.auto_refresh:
            return
            
        def refresh_task():
            while self.auto_refresh:
                self.refresh_balance()
                self.load_order_history()
                time.sleep(self.refresh_interval)
                
        self.refresh_thread = threading.Thread(target=refresh_task)
        self.refresh_thread.daemon = True
        self.refresh_thread.start()
        
    def confirm_get_trial(self):
        """确认是否获取试玩账号"""
        if self.has_loaded_trial and self.account and self.balance:
            # 如果已经加载过试玩账号，询问是否重新获取
            if messagebox.askyesno("确认", "您已经获取过试玩账号，是否重新获取？\n\n注意：试玩次数有限制，请谨慎使用"):
                self.load_trial_account()
        else:
            # 如果还没有加载过试玩账号，询问是否获取
            if messagebox.askyesno("确认", "是否获取试玩账号？\n\n注意：试玩次数有限制，请谨慎使用"):
                self.load_trial_account()
    
    def on_tab_changed(self, event):
        """当选项卡切换时触发"""
        try:
            current_tab = self.notebook.select()
            tab_text = self.notebook.tab(current_tab, "text")
            
            if tab_text == "试玩":
                self.logger.info("切换到试玩选项卡")
                # 在切换到试玩选项卡时启动游戏信息自动更新
                self.start_game_info_auto_update()
                
                # 如果已经加载过试玩账号，则刷新余额
                if self.has_loaded_trial:
                    self.refresh_balance()
            else:
                # 在切换到其他选项卡时停止游戏信息自动更新
                self.stop_game_info_auto_update()
        except Exception as e:
            self.logger.info(f"处理选项卡切换事件时出错: {str(e)}")
    
    def cleanup(self):
        """清理资源，在应用程序关闭时调用"""
        try:
            # 取消注册状态更新回调
            unregister_status_callback(self.update_status_from_log)
            
            # 停止游戏信息自动更新
            self.stop_game_info_auto_update()
            
            # 停止自动刷新
            if self.auto_refresh and self.refresh_thread and self.refresh_thread.is_alive():
                self.auto_refresh = False
                self.refresh_thread.join(timeout=1.0)
                
            self.logger.info("试玩选项卡资源已清理")
        except Exception as e:
            self.logger.info(f"清理试玩选项卡资源时出错: {str(e)}")
                
    def place_bet(self):
        """发送投注请求"""
        # 检查是否有选择的号码
        has_selections = False
        total_bet_amount = 0
        bet_positions = []
        
        # 检查每个位置的选择情况
        for position in self.positions:
            selected_numbers = self.position_data[position]["selected_numbers"]
            if selected_numbers:
                has_selections = True
                try:
                    amount = float(self.position_data[position]["amount"].get())
                    position_total = amount * len(selected_numbers)
                    total_bet_amount += position_total
                    bet_positions.append(position)
                except ValueError:
                    messagebox.showwarning("提示", f"{position} 的投注金额无效")
                    return
        
        if not has_selections:
            messagebox.showwarning("提示", "请至少选择一个位置和号码")
            return
        
        # 检查余额
        try:
            balance = float(self.balance)
            if total_bet_amount > balance:
                messagebox.showwarning("提示", f"余额不足\n当前余额: {balance}\n总投注金额: {total_bet_amount:.2f}")
                return
        except ValueError:
            pass  # 如果余额不是数字，忽略余额检查
        
        # 构建投注内容的描述信息
        bet_description = ""
        for position in bet_positions:
            selected_numbers = self.position_data[position]["selected_numbers"]
            amount = self.position_data[position]["amount"].get()
            bet_description += f"{position}: {', '.join(selected_numbers)} @ {amount}\n"
        
        # 确认投注
        confirm = messagebox.askyesno("确认投注", 
                                     f"投注详情:\n{bet_description}\n"
                                     f"总金额: {total_bet_amount:.2f}")
        if not confirm:
            return
        
        # 发送投注请求
        try:
            self.logger.info("尝试获取当前期号...")
            
            # 首先从 API 接口获取当前期号
            self.logger.info("尝试从 API 接口获取当前期号...")
            current_issue = ""
            
            # 使用用户提供的新 API 接口获取期号
            try:
                # 生成正确的毫秒级时间戳
                # 使用当前时间生成毫秒级时间戳
                current_time = datetime.datetime.now()
                timestamp = int(current_time.timestamp() * 1000)
                self.logger.info(f"当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S.%f')}") 
                self.logger.info(f"生成的时间戳: {timestamp}")
                
                # 确保 URL 中的域名和端口正确
                api_url = f"{BASE_URL}/lotData/getNewPeriod.do?code=JSSC168&_={timestamp}"
                self.logger.info(f"请求 API URL: {api_url}")
                
                # 添加请求头
                headers = {
                    'Accept': 'application/json, text/javascript, */*; q=0.01',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'X-Requested-With': 'XMLHttpRequest',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                }
                
                response = self.session.get(api_url, headers=headers, verify=False, timeout=10)
                response.raise_for_status()
                
                # 保存响应以便分析
                with open("api_response.json", "w", encoding="utf-8") as f:
                    f.write(response.text)
                self.logger.info("已保存 API 响应到 api_response.json")
                
                # 解析 JSON 响应
                try:
                    data = response.json()
                    self.logger.info(f"API 响应数据类型: {type(data).__name__}")
                    
                    # 根据实际响应格式提取期号
                    self.logger.info(f"API 响应数据: {data}")
                    
                    # 当前期号在 fnumberofperiod 字段中
                    if 'fnumberofperiod' in data:
                        current_issue = data['fnumberofperiod']
                        self.logger.info(f"从 API 响应的 fnumberofperiod 字段获取当前期号: {current_issue}")
                        
                        # 显示当前期号相关信息
                        if 'fpreviousperiod' in data and 'fpreviousresult' in data:
                            previous_period = data['fpreviousperiod']
                            previous_result = data['fpreviousresult']
                            self.logger.info(f"上一期({previous_period})开奖号码: {previous_result}")
                        
                        # 显示当前期状态
                        if 'fstatus' in data:
                            status = data['fstatus']
                            status_text = "未知状态"
                            if status == 1:
                                status_text = "投注中"
                            elif status == 2:
                                status_text = "已封盘"
                            self.logger.info(f"当前期状态: {status_text} (fstatus={status})")
                    
                    # 如果当前期已封盘，使用下一期号
                    if 'fstatus' in data and data['fstatus'] == 2 and 'fnextperiod' in data:
                        current_issue = data['fnextperiod']
                        self.logger.info(f"当前期已封盘，使用下一期号: {current_issue}")
                    # 如果没有获取到当前期号，尝试使用其他字段
                    elif not current_issue and 'fnextperiod' in data:
                        current_issue = data['fnextperiod']
                        self.logger.info(f"从 API 响应的 fnextperiod 字段获取下一期号: {current_issue}")
                    elif not current_issue and 'fpreviousperiod' in data:
                        current_issue = data['fpreviousperiod']
                        self.logger.info(f"从 API 响应的 fpreviousperiod 字段获取上一期号: {current_issue}")
                    elif not current_issue and 'fsettlenumber' in data:
                        current_issue = data['fsettlenumber']
                        self.logger.info(f"从 API 响应的 fsettlenumber 字段获取结算期号: {current_issue}")
                    
                    # 如果没有找到特定字段，尝试遍历所有字段查找期号样式的值
                    if not current_issue:
                        for key, value in data.items():
                            if isinstance(value, str) and re.match(r'^\d{8,}$', value):
                                current_issue = value
                                self.logger.info(f"从 API 响应的 {key} 字段获取期号: {current_issue}")
                                break
                except ValueError as e:
                    self.logger.info(f"无法解析 API 响应为 JSON: {str(e)}")
                    
                # 如果还是没有找到期号，尝试从响应内容中提取
                if not current_issue:
                    # 尝试从响应内容中使用正则表达式提取期号
                    period_match = re.search(r'\"qiHao\"\s*:\s*\"([\d]+)\"', response.text)
                    if period_match:
                        current_issue = period_match.group(1)
                        self.logger.info(f"从响应内容中提取期号: {current_issue}")
            except Exception as e:
                self.logger.info(f"从 API 接口获取期号时出错: {str(e)}")
            
            # 如果 API 接口获取失败，尝试从游戏信息中获取
            if not current_issue:
                if hasattr(self, 'game_info') and self.game_info and self.game_info.current_period:
                    current_issue = self.game_info.current_period
                    self.logger.info(f"从游戏信息中获取期号: {current_issue}")
            
            # 如果游戏信息中没有期号，尝试从游戏页面获取
            if not current_issue or current_issue == "--":
                self.logger.info("从游戏页面获取期号...")
                
                # 添加请求头模拟浏览器
                headers = {
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                    'Accept-Charset': 'utf-8,gbk;q=0.9,*;q=0.8',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                }
                
                game_url = urljoin(BASE_URL, "offcial/index.do?code=JSSC168#5.0.0")
                try:
                    response = self.session.get(game_url, headers=headers, verify=False, timeout=10)
                    response.raise_for_status()
                    
                    # 保存响应以便分析
                    with open("game_page_response.html", "w", encoding="utf-8") as f:
                        f.write(response.text)
                    self.logger.info("已保存游戏页面响应到 game_page_response.html")
                    
                    # 解析HTML获取期号
                    soup = BeautifulSoup(response.text, 'html.parser')
                    
                    # 根据用户提供的路径获取期号
                    # class="openinfo" 下的 class="drawNumber" 下的 class="c-period" 的 data-bind="text:periodInfo().fnumberofperiod"
                    self.logger.info("尝试从用户指定的路径获取期号...")
                    
                    # 首先尝试用户指定的路径
                    try:
                        # 找到 openinfo 类
                        openinfo = soup.find('div', class_='openinfo')
                        if openinfo:
                            # 找到 drawNumber 类
                            draw_number = openinfo.find('div', class_='drawNumber')
                            if draw_number:
                                # 找到 c-period 类
                                c_period = draw_number.find('div', class_='c-period')
                                if c_period:
                                    # 找到具有指定 data-bind 的元素
                                    period_elem = c_period.find(attrs={'data-bind': 'text:periodInfo().fnumberofperiod'})
                                    if period_elem:
                                        current_issue = period_elem.get_text(strip=True)
                                        self.logger.info(f"从用户指定路径找到期号: {current_issue}")
                    except Exception as e:
                        self.logger.info(f"从用户指定路径获取期号时出错: {str(e)}")
                    
                    # 如果从用户指定路径没有找到，尝试其他方法
                    if not current_issue:
                        # 直接尝试找到所有可能的 data-bind 属性
                        elements_with_data_bind = soup.find_all(attrs={'data-bind': lambda x: x and 'periodInfo' in x})
                        for elem in elements_with_data_bind:
                            text = elem.get_text(strip=True)
                            if text and text.isdigit() and len(text) > 5:  # 期号通常是较长的数字
                                current_issue = text
                                self.logger.info(f"从 data-bind 属性找到期号: {current_issue}")
                                break
                    
                    # 如果还是没找到，尝试其他选择器
                    if not current_issue:
                        selectors = [
                            '.issue',
                            '[data-bind*="text: issue"]',
                            '.period',
                            '.period-number',
                            '.lottery-period',
                            '.lottery-issue',
                            '.qihao',
                            '#currentIssue',
                            '[data-issue]'
                        ]
                        
                        for selector in selectors:
                            issue_elem = soup.select_one(selector)
                            if issue_elem:
                                current_issue = issue_elem.get_text(strip=True)
                                self.logger.info(f"使用选择器 {selector} 找到期号: {current_issue}")
                                break
                    
                    # 如果还是找不到，使用正则表达式匹配
                    if not current_issue:
                        patterns = [
                            r'"issue"\s*:\s*"([^"]+)"',
                            r'"qihao"\s*:\s*"([^"]+)"',
                            r'"period"\s*:\s*"([^"]+)"',
                            r'\bqihao\s*=\s*[\'"]([\d]+)[\'"]\s',
                            r'\bissue\s*=\s*[\'"]([\d]+)[\'"]\s',
                            r'\bperiod\s*=\s*[\'"]([\d]+)[\'"]\s'
                        ]
                        
                        for pattern in patterns:
                            issue_match = re.search(pattern, response.text)
                            if issue_match:
                                current_issue = issue_match.group(1)
                                self.logger.info(f"使用正则表达式 {pattern} 找到期号: {current_issue}")
                                break
                except Exception as e:
                    self.logger.info(f"获取期号时出错: {str(e)}")
            
            # 如果还是没有期号，使用当前时间生成一个期号
            if not current_issue:
                # 使用当前时间生成一个期号
                now = datetime.now()
                current_issue = f"{now.year}{now.month:02d}{now.day:02d}{now.hour:02d}{now.minute:02d}"
                self.logger.info(f"使用当前时间生成期号: {current_issue}")
            
            self.logger.info(f"最终使用的期号: {current_issue}")
                
            # 构建投注数据
            order_list = []
            
            # 为每个位置添加投注项
            for position in bet_positions:
                position_index = self.positions.index(position) + 1
                position_code = f"dwd_{position_index}"  # 位置代码，如 dwd_1 表示冠军
                
                selected_numbers = self.position_data[position]["selected_numbers"]
                base_amount = float(self.position_data[position]["amount"].get())
                
                # 考虑倍数和元角分模式
                try:
                    multiple_value = int(self.multiple.get())
                    unit_value = float(self.bet_unit.get())
                except (ValueError, AttributeError):
                    multiple_value = 1
                    unit_value = 1.0
                
                # 计算实际金额 = 基础金额 * 倍数 * 单位值(元/角/分)
                amount = base_amount * multiple_value * unit_value
                
                for num in selected_numbers:
                    # 确保号码是两位数字格式，如 01, 02 而不是 1, 2
                    num_formatted = num.zfill(2) if len(num) == 1 else num
                    
                    order_item = {
                        "i": position_code,    # 位置代码，如 dwd_1
                        "c": num_formatted,   # 选中的号码，保证两位数字格式
                        "n": 1,               # 数量
                        "t": 1,               # 类型
                        "k": 0,               # 未知参数
                        "m": multiple_value,  # 倍数
                        "a": amount           # 金额
                    }
                    order_list.append(order_item)
            
            # 构建最终请求数据
            bet_data = {
                "code": "JSSC168",
                "qiHao": current_issue,
                "orderList": order_list,
                "gameType": "official",  # 添加游戏类型
                "source": "0",          # 添加来源信息
                "terminal": "1"         # 添加终端信息
            }
            
            # 发送投注请求
            bet_url = urljoin(BASE_URL, "offcial/doBet.do")
            
            # 将orderList转换为JSON字符串，然后进行URL编码
            import json
            from urllib.parse import urlencode
            
            # 将orderList转换为JSON字符串
            order_list_json = json.dumps(order_list)
            
            # 构建URL编码的表单数据
            form_data = {
                "code": "JSSC168",
                "qiHao": current_issue,
                "orderList": order_list_json,
                "gameType": "official",
                "source": "0",
                "terminal": "1"
            }
            
            # 设置正确的Content-Type头
            headers = {
                "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8",
                "X-Requested-With": "XMLHttpRequest"
            }
            
            self.update_status(f"正在提交投注: {bet_description}")
            self.logger.info(f"发送投注请求 (URL编码): {form_data}")
            
            # 使用data参数发送URL编码的表单数据
            response = self.session.post(bet_url, data=urlencode(form_data), headers=headers, verify=False)
            self.logger.info(f"投注请求URL: {bet_url}")
            self.logger.info(f"投注请求头: {headers}")
            self.logger.info(f"投注请求体: {urlencode(form_data)}")
            response.raise_for_status()
            
            try:
                result = response.json()
                self.logger.info(f"投注响应: {result}")
                
                if result.get("success"):
                    # 投注成功
                    self.update_status(f"投注成功: 总金额 {total_bet_amount:.2f}")
                    messagebox.showinfo("成功", f"投注成功!\n{bet_description}\n总金额: {total_bet_amount:.2f}")
                    
                    # 刷新余额和投注记录
                    self.refresh_balance()
                    self.load_order_history()
                    
                    # 清除所有位置的选择
                    for position in bet_positions:
                        self.clear_numbers(position)
                else:
                    # 投注失败
                    error_msg = result.get("msg", "未知错误")
                    self.update_status(f"投注失败: {error_msg}")
                    messagebox.showerror("错误", f"投注失败: {error_msg}")
            except ValueError:
                # 响应不是JSON格式
                self.logger.info(f"非JSON响应: {response.text[:200]}...")
                messagebox.showerror("错误", "服务器响应格式不正确")
                
        except Exception as e:
            self.logger.info(f"投注失败: {str(e)}")
            self.update_status(f"投注失败: {str(e)}")
            messagebox.showerror("错误", f"投注失败: {str(e)}")


    def load_trial_account(self):
        """加载试玩账号信息"""
        try:
            # 更新状态
            self.update_status("正在获取试玩账号...")
            
            # 访问试玩页面
            trial_url = f"{BASE_URL}/normalTrialPlay.do"
            self.logger.info(f"正在请求试玩页面: {trial_url}")
            
            # 添加请求头来模拟浏览器行为
            headers = {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
                'Accept-Charset': 'utf-8,gbk;q=0.9,*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Cache-Control': 'max-age=0',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            # 先访问首页再访问试玩页面，确保会话有效
            try:
                self.session.get(BASE_URL, headers=headers, verify=False, timeout=10)
                self.logger.info("已访问首页，准备访问试玩页面")
            except Exception as e:
                self.logger.info(f"访问首页时出错: {str(e)}")
            
            # 尝试三次请求，增强稳定性
            max_retries = 3
            for retry in range(max_retries):
                try:
                    response = self.session.get(trial_url, headers=headers, verify=False, timeout=15)
                    if response.status_code == 200:
                        self.logger.info(f"成功请求试玩页面，状态码: {response.status_code}")
                        break
                    else:
                        self.logger.info(f"请求试玩页面返回非200状态码: {response.status_code}")
                        if retry < max_retries - 1:
                            time.sleep(1)  # 等待一秒后重试
                except Exception as e:
                    self.logger.info(f"请求试玩页面时出错 (重试 {retry+1}/{max_retries}): {str(e)}")
                    if retry < max_retries - 1:
                        time.sleep(1)  # 等待一秒后重试
            
            # 尝试不同的编码方式解析响应
            encodings = ['utf-8', 'gbk', 'gb2312', 'gb18030', 'big5']
            response_text = None
            
            for encoding in encodings:
                try:
                    response_text = response.content.decode(encoding)
                    self.logger.info(f"使用 {encoding} 编码成功解析响应")
                    break
                except UnicodeDecodeError:
                    continue
            
            if response_text is None:
                # 如果所有编码都失败，使用默认的响应文本
                response_text = response.text
                self.logger.info("所有编码尝试失败，使用默认响应文本")
            
            # 保存响应以便分析
            with open("trial_page_response.html", "w", encoding="utf-8") as f:
                f.write(response_text)
            self.logger.info("已保存试玩页面响应到 trial_page_response.html")
            
            # 解析响应
            soup = BeautifulSoup(response_text, 'html.parser')
            
            # 检查页面标题
            title = soup.title.text if soup.title else ""
            self.logger.info(f"页面标题: {title}")
            
            # 检查是否需要登录
            login_form = soup.find('form', {'name': 'loginform'}) or soup.find('form', {'id': 'loginform'})
            if login_form:
                self.logger.info("检测到登录表单，可能需要重新登录")
                # 尝试重新登录
                if self.app and hasattr(self.app, 'login_tab') and self.app.login_tab:
                    self.logger.info("尝试重新登录...")
                    self.update_status("会话过期，尝试重新登录...")
                    # 尝试重新登录并再次获取试玩账号
                    messagebox.showinfo("提示", "会话已过期，需要重新登录")
                    return False
            
            # 重置账号和余额信息
            self.account = ""
            self.balance = ""
            
            # 方法1: 使用正则表达式直接从文本中提取
            self.logger.info("尝试使用正则表达式提取账号和余额信息...")
            account_match = re.search(r'账\s*号[\s\:\-]*\s*([A-Za-z0-9]+)', response_text)
            balance_match = re.search(r'余\s*额[\s\:\-]*\s*([0-9,.]+)', response_text)
            
            if account_match:
                self.account = account_match.group(1).strip()
                self.logger.info(f"使用正则表达式找到账号: {self.account}")
            
            if balance_match:
                self.balance = balance_match.group(1).strip()
                self.logger.info(f"使用正则表达式找到余额: {self.balance}")
            
            # 方法2: 尝试查找用户信息区域
            if not self.account or not self.balance:
                self.logger.info("尝试从用户信息区域提取账号和余额信息...")
                user_info_divs = soup.find_all(['div', 'span', 'p'], class_=lambda c: c and ('user' in c.lower() or 'account' in c.lower() or 'balance' in c.lower()))
                
                for div in user_info_divs:
                    div_text = div.get_text()
                    account_match = re.search(r'账\s*号[\s\:\-]*\s*([A-Za-z0-9]+)', div_text)
                    balance_match = re.search(r'余\s*额[\s\:\-]*\s*([0-9,.]+)', div_text)
                    
                    if account_match and not self.account:
                        self.account = account_match.group(1).strip()
                        self.logger.info(f"从用户信息区域找到账号: {self.account}")
                    
                    if balance_match and not self.balance:
                        self.balance = balance_match.group(1).strip()
                        self.logger.info(f"从用户信息区域找到余额: {self.balance}")
            
            # 方法3: 尝试查找顶部导航栏中的用户信息
            if not self.account or not self.balance:
                self.logger.info("尝试从顶部导航栏提取账号和余额信息...")
                header_elements = soup.find_all(['div', 'header', 'nav'], class_=lambda c: c and ('header' in c.lower() or 'nav' in c.lower() or 'top' in c.lower()))
                
                for header in header_elements:
                    header_text = header.get_text()
                    account_match = re.search(r'账\s*号[\s\:\-]*\s*([A-Za-z0-9]+)', header_text)
                    balance_match = re.search(r'余\s*额[\s\:\-]*\s*([0-9,.]+)', header_text)
                    
                    if account_match and not self.account:
                        self.account = account_match.group(1).strip()
                        self.logger.info(f"从顶部导航栏找到账号: {self.account}")
                    
                    if balance_match and not self.balance:
                        self.balance = balance_match.group(1).strip()
                        self.logger.info(f"从顶部导航栏找到余额: {self.balance}")
            
            # 如果还是没有找到，尝试查找表格中的信息
            if not self.account or not self.balance:
                tables = soup.find_all('table')
                for table in tables:
                    rows = table.find_all('tr')
                    for row in rows:
                        cells = row.find_all(['td', 'th'])
                        for i, cell in enumerate(cells):
                            cell_text = cell.get_text(strip=True)
                            if '账号' in cell_text and i + 1 < len(cells):
                                self.account = cells[i + 1].get_text(strip=True)
                                self.logger.info(f"从表格中找到账号: {self.account}")
                            if '余额' in cell_text and i + 1 < len(cells):
                                self.balance = cells[i + 1].get_text(strip=True)
                                self.logger.info(f"从表格中找到余额: {self.balance}")
            
            # 更新UI显示
            if self.account:
                self.account_label.config(text=self.account)
            if self.balance:
                self.balance_label.config(text=self.balance)
                
            # 设置已加载试玩账号标志
            self.has_loaded_trial = True
                
            # 如果成功获取到账号和余额
            if self.account and self.balance:
                self.logger.info("成功获取试玩账号信息")
                self.update_status(f"试玩账号: {self.account}, 余额: {self.balance}")
                messagebox.showinfo("成功", "成功获取试玩账号信息")
                
                # 加载投注记录
                self.load_order_history()
                
                # 询问是否进入游戏
                if messagebox.askyesno("提示", "是否立即进入游戏页面？"):
                    self.go_to_game()
            else:
                self.logger.info(f"未能获取到完整的试玩账号信息，\n页面提示: {title}")
                self.update_status("获取试玩账号失败")
                messagebox.showwarning("提示", f"未能获取到完整的试玩账号信息，请检查网络连接或者联系客服\n页面提示: {title}")
        except Exception as e:
            self.logger.info(f"加载试玩账号信息失败: {str(e)}")
            self.update_status("获取试玩账号失败")
            messagebox.showerror("错误", f"加载试玩账号信息失败: {str(e)}")
            return False

    def load_order_history(self):
        """加载投注记录数据"""
        try:
            # 清空现有数据
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # 访问试玩页面获取投注记录
            trial_url = urljoin(BASE_URL, "normalTrialPlay.do")
            self.logger.info("正在获取投注记录...")
            response = self.session.get(trial_url, verify=False)
            response.raise_for_status()
            
            # 解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 调试：检查页面内容
            self.logger.info("检查页面内容...")
            self.logger.info(f"页面标题: {soup.title.string if soup.title else '无标题'}")
            
            # 尝试多种选择器查找表格
            table = soup.select_one('.table-list table')
            if not table:
                self.logger.info("使用.table-list table选择器未找到表格")
                table = soup.select_one('table')  # 尝试查找任何表格
                if table:
                    self.logger.info("找到通用表格，检查结构...")
                    self.logger.info(f"表格HTML: {str(table)[:200]}...")  # 记录前200个字符
            
            if not table:
                self.logger.info("未找到投注记录表格")
                messagebox.showinfo("提示", "未找到投注记录表格，可能还没有投注记录")
                return
                
            # 查找表格中的所有行
            tbody = table.find('tbody')
            if not tbody:
                self.logger.info("表格中未找到tbody")
                tbody = table  # 有些表格可能没有tbody
                
            rows = tbody.find_all('tr', recursive=False)
            self.logger.info(f"找到 {len(rows)} 行数据")
            
            if not rows:
                self.logger.info("没有找到投注记录")
                messagebox.showinfo("提示", "没有找到投注记录")
                return
                
            # 处理每一行数据
            for row in rows:
                try:
                    # 提取每个单元格的数据
                    cells = row.find_all(['td', 'th'])
                    self.logger.info(f"行包含 {len(cells)} 个单元格")
                    
                    if len(cells) >= 10:  # 确保有足够的单元格
                        order_data = {
                            'order_id': cells[1].get_text(strip=True),
                            'game_type': cells[2].get_text(strip=True),
                            'issue': cells[3].get_text(strip=True),
                            'bet_time': cells[4].get_text(strip=True),
                            'bet_content': cells[5].get_text(strip=True),
                            'odds': cells[6].get_text(strip=True),
                            'amount': cells[7].get_text(strip=True),
                            'bonus': cells[8].get_text(strip=True),
                            'status': cells[9].get_text(strip=True)
                        }
                        
                        # 添加到树形视图
                        self.tree.insert("", "end", values=(
                            order_data['order_id'],
                            order_data['game_type'],
                            order_data['issue'],
                            order_data['bet_time'],
                            order_data['bet_content'],
                            order_data['odds'],
                            order_data['amount'],
                            order_data['bonus'],
                            order_data['status']
                        ))
                except Exception as e:
                    self.logger.info(f"处理行时出错: {str(e)}")
                    continue
            
            self.logger.info(f"成功加载 {len(rows)} 条投注记录")
            
        except Exception as e:
            self.logger.info(f"加载投注记录失败: {str(e)}")
            messagebox.showerror("错误", f"加载投注记录失败: {str(e)}")

    def go_to_game(self):
        """Navigate to the game page"""
        try:
            game_url = urljoin(BASE_URL, "offcial/index.do?code=JSSC168#5.0.0")
            response = self.session.get(game_url, verify=False)
            response.raise_for_status()
            
            # Save the game page for debugging
            with open("trial_game_response.html", "w", encoding="utf-8") as f:
                f.write(response.text)
            
            # 刷新游戏信息
            self.refresh_game_info()
                
            self.logger.info("成功进入游戏页面")
            messagebox.showinfo("成功", "已进入游戏页面")
            
        except Exception as e:
            self.logger.info(f"进入游戏页面失败: {str(e)}")
            messagebox.showerror("错误", f"进入游戏页面失败: {str(e)}")
        
        # 启动游戏信息自动更新
        self.start_game_info_auto_update()

    def refresh_game_info(self):
        """刷新游戏信息（手动刷新）"""
        try:
            # 直接使用API接口获取游戏信息，指定为手动刷新
            self.update_game_info_from_api(manual_refresh=True)
            
            self.logger.info("游戏信息刷新成功")
        except Exception as e:
            self.logger.info(f"刷新游戏信息时出错: {str(e)}")
            
            # 如果直接使用API失败，尝试使用原来的方法
            try:
                # 刷新游戏信息
                self.game_info.refresh()
                
                # 更新UI
                self.update_game_info_ui()
                
                self.logger.info("使用原来的方法刷新游戏信息成功")
            except Exception as e:
                self.logger.info(f"使用原来的方法刷新游戏信息时出错: {str(e)}")
        except Exception as e:
            self.logger.info(f"刷新游戏信息时出错: {str(e)}")

    def update_game_info_from_api(self, manual_refresh=False):
        """使用API接口数据更新游戏信息
        
        Args:
            manual_refresh: 是否是手动刷新，如果是手动刷新则更新游戏名称
            
        Returns:
            bool: 是否成功更新游戏信息
        """
        try:
            # ========== 新增：仅通过API获取游戏名称，避免HTML解析 ===========
            # 只在手动刷新或首次初始化时刷新游戏名称，避免频繁请求
            if manual_refresh or not self.game_info.game_name or self.game_info.game_name == "未知游戏":
                # 调用GameInfo对象的fetch_game_info方法（该方法已实现API获取游戏名）
                self.game_info.fetch_game_info()
                self.logger.info(f"已通过API刷新游戏名称: {self.game_info.game_name}")
            # ==========================================================
            # 生成时间戳
            current_time = datetime.datetime.now()
            timestamp = int(current_time.timestamp() * 1000)
            self.logger.info(f"当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S.%f')}")
            
            # 构建 API URL
            api_url = f"{BASE_URL}/lotData/getNewPeriod.do?code=JSSC168&_={timestamp}"
            self.logger.info(f"请求游戏信息 API URL: {api_url}")
            
            # 添加请求头
            headers = {
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'X-Requested-With': 'XMLHttpRequest',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }
            
            # 发送请求
            response = self.session.get(api_url, headers=headers, verify=False, timeout=10)
            response.raise_for_status()
            
            # 解析响应
            data = response.json()
            # 只输出关键信息，避免日志过多
            self.logger.info(f"游戏信息 API 获取成功，当前期号: {data.get('fnumberofperiod', '--')}, 状态: {data.get('fstatus', '--')}")
            
            # 更新游戏信息
            # ========= 游戏名获取逻辑增强 =========
            game_name = None
            # 1. 优先用API字段
            if 'gameName' in data and data['gameName']:
                game_name = data['gameName']
                self.logger.info(f"通过API字段gameName获取游戏名: {game_name}")
            elif 'game_name' in data and data['game_name']:
                game_name = data['game_name']
                self.logger.info(f"通过API字段game_name获取游戏名: {game_name}")
            # 2. API无名时，解析页面<div class="game_info"><div class="game_mane "><span>...
            if not game_name:
                try:
                    game_url = urljoin(BASE_URL, "offcial/index.do?code=JSSC168#5.0.0")
                    html_resp = self.session.get(game_url, verify=False, timeout=10)
                    html_resp.raise_for_status()
                    soup = BeautifulSoup(html_resp.text, 'html.parser')
                    game_info_div = soup.find('div', class_='game_info')
                    if game_info_div:
                        game_mane_div = game_info_div.find('div', class_='game_mane ')
                        if game_mane_div:
                            span = game_mane_div.find('span')
                            if span and span.text.strip():
                                game_name = span.text.strip()
                                self.logger.info(f"通过HTML .game_mane span获取游戏名: {game_name}")
                            else:
                                self.logger.info("未找到 .game_mane span，无法通过HTML获取游戏名")
                        else:
                            self.logger.info("未找到 .game_mane 区域，无法通过HTML获取游戏名")
                    else:
                        self.logger.info("未找到 .game_info 区域，无法通过HTML获取游戏名")
                except Exception as e:
                    self.logger.info(f"解析页面结构获取游戏名出错: {str(e)}")
            # 3. 兜底用code字段
            if not game_name and 'code' in data:
                game_code = data['code']
                if game_code == "JSSC168":
                    game_name = "168极速赛车"
                    self.logger.info("通过code字段JSSC168，设置游戏名为168极速赛车")
                else:
                    game_name = game_code
                    self.logger.info(f"通过code字段设置游戏名: {game_name}")
            if not game_name:
                game_name = "168极速赛车*"
                self.logger.info("最终game_name为空，强制赋值为'未知游戏'")
            else:
                self.logger.info(f"最终用于显示的游戏名: {game_name}")
            # ========= 游戏名获取逻辑 END =========

            # 判断是否需要更新界面（手动刷新或界面无名）
            current_name = self.game_name_label.cget("text")
            if manual_refresh or not current_name or current_name == "--":
                self.game_name_label.config(text=game_name)
                self.game_info.game_name = game_name

            
            # 更新期号信息
            if 'fnumberofperiod' in data:
                current_period = data['fnumberofperiod']
                self.period_label.config(text=f"第 {current_period} 期")
                # 更新游戏信息对象中的期号
                self.game_info.current_period = current_period
            
            # 更新期号状态
            if 'fstatus' in data:
                status = data['fstatus']
                status_text = "未知状态"
                if status == 1:
                    status_text = "投注中"
                elif status == 2:
                    status_text = "已封盘"
                self.period_status_label.config(text=status_text)
                # 更新游戏信息对象中的期号状态
                self.game_info.period_status = status_text
            
            # 更新倒计时
            remaining_time = "未知"
            self.remaining_seconds = 0  # 初始化剩余秒数
            
            if 'fclosetime' in data and 'ServerTime' in data:
                try:
                    # 解析时间
                    close_time = datetime.datetime.strptime(data['fclosetime'], "%Y/%m/%d %H:%M:%S")
                    server_time = datetime.datetime.strptime(data['ServerTime'], "%Y/%m/%d %H:%M:%S")
                    
                    # 计算剩余时间
                    time_diff = (close_time - server_time).total_seconds()
                    if time_diff > 0:
                        # 保存剩余秒数供倒计时使用
                        self.remaining_seconds = int(time_diff)
                        self.logger.info(f"获取到剩余投注时间: {self.remaining_seconds} 秒")
                        
                        minutes = int(time_diff // 60)
                        seconds = int(time_diff % 60)
                        remaining_time = f"{minutes:02d}:{seconds:02d}"
                    else:
                        remaining_time = "00:00"
                        self.remaining_seconds = 0
                except Exception as e:
                    self.logger.info(f"计算倒计时时出错: {str(e)}")
                    remaining_time = "--:--"
                    self.remaining_seconds = 0
            
            # 只有当没有正在运行的倒计时线程时才更新显示
            if not hasattr(self, 'countdown_running') or not self.countdown_running:
                self.countdown_label.config(text=remaining_time)
            
            # 更新游戏信息对象中的倒计时
            self.game_info.remaining_time_str = remaining_time
            
            # 显示上一期开奖号码
            if 'fpreviousperiod' in data and 'fpreviousresult' in data:
                previous_period = data['fpreviousperiod']
                previous_result = data['fpreviousresult']
                self.logger.info(f"上一期({previous_period})开奖号码: {previous_result}")
                
                # 更新上一期期号显示
                self.previous_period_label.config(text=previous_period)
                
                # 将开奖号码分割为列表
                numbers = previous_result.split(',')
                
                # 更新开奖号码显示，使用空格分隔以便于阅读
                formatted_result = ' '.join(numbers)
                self.previous_result_label.config(text=formatted_result)
                
                # 将开奖号码保存到游戏信息对象中
                self.game_info.previous_result = previous_result
                self.game_info.previous_period = previous_period
            
            self.logger.info("使用 API 数据更新游戏信息成功")
            return True
        except Exception as e:
            self.logger.info(f"使用 API 数据更新游戏信息时出错: {str(e)}")
            return False
            
    def update_game_info_ui(self):
        """更新游戏信息UI"""
        try:
            # 更新游戏名称
            self.game_name_label.config(text=self.game_info.game_name)
            
            # 更新游戏图标
            if self.game_info.game_icon:
                try:
                    # 尝试从服务器获取图标
                    icon_url = urljoin(BASE_URL, self.game_info.game_icon)
                    response = self.session.get(icon_url, verify=False)
                    response.raise_for_status()
                    
                    # 将图像数据转换为PIL图像
                    image_data = response.content
                    self.game_icon_image = Image.open(io.BytesIO(image_data))
                    
                    # 调整图像大小
                    self.game_icon_image = self.game_icon_image.resize((40, 40), Image.LANCZOS)
                    
                    # 转换为Tkinter可用的格式
                    self.game_icon_photo = ImageTk.PhotoImage(self.game_icon_image)
                    
                    # 更新标签
                    self.game_icon_label.config(image=self.game_icon_photo)
                except Exception as e:
                    self.logger.info(f"加载游戏图标失败: {str(e)}")
            
            # 更新期号信息
            if self.game_info.current_period:
                self.period_label.config(text=f"第 {self.game_info.current_period} 期")
            
            # 更新期号状态
            self.period_status_label.config(text=self.game_info.period_status)
            
            # 更新倒计时
            self.countdown_label.config(text=self.game_info.remaining_time_str)
            
            # 更新官方开奖网链接
            self.official_site_link.config(text="官方开奖网")
            
            self.logger.info("游戏信息UI更新成功")
        except Exception as e:
            self.logger.info(f"更新游戏信息UI时出错: {str(e)}")

    def open_official_site(self, event):
        """打开官方开奖网站"""
        import webbrowser
        try:
            if self.game_info.official_site:
                webbrowser.open(self.game_info.official_site)
                self.logger.info(f"打开官方开奖网站: {self.game_info.official_site}")
        except Exception as e:
            self.logger.info(f"打开官方开奖网站失败: {str(e)}")
            messagebox.showerror("错误", f"打开官方开奖网站失败: {str(e)}")

    def start_game_info_auto_update(self):
        """启动游戏信息自动更新（使用after定时器，避免多线程/多次刷新）"""
        try:
            # 先直接使用API更新一次游戏信息
            self.update_game_info_from_api()
            # 启动after定时器，保证只有一个刷新任务
            if hasattr(self, '_game_info_job') and self._game_info_job:
                self.root.after_cancel(self._game_info_job)
            self._game_info_job = self.root.after(1000, self._game_info_update_tick)
            self.logger.info("启动API游戏信息自动更新（after定时器）")
            # 作为备用，仍然保留原来的更新机制
            self.game_info.register_update_callback(self.on_game_info_updated)
            self.game_info.start_auto_update()
            self.logger.info("启动游戏信息自动更新")
        except Exception as e:
            self.logger.info(f"启动游戏信息自动更新失败: {str(e)}")

    def _game_info_update_tick(self):
        """after定时器回调，定时刷新游戏信息"""
        try:
            self.update_game_info_from_api()
        except Exception as e:
            self.logger.info(f"游戏信息定时刷新出错: {str(e)}")
        # 保证只存在一个定时器
        self._game_info_job = self.root.after(1000, self._game_info_update_tick)


    def _api_update_task(self):
        """使用API更新游戏信息的后台任务"""
        try:
            self.logger.info("API游戏信息自动更新线程启动")
            default_update_interval = 5  # 默认更新间隔，秒
            
            while hasattr(self, 'api_update_running') and self.api_update_running:
                try:
                    # 使用API更新游戏信息
                    result = self.update_game_info_from_api()
                    
                    # 如果成功获取到剩余时间，使用剩余时间作为下次更新的间隔
                    if result and hasattr(self, 'remaining_seconds') and self.remaining_seconds > 0:
                        # 如果剩余时间大于0，启动倒计时
                        self.logger.info(f"使用剩余投注时间作为倒计时: {self.remaining_seconds} 秒")
                        
                        # 启动倒计时线程
                        self.start_countdown(self.remaining_seconds)
                        
                        # 等待倒计时结束
                        while hasattr(self, 'countdown_running') and self.countdown_running:
                            if not hasattr(self, 'api_update_running') or not self.api_update_running:
                                break
                            time.sleep(1)
                    else:
                        # 如果没有获取到剩余时间或剩余时间已经为0，使用默认间隔
                        self.logger.info(f"使用默认间隔更新游戏信息: {default_update_interval} 秒")
                        for _ in range(default_update_interval):
                            if not hasattr(self, 'api_update_running') or not self.api_update_running:
                                break
                            time.sleep(1)
                except Exception as e:
                    self.logger.info(f"API自动更新游戏信息时出错: {str(e)}")
                    # 出错时使用默认间隔
                    for _ in range(default_update_interval):
                        if not hasattr(self, 'api_update_running') or not self.api_update_running:
                            break
                        time.sleep(1)
            
            self.logger.info("API游戏信息自动更新线程结束")
        except Exception as e:
            self.logger.info(f"API游戏信息自动更新线程出错: {str(e)}")
            
    def start_countdown(self, seconds):
        """启动倒计时线程
        
        Args:
            seconds: 倒计时秒数
        """
        # 停止已有的倒计时线程
        if hasattr(self, 'countdown_running') and self.countdown_running:
            self.countdown_running = False
            if hasattr(self, 'countdown_thread') and self.countdown_thread:
                try:
                    self.countdown_thread.join(timeout=1)
                except Exception:
                    pass
        
        # 创建新的倒计时线程
        self.countdown_running = True
        self.countdown_thread = threading.Thread(target=self._countdown_task, args=(seconds,))
        self.countdown_thread.daemon = True
        self.countdown_thread.start()
        
    def start_countdown(self, seconds):
        """启动倒计时（只允许一个倒计时任务）"""
        if hasattr(self, '_countdown_job') and self._countdown_job:
            self.root.after_cancel(self._countdown_job)
        self._countdown_time = seconds
        self._countdown_job = self.root.after(1000, self._countdown_tick)

    def _countdown_tick(self):
        """倒计时每秒回调"""
        self._countdown_time -= 1
        # 更新UI显示
        minutes = self._countdown_time // 60
        seconds = self._countdown_time % 60
        time_str = f"{minutes:02d}:{seconds:02d}"
        self.countdown_label.config(text=time_str)
        if self._countdown_time > 0:
            self._countdown_job = self.root.after(1000, self._countdown_tick)
        else:
            self._countdown_job = None
            self.logger.info("倒计时结束")
            # 可在此处添加倒计时结束后的逻辑

    
    def stop_game_info_auto_update(self):
        """停止游戏信息自动更新（取消after定时器）"""
        try:
            # 停止after定时器
            if hasattr(self, '_game_info_job') and self._game_info_job:
                self.root.after_cancel(self._game_info_job)
                self._game_info_job = None
                self.logger.info("停止API游戏信息自动更新（after定时器）")
            # 停止原来的自动更新
            self.game_info.stop_auto_update()
            self.game_info.unregister_update_callback(self.on_game_info_updated)
            self.logger.info("停止游戏信息自动更新")
        except Exception as e:
            self.logger.info(f"停止游戏信息自动更新失败: {str(e)}")


    def on_game_info_updated(self, game_info, countdown_only=False):
        """游戏信息更新回调"""
        try:
            if countdown_only:
                # 只更新倒计时
                self.countdown_label.config(text=game_info.remaining_time_str)
            else:
                # 更新所有游戏信息
                self.update_game_info_ui()
        except Exception as e:
            self.logger.info(f"处理游戏信息更新回调时出错: {str(e)}")
    
    def adjust_multiple(self, delta):
        """调整倍数"""
        try:
            current = int(self.multiple.get())
            new_value = max(1, current + delta)  # 确保倍数至少为1
            self.multiple.set(str(new_value))
            self.update_total_amount()
        except ValueError:
            # 如果当前值不是有效的整数，重置为1
            self.multiple.set("1")
            self.update_total_amount()
    
    def set_multiple(self, value):
        """设置倍数"""
        self.multiple.set(value)
        self.update_total_amount()
    
    def show_multiple_menu(self, event):
        """显示倍数选择菜单"""
        try:
            self.multiple_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.multiple_menu.grab_release()
    
    def set_bet_unit(self, value, text):
        """设置投注单位（元/角/分）"""
        self.bet_unit.set(value)
        self.bet_unit_text.set(text)
        self.update_total_amount()
    
    def confirm_selection(self):
        """确认选号"""
        # 获取当前选中的号码和投注信息
        total_count = 0
        for position in self.positions:
            total_count += len(self.position_data[position]["selected_numbers"])
        
        if total_count == 0:
            messagebox.showinfo("提示", "请先选择号码")
            return
        
        # 显示确认信息
        total = float(self.total_amount.get())
        message = f"您已选择 {total_count} 注，共 {total:.2f} 元\n确认添加到投注列表吗？"
        if messagebox.askyesno("确认选号", message):
            self.update_status(f"已确认选号：{total_count} 注，共 {total:.2f} 元")
            # 这里可以添加将选号添加到投注列表的逻辑