#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import datetime
import pickle
import json
import os

# 全局配置
BASE_DOMAIN = "jvvpbv2580513aknu.com"
BASE_URL = f"https://jk7859.{BASE_DOMAIN}:59789"
SESSION_FILE = "session.pkl"
USER_INFO_FILE = "user_info.json"

# 创建调试日志函数
# 全局状态更新回调函数列表
status_update_callbacks = []

def register_status_callback(callback):
    """
    注册状态更新回调函数
    
    Args:
        callback: 接收消息字符串的函数
    """
    if callback not in status_update_callbacks:
        status_update_callbacks.append(callback)

def unregister_status_callback(callback):
    """
    取消注册状态更新回调函数
    
    Args:
        callback: 之前注册的回调函数
    """
    if callback in status_update_callbacks:
        status_update_callbacks.remove(callback)

def debug_log(message):
    """
    记录调试信息并更新状态
    
    Args:
        message: 要记录的消息
    """
    log_file = "login_debug.log"
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    with open(log_file, "a", encoding="utf-8") as f:
        f.write(f"[{timestamp}] {message}\n")
    print(message)
    
    # 调用所有注册的状态更新回调函数
    for callback in status_update_callbacks:
        try:
            callback(message)
        except Exception as e:
            print(f"调用状态更新回调时出错: {str(e)}")
            # 出错时不中断其他回调

# 保存会话到文件
def save_session(session, username, balance, user_links, is_logged_in):
    try:
        # 保存会话到文件
        with open(SESSION_FILE, "wb") as f:
            pickle.dump(session, f)
        debug_log(f"会话信息已保存到 {SESSION_FILE}")

        # 保存用户信息到文件
        user_info = {
            "username": username,
            "balance": balance,
            "user_links": user_links,
            "is_logged_in": is_logged_in
        }
        with open(USER_INFO_FILE, "w", encoding="utf-8") as f:
            json.dump(user_info, f, ensure_ascii=False)
        debug_log(f"用户信息已保存到 {USER_INFO_FILE}")
        return True
    except Exception as e:
        debug_log(f"保存会话信息时出错: {str(e)}")
        return False

# 加载会话从文件
def load_session():
    try:
        # 检查会话文件是否存在
        if not os.path.exists(SESSION_FILE) or not os.path.exists(USER_INFO_FILE):
            debug_log("没有找到会话文件，需要重新登录")
            return None, None, None, None, False

        # 加载会话
        with open(SESSION_FILE, "rb") as f:
            session_data = pickle.load(f)

        # 确保加载的是Session对象而不是RequestsCookieJar
        if not isinstance(session_data, requests.Session):
            debug_log(f"加载的会话对象类型不正确: {type(session_data)}, 创建新会话")
            session = requests.Session()
            # 如果有必要，可以从旧会话中复制cookies
            if hasattr(session_data, 'cookies'):
                session.cookies.update(session_data.cookies)
        else:
            session = session_data

        debug_log(f"从 {SESSION_FILE} 加载了会话信息")
        
        # 加载用户信息
        with open(USER_INFO_FILE, "r", encoding="utf-8") as f:
            user_info = json.load(f)
        
        username = user_info.get("username", "")
        balance = user_info.get("balance", "0")
        user_links = user_info.get("user_links", [])
        is_logged_in = user_info.get("is_logged_in", False)
        
        debug_log(f"从 {USER_INFO_FILE} 加载了用户信息: {username}")

        return session, username, balance, user_links, is_logged_in
    except Exception as e:
        debug_log(f"加载会话信息时出错: {str(e)}")
        return None, None, None, None, False

# 验证会话是否有效
def verify_session(session, is_logged_in):
    try:
        if not is_logged_in:
            return False
        
        # 访问首页检查登录状态
        index_url = f"{BASE_URL}/index.do"
        debug_log(f"验证会话，发送请求到: {index_url}")

        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36",
            "Referer": f"{BASE_URL}/"
        }

        response = session.get(index_url, headers=headers, verify=False, timeout=10)

        # 检查是否包含登录成功的标志
        import re
        patterns = [
            r'账\s*号',
            r'系统余额',
            r'退出登录',
            r'会员中心'
        ]

        for pattern in patterns:
            if re.search(pattern, response.text):
                debug_log(f"会话验证成功，找到登录标志: {pattern}")
                return True

        debug_log("会话验证失败，未找到登录标志")
        return False
    except Exception as e:
        debug_log(f"验证会话时出错: {str(e)}")
        return False

# 清除会话
def clear_session():
    try:
        if os.path.exists(SESSION_FILE):
            os.remove(SESSION_FILE)
        if os.path.exists(USER_INFO_FILE):
            os.remove(USER_INFO_FILE)
        debug_log("已删除会话文件")
        return True
    except Exception as e:
        debug_log(f"删除会话文件时出错: {str(e)}")
        return False
