#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin
import re
import threading
import time
from utils.logger import Logger
from core.session import SessionConfig

# 从SessionConfig获取BASE_URL
BASE_URL = SessionConfig.BASE_URL

class GameInfo:
    def __init__(self, session):
        """初始化游戏信息类"""
        # 创建日志记录器
        self.logger = Logger("GameInfo")
        
        self.session = session
        self.game_code = "JSSC168"  # 默认游戏代码
        self.game_name = "168极速赛车"  # 默认游戏名称
        self.game_icon = "/common/lot/images/gameIcon/JSSC168.png"  # 默认图标路径
        #self.official_site = "https://www.7859kjw15.bet:59789/live.do"  # 默认官方开奖网站
        self.official_site = "https://jk7859.mcu25805gtwqekeyw.com:59789/index.do"
        
        # 期号信息
        self.current_period = ""
        self.period_status = "等待下期开盘"
        
        # 倒计时信息
        self.remaining_time = 0  # 剩余时间（秒）
        self.remaining_time_str = "00:00:00"  # 格式化的剩余时间
        
        # 更新线程
        self.update_thread = None
        self.is_updating = False
        self.update_interval = 5  # 更新间隔（秒）
        
        # 回调函数
        self.on_update_callbacks = []
    
    def fetch_game_info(self):
        """从服务器获取游戏信息"""
        try:
            # 构建游戏页面URL
            game_url = urljoin(BASE_URL, f"offcial/index.do?code={self.game_code}#5.0.0")
            
            # 发送请求
            response = self.session.get(game_url, verify=False)
            response.raise_for_status()
            
            # 不再保存响应内容
            # self.logger.info("游戏页面响应获取成功")
            
            # 解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 尝试直接使用您提供的HTML代码
            game_info_div = soup.select_one('div.game_info')
            if not game_info_div:
                self.logger.warning("使用 div.game_info 选择器未找到游戏信息区域")
                
                # 尝试使用更宽松的选择器
                game_info_div = soup.find('div', class_='game_info')
                if not game_info_div:
                    self.logger.warning("使用 find('div', class_='game_info') 仍然未找到游戏信息区域")
                    
                    # 尝试使用部分匹配
                    game_info_div = soup.find('div', class_=lambda c: c and 'game_info' in c)
                    if not game_info_div:
                        self.logger.warning("使用部分匹配仍然未找到游戏信息区域")
                        
                        # 使用 '--' 作为占位符
                        self.game_name = "--"
                        self.game_icon = "/common/lot/images/gameIcon/JSSC168.png"  # 保留图标路径以显示默认图标
                        self.official_site = "--"
                        self.current_period = "--"
                        self.period_status = "--"
                        self.remaining_time = 0
                        self.remaining_time_str = "--:--:--"
                        
                        self.logger.info("使用硬编码的游戏信息")
                        return True
            
            self.logger.info("成功找到游戏信息区域")
            
            # 提取游戏图标
            game_ico_div = game_info_div.select_one('.game_ico img')
            if game_ico_div and game_ico_div.get('src'):
                self.game_icon = game_ico_div['src']
                self.logger.info(f"游戏图标: {self.game_icon}")
            
            # 提取游戏名称（修正：从 .game_mane 下的 span 获取）
            game_mane_div = game_info_div.select_one('div.game_mane')
            if game_mane_div:
                game_name_span = game_mane_div.find('span')
                if game_name_span:
                    self.game_name = game_name_span.text.strip()
                    self.logger.info(f"游戏名称: {self.game_name}")
                else:
                    self.logger.warning("未找到游戏名称的 <span> 标签")
            else:
                self.logger.warning("未找到 .game_mane 区域，无法获取游戏名称")

            
            # 提取官方开奖网站链接
            official_site_a = game_info_div.select_one('.game_mane a.lot-network')
            if official_site_a and official_site_a.get('href'):
                self.official_site = official_site_a['href']
                self.logger.info(f"官方开奖网站: {self.official_site}")
            
            # 提取期号信息
            period_b = game_info_div.select_one('.c-period b[data-bind="text:periodInfo().fnumberofperiod"]')
            if period_b:
                self.current_period = period_b.text.strip()
                self.logger.info(f"期号: {self.current_period}")
            else:
                # 尝试其他选择器
                period_b = game_info_div.select_one('.drawNumber .c-period b')
                if period_b:
                    self.current_period = period_b.text.strip()
                    self.logger.info(f"使用备选选择器找到期号: {self.current_period}")
            
            # 提取期号状态
            period_tip_div = game_info_div.select_one('.c-period .time[data-bind="text:periodTip"]')
            if period_tip_div:
                self.period_status = period_tip_div.text.strip()
                self.logger.info(f"期号状态: {self.period_status}")
            else:
                # 尝试其他选择器
                period_tip_div = game_info_div.select_one('.drawNumber .c-period .time')
                if period_tip_div:
                    self.period_status = period_tip_div.text.strip()
                    self.logger.info(f"使用备选选择器找到期号状态: {self.period_status}")
            
            # 提取剩余时间
            # 尝试从 JavaScript 中提取
            remaining_time_match = re.search(r'var leftTime = (\d+);', response.text)
            if remaining_time_match:
                self.remaining_time = int(remaining_time_match.group(1))
                self.remaining_time_str = self.format_remaining_time(self.remaining_time)
                self.logger.info(f"\u5269\u4f59\u65f6\u95f4: {self.remaining_time_str} ({self.remaining_time}\u79d2)")
            else:
                # \u5982\u679c\u4ece JavaScript \u4e2d\u65e0\u6cd5\u63d0\u53d6\uff0c\u5c1d\u8bd5\u4ece\u663e\u793a\u7684\u5012\u8ba1\u65f6\u4e2d\u63d0\u53d6
                # \u8fd9\u91cc\u53ea\u662f\u4e00\u4e2a\u793a\u4f8b\uff0c\u5b9e\u9645\u4e0a\u53ef\u80fd\u9700\u8981\u66f4\u590d\u6742\u7684\u903b\u8f91
                resttime_span = game_info_div.select_one('#RestTime')
                if resttime_span:
                    # \u5c1d\u8bd5\u4ece\u5012\u8ba1\u65f6\u7684\u663e\u793a\u4e2d\u63d0\u53d6\u6570\u5b57
                    seconds_match = re.findall(r'<div class="inn">(\d+)</div>', str(resttime_span))
                    if len(seconds_match) >= 6:  # \u5e0c\u671b\u81f3\u5c11\u6709 6 \u4e2a\u6570\u5b57\uff08\u5c0f\u65f6\u3001\u5206\u949f\u3001\u79d2\u6570\uff09
                        hours = int(seconds_match[0] + seconds_match[1])
                        minutes = int(seconds_match[2] + seconds_match[3])
                        seconds = int(seconds_match[4] + seconds_match[5])
                        self.remaining_time = hours * 3600 + minutes * 60 + seconds
                        self.remaining_time_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
                        self.logger.info(f"\u4ece\u663e\u793a\u7684\u5012\u8ba1\u65f6\u4e2d\u63d0\u53d6\u5269\u4f59\u65f6\u95f4: {self.remaining_time_str}")
            
            self.logger.info(f"\u6210\u529f\u83b7\u53d6\u6e38\u620f\u4fe1\u606f: {self.game_name}, \u671f\u53f7: {self.current_period}, \u72b6\u6001: {self.period_status}")
            return True
            
        except Exception as e:
            self.logger.error(f"\u83b7\u53d6\u6e38\u620f\u4fe1\u606f\u5931\u8d25: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return False
    
    def format_remaining_time(self, seconds):
        """将秒数格式化为时:分:秒格式"""
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    
    def start_auto_update(self):
        """开始自动更新游戏信息"""
        if self.is_updating:
            return
        
        self.is_updating = True
        self.update_thread = threading.Thread(target=self._update_task)
        self.update_thread.daemon = True
        self.update_thread.start()
    
    def stop_auto_update(self):
        """停止自动更新"""
        self.is_updating = False
        if self.update_thread:
            self.update_thread.join(timeout=1.0)
            self.update_thread = None
    
    def _update_task(self):
        """更新任务"""
        while self.is_updating:
            success = self.fetch_game_info()
            if success:
                # 通知所有回调
                for callback in self.on_update_callbacks:
                    try:
                        callback(self)
                    except Exception as e:
                        self.logger.error(f"执行回调时出错: {str(e)}")
            
            # 倒计时更新
            local_remaining = self.remaining_time
            for _ in range(self.update_interval):
                if not self.is_updating:
                    break
                if local_remaining > 0:
                    local_remaining -= 1
                    self.remaining_time = local_remaining
                    self.remaining_time_str = self.format_remaining_time(local_remaining)
                    # 通知倒计时更新
                    for callback in self.on_update_callbacks:
                        try:
                            callback(self, countdown_only=True)
                        except Exception as e:
                            self.logger.error(f"执行倒计时回调时出错: {str(e)}")
                time.sleep(1)
    
    def register_update_callback(self, callback):
        """注册更新回调函数"""
        if callback not in self.on_update_callbacks:
            self.on_update_callbacks.append(callback)
    
    def unregister_update_callback(self, callback):
        """取消注册更新回调函数"""
        if callback in self.on_update_callbacks:
            self.on_update_callbacks.remove(callback)
    
    def get_game_info_dict(self):
        """获取游戏信息字典，方便其他模块使用"""
        return {
            'game_code': self.game_code,
            'game_name': self.game_name,
            'game_icon': self.game_icon,
            'official_site': self.official_site,
            'current_period': self.current_period,
            'period_status': self.period_status,
            'remaining_time': self.remaining_time,
            'remaining_time_str': self.remaining_time_str
        }
