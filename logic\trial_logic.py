#!/usr/bin/env python
# -*- coding: utf-8 -*-

import threading
import time
import datetime
import re
from typing import Dict, List, Callable, Optional, Any, Tuple

from core.api import ApiClient
from core.game_info import GameInfo
from utils.logger import Logger

class TrialLogic:
    """
    试玩选项卡逻辑类，负责处理试玩相关的业务逻辑和API交互
    """
    
    def __init__(self, session, logger: Logger):
        """
        初始化试玩逻辑
        
        Args:
            session: 会话对象
            logger: 日志记录器
        """
        self.session = session
        self.logger = logger
        self.api_client = ApiClient(session, logger)
        self.game_info = GameInfo(session)
        
        # 账号信息
        self.account = ""
        self.balance = "0"
        self.has_loaded_trial = False
        
        # 投注信息
        self.positions = ["冠军", "亚军", "季军", "第四", "第五", "第六", "第七", "第八", "第九", "第十"]
        
        # 每个位置的选中号码和投注金额
        self.position_data = {}
        for position in self.positions:
            self.position_data[position] = {
                "selected_numbers": [],
                "amount": "1"
            }
        
        # 投注单位和倍数
        self.bet_unit = "1"  # 元
        self.bet_unit_text = "元"
        self.multiple = "1"
        
        # 自动刷新相关
        self.auto_refresh = False
        self.refresh_interval = 30  # 秒
        self.refresh_thread = None
        self.refresh_thread_running = False
        
        # 回调函数
        self.on_account_updated = None
        self.on_game_info_updated = None
        self.on_bet_result = None
    
    def register_callbacks(self, on_account_updated=None, on_game_info_updated=None, on_bet_result=None):
        """
        注册回调函数
        
        Args:
            on_account_updated: 账号信息更新回调
            on_game_info_updated: 游戏信息更新回调
            on_bet_result: 投注结果回调
        """
        self.on_account_updated = on_account_updated
        self.on_game_info_updated = on_game_info_updated
        self.on_bet_result = on_bet_result
    
    def load_trial_account(self) -> bool:
        """
        加载试玩账号信息
        
        Returns:
            bool: 是否成功加载试玩账号
        """
        try:
            self.logger.info("正在获取试玩账号...")
            
            # 使用API客户端获取试玩页面
            success, trial_info = self.api_client.get_trial_account()
            
            if success and trial_info:
                self.account = trial_info.get('account', '未知账号')
                self.balance = trial_info.get('balance', '0')
                self.has_loaded_trial = True
                
                self.logger.info(f"成功获取试玩账号: {self.account}, 余额: {self.balance}")
                
                # 调用回调函数
                if self.on_account_updated:
                    self.on_account_updated(self.account, self.balance)
                
                return True
            else:
                self.logger.error("获取试玩账号失败")
                return False
        
        except Exception as e:
            self.logger.error(f"加载试玩账号时出错: {str(e)}")
            return False
    
    def refresh_balance(self) -> bool:
        """
        刷新账号余额
        
        Returns:
            bool: 是否成功刷新余额
        """
        try:
            if not self.has_loaded_trial:
                self.logger.warning("请先获取试玩账号")
                return False
            
            self.logger.info("正在刷新余额...")
            
            # 使用API客户端获取试玩页面
            success, trial_info = self.api_client.get_trial_account()
            
            if success and trial_info:
                self.balance = trial_info.get('balance', self.balance)
                
                self.logger.info(f"余额刷新成功: {self.balance}")
                
                # 调用回调函数
                if self.on_account_updated:
                    self.on_account_updated(self.account, self.balance)
                
                return True
            else:
                self.logger.error("余额刷新失败")
                return False
        
        except Exception as e:
            self.logger.error(f"刷新余额时出错: {str(e)}")
            return False
    
    def toggle_auto_refresh(self) -> bool:
        """
        切换自动刷新状态
        
        Returns:
            bool: 切换后的自动刷新状态
        """
        self.auto_refresh = not self.auto_refresh
        
        if self.auto_refresh:
            self.start_auto_refresh()
            self.logger.info(f"已开启自动刷新 (间隔: {self.refresh_interval}秒)")
        else:
            self.stop_auto_refresh()
            self.logger.info("已关闭自动刷新")
        
        return self.auto_refresh
    
    def start_auto_refresh(self):
        """启动自动刷新线程"""
        if self.refresh_thread and self.refresh_thread.is_alive():
            return
        
        self.refresh_thread_running = True
        self.refresh_thread = threading.Thread(target=self._refresh_task)
        self.refresh_thread.daemon = True
        self.refresh_thread.start()
    
    def stop_auto_refresh(self):
        """停止自动刷新线程"""
        self.refresh_thread_running = False
        if self.refresh_thread and self.refresh_thread.is_alive():
            self.refresh_thread.join(1.0)  # 等待线程结束，最多等待1秒
    
    def _refresh_task(self):
        """自动刷新任务"""
        while self.refresh_thread_running:
            if self.has_loaded_trial:
                self.refresh_balance()
            
            # 等待指定间隔时间
            for _ in range(self.refresh_interval):
                if not self.refresh_thread_running:
                    break
                time.sleep(1)
    
    def go_to_game(self) -> bool:
        """
        进入游戏页面
        
        Returns:
            bool: 是否成功进入游戏页面
        """
        try:
            if not self.has_loaded_trial:
                self.logger.warning("请先获取试玩账号")
                return False
            
            self.logger.info("正在进入游戏页面...")
            
            # 使用API客户端进入游戏页面
            success, _ = self.api_client.get_game_page()
            
            if success:
                self.logger.info("成功进入游戏页面")
                return True
            else:
                self.logger.error("进入游戏页面失败")
                return False
        
        except Exception as e:
            self.logger.error(f"进入游戏页面时出错: {str(e)}")
            return False
    
    def refresh_game_info(self) -> bool:
        """
        刷新游戏信息（手动刷新）
        
        Returns:
            bool: 是否成功刷新游戏信息
        """
        try:
            if not self.has_loaded_trial:
                self.logger.warning("请先获取试玩账号")
                return False
            
            self.logger.info("正在刷新游戏信息...")
            
            # 使用API更新游戏信息
            success = self.update_game_info_from_api(manual_refresh=True)
            
            if success:
                self.logger.info("游戏信息刷新成功")
                return True
            else:
                self.logger.error("游戏信息刷新失败")
                return False
        
        except Exception as e:
            self.logger.error(f"刷新游戏信息时出错: {str(e)}")
            return False
    
    def update_game_info_from_api(self, manual_refresh=False) -> bool:
        """
        使用API接口数据更新游戏信息
        
        Args:
            manual_refresh: 是否是手动刷新，如果是手动刷新则更新游戏名称
        
        Returns:
            bool: 是否成功更新游戏信息
        """
        try:
            # 使用API客户端获取游戏信息
            success, game_data = self.api_client.get_game_info()
            
            if success and game_data:
                # 更新游戏信息
                self.game_info.update_from_api_data(game_data, manual_refresh)
                
                # 调用回调函数
                if self.on_game_info_updated:
                    game_info_dict = {
                        'game_name': self.game_info.game_name,
                        'current_period': self.game_info.current_period,
                        'period_status': self.game_info.get_status_text(),
                        'remaining_time_str': self.game_info.remaining_time_str,
                        'previous_period': self.game_info.previous_period,
                        'previous_result': self.game_info.previous_result
                    }
                    self.on_game_info_updated(game_info_dict)
                
                return True
            else:
                self.logger.error("从API获取游戏信息失败")
                return False
        
        except Exception as e:
            self.logger.error(f"从API更新游戏信息时出错: {str(e)}")
            return False
    
    def start_game_info_auto_update(self, interval=5):
        """
        启动游戏信息自动更新
        
        Args:
            interval: 更新间隔（秒）
        """
        # 调用 start_auto_update 时不传递参数，因为该方法不接受参数
        self.game_info.start_auto_update()
    
    def stop_game_info_auto_update(self):
        """停止游戏信息自动更新"""
        self.game_info.stop_auto_update()
    
    def select_position(self, position: str):
        """
        选择位置
        
        Args:
            position: 位置名称
        """
        try:
            self.logger.info(f"选择位置: {position}")
            # 这里可以添加其他逻辑，如记录当前选中的位置等
            self.current_position = position
        except Exception as e:
            self.logger.error(f"选择位置时出错: {str(e)}")
    
    def toggle_number(self, position: str, number: int) -> bool:
        """
        切换号码选中状态
        
        Args:
            position: 位置名称
            number: 号码
        
        Returns:
            bool: 切换后的选中状态
        """
        if position not in self.position_data:
            return False
        
        selected_numbers = self.position_data[position]["selected_numbers"]
        
        if number in selected_numbers:
            selected_numbers.remove(number)
            is_selected = False
        else:
            selected_numbers.append(number)
            is_selected = True
        
        return is_selected
    
    def select_all_numbers(self, position: str) -> List[int]:
        """
        全选号码
        
        Args:
            position: 位置名称
        
        Returns:
            List[int]: 选中的号码列表
        """
        if position not in self.position_data:
            return []
        
        # 全选1-10的号码
        self.position_data[position]["selected_numbers"] = list(range(1, 11))
        
        return self.position_data[position]["selected_numbers"]
    
    def clear_numbers(self, position: Optional[str] = None) -> None:
        """
        清除选中的号码
        
        Args:
            position: 指定要清除的位置，如果为None则清除所有位置
        """
        if position is None:
            # 清除所有位置的选中号码
            for pos in self.positions:
                self.position_data[pos]["selected_numbers"] = []
        elif position in self.position_data:
            # 清除指定位置的选中号码
            self.position_data[position]["selected_numbers"] = []
    
    def set_position_amount(self, position: str, amount: str) -> None:
        """
        设置指定位置的投注金额
        
        Args:
            position: 位置名称
            amount: 投注金额
        """
        if position in self.position_data:
            try:
                # 确保金额是有效的数字
                float_amount = float(amount)
                if float_amount <= 0:
                    amount = "1"
            except ValueError:
                amount = "1"
            
            self.position_data[position]["amount"] = amount
    
    def set_bet_unit(self, unit: str, text: str) -> None:
        """
        设置投注单位（元/角/分）
        
        Args:
            unit: 单位值
            text: 单位文本
        """
        self.bet_unit = unit
        self.bet_unit_text = text
    
    def set_multiple(self, multiple: str) -> None:
        """
        设置倍数
        
        Args:
            multiple: 倍数
        """
        try:
            # 确保倍数是有效的整数
            int_multiple = int(multiple)
            if int_multiple <= 0:
                multiple = "1"
        except ValueError:
            multiple = "1"
        
        self.multiple = multiple
    
    def calculate_total_amount(self) -> float:
        """
        计算总投注金额
        
        Returns:
            float: 总投注金额
        """
        total = 0.0
        base_amount = 2.0  # 每注基础金额为2元
        
        try:
            multiple = int(self.multiple)
            unit = float(self.bet_unit)
            
            for position in self.positions:
                selected_count = len(self.position_data[position]["selected_numbers"])
                if selected_count > 0:
                    try:
                        position_amount = float(self.position_data[position]["amount"])
                    except ValueError:
                        position_amount = 1.0
                    
                    # 计算该位置的投注金额
                    position_total = base_amount * multiple * unit * selected_count * position_amount
                    total += position_total
            
            return total
        except Exception as e:
            self.logger.error(f"计算总金额时出错: {str(e)}")
            return 0.0
    
    def load_order_history(self) -> List[Dict[str, str]]:
        """
        加载投注记录数据
        
        Returns:
            List[Dict[str, str]]: 投注记录列表
        """
        try:
            if not self.has_loaded_trial:
                self.logger.warning("请先获取试玩账号")
                return []
            
            self.logger.info("正在加载投注记录...")
            
            # 使用API客户端获取投注记录
            success, history_data = self.api_client.get_bet_history()
            
            if success and history_data:
                self.logger.info(f"成功加载投注记录，共 {len(history_data)} 条")
                return history_data
            else:
                self.logger.error("加载投注记录失败")
                return []
        
        except Exception as e:
            self.logger.error(f"加载投注记录时出错: {str(e)}")
            return []
    
    def place_bet(self) -> Tuple[bool, str]:
        """
        发送投注请求
        
        Returns:
            Tuple[bool, str]: (是否成功, 结果消息)
        """
        try:
            if not self.has_loaded_trial:
                return False, "请先获取试玩账号"
            
            # 检查是否有选中的号码
            total_selected = 0
            bet_data = []
            
            for position in self.positions:
                selected_numbers = self.position_data[position]["selected_numbers"]
                if selected_numbers:
                    total_selected += len(selected_numbers)
                    
                    # 格式化号码为两位数字
                    formatted_numbers = [f"{num:02d}" for num in selected_numbers]
                    
                    # 添加到投注数据
                    bet_data.append({
                        "position": position,
                        "numbers": formatted_numbers,
                        "amount": self.position_data[position]["amount"]
                    })
            
            if total_selected == 0:
                return False, "请先选择号码"
            
            # 获取当前期号
            current_period = self.game_info.current_period
            if not current_period:
                return False, "无法获取当前期号"
            
            # 检查期号状态
            if self.game_info.period_status == 2:
                return False, "当前期已封盘，请等待下一期"
            
            # 计算总金额
            total_amount = self.calculate_total_amount()
            
            # 检查余额
            try:
                if float(self.balance) < total_amount:
                    return False, f"余额不足，需要 {total_amount:.2f} 元，当前余额 {self.balance} 元"
            except ValueError:
                pass  # 忽略余额格式错误
            
            self.logger.info(f"正在投注，期号: {current_period}, 总金额: {total_amount:.2f} 元")
            
            # 使用API客户端发送投注请求
            success, result = self.api_client.place_bet(
                period=current_period,
                bet_data=bet_data,
                multiple=self.multiple,
                unit=self.bet_unit
            )
            
            if success:
                message = f"投注成功，期号: {current_period}, 总金额: {total_amount:.2f} 元"
                self.logger.info(message)
                
                # 刷新余额
                self.refresh_balance()
                
                # 调用回调函数
                if self.on_bet_result:
                    self.on_bet_result(True, message, result)
                
                return True, message
            else:
                error_msg = result.get("message", "投注失败，未知错误")
                self.logger.error(f"投注失败: {error_msg}")
                
                # 调用回调函数
                if self.on_bet_result:
                    self.on_bet_result(False, error_msg, result)
                
                return False, error_msg
        
        except Exception as e:
            error_msg = f"投注过程中出错: {str(e)}"
            self.logger.error(error_msg)
            
            # 调用回调函数
            if self.on_bet_result:
                self.on_bet_result(False, error_msg, {})
            
            return False, error_msg
    
    def cleanup(self):
        """清理资源，在应用程序关闭时调用"""
        self.stop_auto_refresh()
        self.stop_game_info_auto_update()
        self.logger.info("试玩逻辑资源已清理")
