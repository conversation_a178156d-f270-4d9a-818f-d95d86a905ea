#!/usr/bin/env python
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, messagebox
from PIL import Image, ImageTk
import io
import time
import re
from bs4 import BeautifulSoup
import requests
import urllib3

# 禁用未验证HTTPS请求的警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

from utils.logger import Logger
from core.session import SessionManager

# 获取全局配置
from core.session import SessionConfig
config = SessionConfig()
BASE_URL = config.BASE_URL
BASE_DOMAIN = config.BASE_DOMAIN

class LoginTab:
    def __init__(self, parent, notebook, app):
        """初始化登录选项卡"""
        self.parent = parent
        self.notebook = notebook
        self.app = app  # 主应用程序的引用
        self.logger = Logger("LoginTab")
        
        # 创建登录选项卡
        self.tab = ttk.Frame(notebook)
        notebook.add(self.tab, text="登录")
        
        # 创建登录表单
        self.create_login_form()
        
        # 验证码URL
        self.verify_url = f"{BASE_URL}/loginVerifycode.do"
        self.refresh_verification_code()
    
    def create_login_form(self):
        """创建登录表单"""
        try:
            # 创建登录界面
            self.login_frame = tk.Frame(self.tab)
            self.login_frame.pack(pady=20)
            
            # 创建输入框
            tk.Label(self.login_frame, text="账号:").pack()
            self.username_entry = tk.Entry(self.login_frame)
            self.username_entry.pack(pady=5)
            
            tk.Label(self.login_frame, text="密码:").pack()
            self.password_entry = tk.Entry(self.login_frame, show="*")
            self.password_entry.pack(pady=5)
            
            tk.Label(self.login_frame, text="验证码:").pack()
            self.verify_code = tk.Entry(self.login_frame)
            self.verify_code.pack(pady=5)

            # 功能按钮
            self.refresh_btn = tk.Button(self.login_frame, text="刷新验证码", command=self.refresh_verification_code)
            self.refresh_btn.pack(pady=5)
            
            self.login_btn = tk.Button(self.login_frame, text="立即登录", command=self.login)
            self.login_btn.pack(pady=10)
            
            # 验证码显示
            self.verify_image_label = tk.Label(self.login_frame)
            self.verify_image_label.pack()
            
            # 创建安全问题框架（初始时隐藏）
            self.security_frame = tk.Frame(self.tab)
            
            # 安全问题标签
            tk.Label(self.security_frame, text="安全问题验证", font=("Arial", 12, "bold")).pack(pady=10)
            
            # 问题显示
            self.question_label = tk.Label(self.security_frame, text="请回答您的安全问题")
            self.question_label.pack(pady=5)
            
            # 回答输入框
            self.answer_entry = tk.Entry(self.security_frame, width=30)
            self.answer_entry.pack(pady=5)
            
            # 提交按钮
            self.submit_answer_btn = tk.Button(self.security_frame, text="提交回答", command=self.verify_security_answer)
            self.submit_answer_btn.pack(pady=10)
            
            # 返回登录按钮
            self.back_to_login_btn = tk.Button(self.security_frame, text="返回登录", command=self.show_login_frame)
            self.back_to_login_btn.pack(pady=5)
            
            # 初始化时隐藏安全问题框架
            self.security_frame.pack_forget()
            
            # 存储当前账号ID（用于安全问题验证）
            self.account_id = None
            
            self.logger.info("登录表单初始化完成")
        except Exception as e:
            self.logger.error(f"创建登录表单时出错: {str(e)}")
    
    def refresh_verification_code(self):
        """刷新验证码"""
        try:
            # 添加时间戳避免缓存
            url = f"{self.verify_url}?t={int(time.time() * 1000)}"
            
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Referer": f"{BASE_URL}/index.do"
            }
            
            # 发送请求获取验证码图片
            response = self.app.session.get(url, headers=headers, verify=False)
            
            if response.status_code == 200:
                # 将图片数据转换为PIL图像对象
                img = Image.open(io.BytesIO(response.content))
                
                # 调整图片大小
                img = img.resize((150, 50), Image.LANCZOS)
                
                # 转换为Tkinter可用的图像对象
                self.verify_image = ImageTk.PhotoImage(img)
                
                # 在标签中显示图像
                self.verify_image_label.config(image=self.verify_image)
                self.verify_image_label.image = self.verify_image  # 保持引用
            else:
                self.logger.warning(f"获取验证码失败，状态码: {response.status_code}")
        except Exception as e:
            self.logger.error(f"刷新验证码时出错: {str(e)}")
            # 显示错误消息而不是图片
            self.verify_image_label.config(image=None, text="验证码加载失败")
            self.verify_image_label.image = None
    
    def show_login_frame(self):
        """显示登录框架，隐藏安全问题框架"""
        self.security_frame.pack_forget()
        self.login_frame.pack(pady=20)
        self.refresh_verification_code()
    
    def show_security_frame(self, account_id, question=None):
        """显示安全问题框架，隐藏登录框架"""
        self.login_frame.pack_forget()
        self.security_frame.pack(pady=20)
        
        # 存储账号ID
        self.account_id = account_id
        
        # 如果有问题，显示问题
        if question:
            self.question_label.config(text=question)
        else:
            self.question_label.config(text="请回答您的安全问题")
        
        # 清空回答框
        self.answer_entry.delete(0, tk.END)
        
        # 设置焦点
        self.answer_entry.focus()
    
    def verify_security_answer(self):
        """验证安全问题答案"""
        try:
            # 获取答案
            answer = self.answer_entry.get().strip()
            
            # 检查答案是否为空
            if not answer:
                messagebox.showwarning("提示", "请输入安全问题答案")
                return
                
            # 检查账号ID是否存在
            if not self.account_id:
                messagebox.showerror("错误", "账号ID不存在，请重新登录")
                self.show_login_frame()
                return
                
            # 构建请求数据
            verify_data = {
                "accountId": self.account_id,
                "answer": answer,
                "timestamp": str(int(time.time() * 1000))
            }
            
            # 发送请求
            verify_url = f"{BASE_URL}/verifySecurityQuestion.do"
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Referer": f"{BASE_URL}/index.do",
                "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
                "X-Requested-With": "XMLHttpRequest"
            }
            
            self.logger.debug(f"发送安全问题验证请求: {verify_data}")
            response = self.app.session.post(verify_url, data=verify_data, headers=headers, verify=False)
            
            # 检查响应
            self.logger.debug(f"安全问题验证响应状态码: {response.status_code}")
            self.logger.debug(f"安全问题验证响应内容: {response.text}")
            
            # 解析JSON响应
            try:
                result = response.json()
                if result.get("success"):
                    # 验证成功，继续登录流程
                    self.logger.info("安全问题验证成功")
                    messagebox.showinfo("成功", "安全问题验证成功，正在登录...")
                    
                    # 完成登录流程
                    self.complete_login()
                else:
                    # 验证失败
                    error_msg = result.get("msg", "安全问题验证失败")
                    self.logger.warning(f"安全问题验证失败: {error_msg}")
                    messagebox.showerror("错误", error_msg)
            except ValueError:
                self.logger.error(f"解析安全问题验证响应失败: {response.text}")
                messagebox.showerror("错误", "安全问题验证响应解析失败")
        except Exception as e:
            self.logger.error(f"安全问题验证过程中出错: {str(e)}")
            messagebox.showerror("错误", f"安全问题验证过程中出错: {str(e)}")
    
    def complete_login(self):
        """完成登录流程，获取用户信息并更新界面"""
        try:
            # 访问首页获取用户信息
            index_url = f"{BASE_URL}/index.do"
            index_response = self.app.session.get(index_url, verify=False)
            
            if index_response.status_code == 200:
                # 解析首页内容
                index_soup = BeautifulSoup(index_response.text, 'html.parser')
                
                # 查找用户信息元素
                username_elements = index_soup.select('.user-name, .username, .user, #username, .account-name')
                balance_elements = index_soup.select('#lebo_money, .balance, .money')
                
                # 查找用户名
                username = ""
                if username_elements:
                    username = username_elements[0].text.strip()
                    self.logger.info(f"从首页提取到用户名: {username}")
                
                # 查找余额
                balance = "0"
                if balance_elements:
                    balance = balance_elements[0].text.strip()
                    self.logger.info(f"从首页提取到余额: {balance}")
                
                # 使用正则表达式尝试提取更多信息
                if not username or not balance:
                    html_str = index_response.text
                    # 尝试找到账号
                    account_match = re.search(r'账\s*号[\uff1a:]\s*<span[^>]*>([^<]+)</span>', html_str)
                    if account_match:
                        username = account_match.group(1).strip()
                        self.logger.info(f"使用正则表达式找到用户名: {username}")
                    
                    # 尝试找到余额
                    balance_match = re.search(r'系统余额[\uff1a:]\s*<span[^>]*>([^<]+)</span>', html_str)
                    if balance_match:
                        balance = balance_match.group(1).strip()
                        self.logger.info(f"使用正则表达式找到余额: {balance}")
            
                # 如果仍然没有提取到用户名，使用登录名
                if not username:
                    username = self.username_entry.get()
                
                # 更新应用程序的用户信息
                self.app.username = username
                self.app.balance = balance
                self.app.is_logged_in = True
                
                # 保存会话信息
                self.app.session_manager.username = username
                self.app.session_manager.balance = balance
                self.app.session_manager.is_logged_in = True
                self.app.session_manager.save_session()
                
                # 更新用户信息显示
                self.app.update_user_info_display()
                
                # 切换到用户信息选项卡
                self.logger.info("切换到用户信息选项卡")
                self.notebook.select(self.app.user_info_tab.tab)
                
                # 启用退出按钮
                if hasattr(self.app.user_info_tab, 'logout_btn'):
                    self.app.user_info_tab.logout_btn.config(state=tk.NORMAL)
                
                # 显示成功消息
                messagebox.showinfo("登录成功", f"欢迎 {username}，登录成功！")
                
                # 重置登录界面
                self.show_login_frame()
            else:
                self.logger.warning(f"获取首页失败，状态码: {index_response.status_code}")
                messagebox.showinfo("登录成功", "登录成功，但获取用户信息失败")
        except Exception as e:
            self.logger.error(f"完成登录过程中出错: {str(e)}")
            messagebox.showinfo("登录成功", "登录成功，但获取用户信息失败")
    
    def login(self):
        """登录功能"""
        try:
            # 字段获取
            username = self.username_entry.get()
            password = self.password_entry.get()
            verify_code = self.verify_code.get()
            
            # 验证字段
            if not all([username, password, verify_code]):
                messagebox.showwarning("提示", "所有字段均为必填项")
                return

            # 构造请求
            login_url = f"{BASE_URL}/login.do"
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Referer": f"{BASE_URL}/index.do",
                "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
                "X-Requested-With": "XMLHttpRequest"
            }
            
            # 请求参数
            form_data = {
                "username": username,
                "password": password,
                "verifyCode": verify_code,
                "gToken": "",  # 添加gToken参数
                "timestamp": str(int(time.time() * 1000))
            }
            
            self.logger.debug(f"发送登录请求数据: {form_data}")
            
            # 打印请求URL和头信息
            self.logger.debug(f"登录URL: {login_url}")
            self.logger.debug(f"请求头信息: {headers}")
            
            # 发送登录请求
            response = self.app.session.post(login_url, data=form_data, headers=headers, verify=False)
            
            # 记录原始响应以便调试
            self.logger.debug(f"响应状态码: {response.status_code}")
            self.logger.debug(f"响应头信息: {dict(response.headers)}")
            self.logger.debug(f"原始响应内容: {response.text}")
            
            # 尝试解析JSON响应
            login_success = False
            try:
                # 判断是否是JSON响应
                content_type = response.headers.get('Content-Type', '')
                if 'application/json' in content_type:
                    # 解析JSON响应
                    json_data = response.json()
                    self.logger.debug(f"解析的JSON响应: {json_data}")
                    
                    # 判断登录结果
                    if 'status' in json_data:
                        # 检查状态码
                        status_code = json_data.get('status') or json_data.get('code')
                        if status_code == 0 or status_code == '0' or status_code == 200:
                            self.logger.info("登录成功: 服务器返回成功状态码")
                            login_success = True
                            message = json_data.get('message', '登录成功')
                        else:
                            self.logger.warning(f"登录失败: 服务器返回错误状态码 {status_code}")
                            error_msg = json_data.get('message') or json_data.get('msg') or '登录失败'
                            messagebox.showerror("登录失败", error_msg)
                            self.refresh_verification_code()
                            return
                    elif 'success' in json_data:
                        # 检查success字段
                        if json_data['success'] == True or json_data['success'] == 'true' or json_data['success'] == 1:
                            self.logger.info("登录成功: 服务器返回success=true")
                            login_success = True
                            message = json_data.get('message', '登录成功')
                        else:
                            self.logger.warning("登录失败: 服务器返回success=false")
                            error_msg = json_data.get('message') or json_data.get('msg') or '登录失败'
                            messagebox.showerror("登录失败", error_msg)
                            self.refresh_verification_code()
                            return
                    else:
                        # 如果没有标准的状态字段，尝试其他方式判断
                        login_success = True  # 默认认为成功，后面会通过获取首页进一步验证
                        self.logger.info("未找到标准状态字段，将尝试通过获取首页验证登录状态")
                else:
                    # 处理HTML响应
                    self.logger.info("响应不是JSON格式，将尝试解析HTML")
                    soup = BeautifulSoup(response.text, 'html.parser')
                    page_text = soup.get_text()
                    
                    # 检查是否有登录成功的关键词
                    success_text = ['会员中心', '系统余额', '账 号', '退出账户']
                    success_indicators = [text for text in success_text if text in page_text]
                    if success_indicators:
                        self.logger.info(f"找到成功指示词: {success_indicators}")
                        login_success = True
            except Exception as e:
                # 捕获解析错误
                self.logger.error(f"解析响应时出错: {str(e)}")
                messagebox.showerror("登录失败", f"处理登录响应时出错: {str(e)}")
                self.refresh_verification_code()
                return
                
            # 如果登录成功，尝试获取用户信息
            if login_success:
                # 检查是否需要验证安全问题
                try:
                    if isinstance(json_data, dict) and json_data.get("code") == "666" and json_data.get("accountId"):
                        # 需要验证安全问题
                        account_id = json_data.get("accountId")
                        question = json_data.get("msg", "请回答您的安全问题")
                        self.logger.info(f"需要验证安全问题，账号ID: {account_id}, 问题: {question}")
                        
                        # 显示安全问题验证界面
                        self.show_security_frame(account_id, question)
                        return
                    else:
                        # 登录成功，完成登录流程
                        self.complete_login()
                except Exception as e:
                    self.logger.error(f"检查安全问题时出错: {str(e)}")
                    # 如果出错，尝试正常登录流程
                    self.complete_login()
        except Exception as e:
            self.logger.error(f"登录过程中出错: {str(e)}")
            messagebox.showerror("错误", f"登录过程中发生错误：{str(e)}")
            self.refresh_verification_code()
