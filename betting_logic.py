#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
投注逻辑模块
处理投注的业务逻辑和状态管理
"""

import json
import time
import threading
from urllib.parse import urljoin
import requests
from utils.logger import Logger
from core.session import SessionConfig

# 从SessionConfig获取BASE_URL
BASE_URL = SessionConfig.BASE_URL

class BettingLogic:
    def __init__(self, session, game_info):
        """初始化投注逻辑
        
        Args:
            session: 会话对象，用于发送请求
            game_info: 游戏信息对象
        """
        # 创建日志记录器
        self.logger = Logger("BettingLogic")
        self.session = session
        self.game_info = game_info
        
        # 投注状态
        self.is_betting = False
        self.bet_result = None
        self.bet_error = None
        
        # 投注记录
        self.bet_records = []
        
        # 回调函数
        self.on_bet_callbacks = []
    
    def place_bet(self, numbers, multiplier, unit, amount):
        """执行投注操作
        
        Args:
            numbers: 选中的号码列表，格式为['01', '02', ...]
            multiplier: 倍数
            unit: 单位（元/角/分）
            amount: 总金额
            
        Returns:
            bool: 投注是否成功
        """
        if self.is_betting:
            self.logger.warning("正在处理上一次投注，请稍后再试")
            return False
        
        self.is_betting = True
        self.bet_result = None
        self.bet_error = None
        
        try:
            # 获取当前期号
            current_period = self.game_info.current_period
            if not current_period:
                self.logger.error("无法获取当前期号，无法投注")
                self.bet_error = "无法获取当前期号"
                self.is_betting = False
                return False
            
            # 检查期号状态
            if self.game_info.period_status != "投注中":
                self.logger.warning(f"当前期号状态不允许投注: {self.game_info.period_status}")
                self.bet_error = f"当前期号状态不允许投注: {self.game_info.period_status}"
                self.is_betting = False
                return False
            
            # 构建投注参数
            unit_value = 1.0 if unit == "元" else 0.1 if unit == "角" else 0.01
            
            bet_params = {
                'code': self.game_info.game_code,
                'gameType': '5',  # 游戏类型，可能需要根据实际情况调整
                'currIssue': current_period,
                'amount': amount,
                'betMultiple': multiplier,
                'betUnit': unit_value,
                'betContent': ','.join(numbers),
                'betCount': len(numbers),
                'source': 'PC',
                'terminal': 'PC'
            }
            
            self.logger.info(f"投注参数: {json.dumps(bet_params)}")
            
            # 发送投注请求
            bet_url = urljoin(BASE_URL, "/bet/submit.do")
            response = self.session.post(bet_url, data=bet_params, verify=False)
            response.raise_for_status()
            
            # 解析响应
            result = response.json()
            self.logger.info(f"投注响应: {json.dumps(result)}")
            
            # 检查投注结果
            if result.get('success'):
                self.logger.info("投注成功")
                self.bet_result = result
                
                # 添加到投注记录
                bet_record = {
                    'period': current_period,
                    'numbers': numbers,
                    'multiplier': multiplier,
                    'unit': unit,
                    'amount': amount,
                    'time': time.strftime("%Y-%m-%d %H:%M:%S"),
                    'status': '已投注',
                    'result': None  # 开奖结果，暂时为空
                }
                self.bet_records.append(bet_record)
                
                # 通知回调
                for callback in self.on_bet_callbacks:
                    try:
                        callback(True, bet_record)
                    except Exception as e:
                        self.logger.error(f"执行投注回调时出错: {str(e)}")
                
                self.is_betting = False
                return True
            else:
                self.logger.warning(f"投注失败: {result.get('msg', '未知错误')}")
                self.bet_error = result.get('msg', '未知错误')
                
                # 通知回调
                for callback in self.on_bet_callbacks:
                    try:
                        callback(False, {'error': self.bet_error})
                    except Exception as e:
                        self.logger.error(f"执行投注回调时出错: {str(e)}")
                
                self.is_betting = False
                return False
                
        except Exception as e:
            self.logger.error(f"投注过程中出错: {str(e)}")
            self.bet_error = str(e)
            
            # 通知回调
            for callback in self.on_bet_callbacks:
                try:
                    callback(False, {'error': self.bet_error})
                except Exception as e:
                    self.logger.error(f"执行投注回调时出错: {str(e)}")
            
            self.is_betting = False
            return False
    
    def get_bet_records(self):
        """获取投注记录
        
        Returns:
            list: 投注记录列表
        """
        return self.bet_records
    
    def update_bet_results(self):
        """更新投注记录的开奖结果"""
        try:
            # 获取最新的开奖结果
            previous_period = self.game_info.previous_period
            previous_result = self.game_info.previous_result
            
            if not previous_period or not previous_result:
                return
            
            # 更新对应期号的投注记录
            for record in self.bet_records:
                if record['period'] == previous_period and record['result'] is None:
                    record['result'] = previous_result
                    record['status'] = '已开奖'
                    
                    # 检查是否中奖
                    winning_numbers = previous_result.split(',')
                    bet_numbers = record['numbers']
                    
                    # 简单判断是否中奖（号码完全匹配）
                    if all(num in winning_numbers for num in bet_numbers):
                        record['status'] = '已中奖'
                    else:
                        record['status'] = '未中奖'
                    
                    self.logger.info(f"更新投注记录开奖结果: 期号={previous_period}, 结果={previous_result}, 状态={record['status']}")
        except Exception as e:
            self.logger.error(f"更新投注记录开奖结果时出错: {str(e)}")
    
    def register_bet_callback(self, callback):
        """注册投注回调函数
        
        Args:
            callback: 回调函数，接收两个参数(success, data)
        """
        if callback not in self.on_bet_callbacks:
            self.on_bet_callbacks.append(callback)
    
    def unregister_bet_callback(self, callback):
        """取消注册投注回调函数
        
        Args:
            callback: 要取消的回调函数
        """
        if callback in self.on_bet_callbacks:
            self.on_bet_callbacks.remove(callback)
    
    def clear_bet_records(self):
        """清空投注记录"""
        self.bet_records = []
        self.logger.info("已清空投注记录")
