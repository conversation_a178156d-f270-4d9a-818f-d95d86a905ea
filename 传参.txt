headers：
Request URL
https://xh7859.dqpqdpv2580601let.com:59789/offcial/doBet.do
Request Method
POST
Status Code
200 OK
Remote Address
127.0.0.1:10809
Referrer Policy
strict-origin-when-cross-origin
cache-control
no-cache
ceipstate
1
content-length
712
content-type
application/json;charset=UTF-8
date
Mon, 02 Jun 2025 16:17:32 GMT
expires
Thu, 01 Jan 1970 00:00:00 GMT
pragma
no-cache
server
nginx/1.8.1
x-cache
BYPASS
:authority
xh7859.dqpqdpv2580601let.com:59789
:method
POST
:path
/offcial/doBet.do
:scheme
https
accept
application/json, text/javascript, */*; q=0.01
accept-encoding
gzip, deflate, br, zstd
accept-language
zh-CN,zh;q=0.9
content-length
161
content-type
application/x-www-form-urlencoded; charset=UTF-8
cookie
SESSIONV=73660c20ebf6dd8a4fee9825b7c7f0e1; SESSION=030a7f03-5405-488b-8855-17db165f27d9
origin
https://xh7859.dqpqdpv2580601let.com:59789
priority
u=1, i
referer
https://xh7859.dqpqdpv2580601let.com:59789/offcial/index.do?code=JSSC168
sec-ch-ua
"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
sec-ch-ua-mobile
?0
sec-ch-ua-platform
"Windows"
sec-fetch-dest
empty
sec-fetch-mode
cors
sec-fetch-site
same-origin
user-agent
Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
x-requested-with
XMLHttpRequest

payload：
code
JSSC168
qiHao
********
orderList
[{"i":"cgj2","c":"04","n":1,"t":1,"k":0,"m":1,"a":2}]

preview：
{msg: "下注成功",…}
insertedOrder
: 
[{accountId: 5069623, buyIp: "************", buyMoney: 2, buyOdds: "19.800000", buyZhuShu: 1,…}]
0
: 
{accountId: 5069623, buyIp: "************", buyMoney: 2, buyOdds: "19.800000", buyZhuShu: 1,…}
accountId
: 
5069623
buyIp
: 
"************"
buyMoney
: 
2
buyOdds
: 
"19.800000"
buyZhuShu
: 
1
canUndo
: 
false
cheat
: 
false
closedTime
: 
"2025-06-03 00:17:48"
createTime
: 
"2025-06-03 00:17:32"
currentRebate
: 
7.5
haoMa
: 
"04"
kickback
: 
0
lotCode
: 
"JSSC168"
lotType
: 
3
lotVersion
: 
1
lunarYear
: 
0
model
: 
1
multiple
: 
1
oddsCode
: 
"cgj2"
orderId
: 
"C25060305736"
periodStatus
: 
0
playCode
: 
"cgj2"
playName
: 
"猜冠军"
proxyRollback
: 
1
qiHao
: 
"********"
rollBackStatus
: 
1
sellingTime
: 
"2025-06-03 00:16:48"
signKey
: 
"83ebd1dbb547382274169adf2a159f64"
stationId
: 
6
status
: 
1
terminalBetType
: 
1
username
: 
"guest10864"
winMoney
: 
0
zhuiHao
: 
1
msg
: 
"下注成功"
success
: 
true

cookie：
SESSION	030a7f03-5405-488b-8855-17db165f27d9	xh7859.dqpqdpv2580601let.com	/	Session	43	✓	✓	Lax			Medium
SESSIONV	73660c20ebf6dd8a4fee9825b7c7f0e1	xh7859.dqpqdpv2580601let.com	/	Session	40						Medium




按照你的建议帮我重构整个项目代码，采用模块化架构
目前看到你已经尝试将 trial_tab.py 拆分为 UI 和逻辑层，这是很好的方向。建议对整个项目采用类似的模式：

UI 层：负责界面绘制和用户交互
逻辑层：处理业务逻辑、API 请求等
数据层：管理数据模型和持久化
工具层：提供通用功能和辅助方法
2. 采用 MVC 或 MVVM 设计模式
将每个模块按照 MVC（模型-视图-控制器）或 MVVM（模型-视图-视图模型）模式重构：

Model：数据模型和业务逻辑
View：纯 UI 界面
Controller/ViewModel：连接 Model 和 View
3. 优化代码结构
减少重复代码：从日志看到多处重复的 API 调用和数据处理
统一错误处理：建立全局异常处理机制
优化线程管理：日志中显示有线程管理问题，如 'function' object has no attribute 'is_alive'
4. 文件组织建议
CopyInsert
xiangmu/
├── core/                  # 核心功能
│   ├── api.py            # API 请求统一管理
│   ├── session.py        # 会话管理
│   └── game_info.py      # 游戏信息模型
├── ui/                    # UI 相关
│   ├── login_ui.py       # 登录界面
│   ├── trial_ui.py       # 试玩界面
│   └── components/       # 可复用组件
├── logic/                 # 业务逻辑
│   ├── login_logic.py    # 登录逻辑
│   ├── trial_logic.py    # 试玩逻辑
│   └── betting_logic.py  # 投注逻辑
├── utils/                 # 工具函数
│   ├── logger.py         # 日志工具
│   └── helpers.py        # 辅助函数
└── main.py               # 主入口
5. 具体优化点
API 请求统一管理：创建专门的 API 客户端类
状态管理优化：使用观察者模式或事件系统管理状态更新
UI 组件复用：将常用 UI 组件（如投注界面）抽象为可复用组件
配置外部化：将 URL、超时时间等配置移至配置文件
日志系统增强：分级日志，便于调试和问题排查
6. 技术改进
引入单元测试：为核心功能编写测试用例
类型提示：添加类型注解，提高代码可读性和可维护性
文档完善：为关键类和方法添加详细文档
7. 重构步骤建议
先建立新的目录结构
逐个模块重构，保持功能不变
编写测试确保重构后功能正常
优化性能和用户体验